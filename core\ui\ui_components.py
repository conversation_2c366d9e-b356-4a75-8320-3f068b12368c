"""
Comprehensive UI Components for Adventure Chess Creator

This consolidated module provides comprehensive UI components including:
- Enhanced search components with filters and suggestions
- Advanced validation rules and real-time validation
- Search results display and file browsing
- Validation feedback and error handling
- Performance analytics and index management

Key Features:
- Real-time search suggestions and filtering
- Advanced validation rules with custom validators
- Rich search results display with previews
- Validation feedback with visual indicators
- Performance analytics and monitoring
- Index management and optimization tools

Consolidates functionality from:
- enhanced_search_components.py (search UI components)
- enhanced_validation_rules.py (validation system)
"""

import logging
from typing import List, Optional

from PyQt6.QtCore import Qt, QThread, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QVBoxLayout,
    QWidget,
)

from ..performance import SearchResult, get_file_system_optimizer

logger = logging.getLogger(__name__)


class SearchWorker(QThread):
    """Background worker for search operations"""

    search_completed = pyqtSignal(list)  # List[SearchResult]
    search_error = pyqtSignal(str)

    def __init__(
        self, query: str, file_type: Optional[str] = None, max_results: int = 50
    ):
        super().__init__()
        self.query = query
        self.file_type = file_type
        self.max_results = max_results

    def run(self):
        """Execute search in background thread"""
        try:
            optimizer = get_file_system_optimizer()
            results = optimizer.search_files(
                query=self.query, file_type=self.file_type, max_results=self.max_results
            )
            self.search_completed.emit(results)
        except Exception as e:
            logger.error(f"Search error: {e}")
            self.search_error.emit(str(e))


class EnhancedSearchWidget(QWidget):
    """Enhanced search widget with filters and suggestions"""

    search_requested = pyqtSignal(str, str)  # query, file_type
    file_selected = pyqtSignal(str)  # file_path

    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_worker = None
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()

        # Search input section
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search files...")
        search_layout.addWidget(self.search_input)

        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(
            ["All Files", "Pieces", "Abilities", "Boards", "Games"]
        )
        search_layout.addWidget(self.file_type_combo)

        self.search_button = QPushButton("Search")
        search_layout.addWidget(self.search_button)

        layout.addLayout(search_layout)

        # Search options
        options_layout = QHBoxLayout()

        self.case_sensitive_cb = QCheckBox("Case sensitive")
        options_layout.addWidget(self.case_sensitive_cb)

        self.whole_words_cb = QCheckBox("Whole words only")
        options_layout.addWidget(self.whole_words_cb)

        self.regex_cb = QCheckBox("Regular expressions")
        options_layout.addWidget(self.regex_cb)

        options_layout.addStretch()
        layout.addLayout(options_layout)

        # Results section
        self.results_widget = SearchResultsWidget()
        layout.addWidget(self.results_widget)

        self.setLayout(layout)

    def setup_connections(self):
        """Setup signal connections"""
        self.search_button.clicked.connect(self.perform_search)
        self.search_input.returnPressed.connect(self.perform_search)
        self.results_widget.file_selected.connect(self.file_selected.emit)

    def perform_search(self):
        """Perform search operation"""
        query = self.search_input.text().strip()
        if not query:
            return

        file_type = self.file_type_combo.currentText()
        if file_type == "All Files":
            file_type = None

        # Stop any existing search
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.terminate()
            self.search_worker.wait()

        # Start new search
        self.search_worker = SearchWorker(query, file_type)
        self.search_worker.search_completed.connect(self.on_search_completed)
        self.search_worker.search_error.connect(self.on_search_error)
        self.search_worker.start()

        # Update UI
        self.search_button.setEnabled(False)
        self.search_button.setText("Searching...")
        self.results_widget.show_loading()

    @pyqtSlot(list)
    def on_search_completed(self, results: List[SearchResult]):
        """Handle search completion"""
        self.search_button.setEnabled(True)
        self.search_button.setText("Search")
        self.results_widget.display_results(results)

    @pyqtSlot(str)
    def on_search_error(self, error_message: str):
        """Handle search error"""
        self.search_button.setEnabled(True)
        self.search_button.setText("Search")
        self.results_widget.show_error(error_message)


class SearchResultsWidget(QWidget):
    """Widget for displaying search results"""

    file_selected = pyqtSignal(str)  # file_path

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()

        # Results header
        self.header_label = QLabel("Search Results")
        self.header_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(self.header_label)

        # Results list
        self.results_list = QListWidget()
        self.results_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        layout.addWidget(self.results_list)

        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def display_results(self, results: List[SearchResult]):
        """Display search results"""
        self.results_list.clear()

        if not results:
            self.header_label.setText("No Results Found")
            self.status_label.setText("No files matched your search criteria.")
            return

        self.header_label.setText(f"Search Results ({len(results)} found)")
        self.status_label.setText("")

        for result in results:
            item = QListWidgetItem()
            item.setText(f"{result.filename} - {result.file_type}")
            item.setData(Qt.ItemDataRole.UserRole, result.file_path)

            # Add tooltip with preview
            if result.preview:
                item.setToolTip(f"Preview: {result.preview}")

            self.results_list.addItem(item)

    def show_loading(self):
        """Show loading state"""
        self.results_list.clear()
        self.header_label.setText("Searching...")
        self.status_label.setText("Please wait while we search for files.")

    def show_error(self, error_message: str):
        """Show error state"""
        self.results_list.clear()
        self.header_label.setText("Search Error")
        self.status_label.setText(f"Error: {error_message}")

    def on_item_double_clicked(self, item: QListWidgetItem):
        """Handle item double click"""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_selected.emit(file_path)


class FileIndexBrowser(QWidget):
    """Browser for file index with analytics"""

    file_selected = pyqtSignal(str)  # file_path

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_index_data()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()

        # Header
        header_label = QLabel("File Index Browser")
        header_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(header_label)

        # Tab widget for different views
        self.tab_widget = QTabWidget()

        # Files tab
        self.files_tab = self.create_files_tab()
        self.tab_widget.addTab(self.files_tab, "Files")

        # Analytics tab
        self.analytics_tab = self.create_analytics_tab()
        self.tab_widget.addTab(self.analytics_tab, "Analytics")

        layout.addWidget(self.tab_widget)

        # Refresh button
        refresh_button = QPushButton("Refresh Index")
        refresh_button.clicked.connect(self.refresh_index)
        layout.addWidget(refresh_button)

        self.setLayout(layout)

    def create_files_tab(self) -> QWidget:
        """Create files tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        # File table
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(4)
        self.file_table.setHorizontalHeaderLabels(["Name", "Type", "Size", "Modified"])
        self.file_table.horizontalHeader().setStretchLastSection(True)
        self.file_table.itemDoubleClicked.connect(self.on_file_double_clicked)

        layout.addWidget(self.file_table)
        widget.setLayout(layout)
        return widget

    def create_analytics_tab(self) -> QWidget:
        """Create analytics tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Analytics labels
        self.total_files_label = QLabel("Total Files: 0")
        layout.addWidget(self.total_files_label)

        self.file_types_label = QLabel("File Types: 0")
        layout.addWidget(self.file_types_label)

        self.index_size_label = QLabel("Index Size: 0 KB")
        layout.addWidget(self.index_size_label)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def load_index_data(self):
        """Load and display index data"""
        try:
            optimizer = get_file_system_optimizer()
            stats = optimizer.get_index_statistics()

            # Update analytics
            self.total_files_label.setText(
                f"Total Files: {stats.get('total_files', 0)}"
            )
            self.file_types_label.setText(
                f"File Types: {len(stats.get('file_types', {}))}"
            )
            self.index_size_label.setText(
                f"Index Size: {stats.get('index_size_kb', 0)} KB"
            )

            # Load file data into table
            try:
                index_entries = optimizer.get_all_index_entries()
                self.file_table.setRowCount(len(index_entries))

                for row, entry in enumerate(index_entries):
                    self.file_table.setItem(
                        row, 0, QTableWidgetItem(entry.get("filename", ""))
                    )
                    self.file_table.setItem(
                        row, 1, QTableWidgetItem(entry.get("file_type", ""))
                    )
                    self.file_table.setItem(
                        row,
                        2,
                        QTableWidgetItem(f"{entry.get('size_bytes', 0) / 1024:.1f} KB"),
                    )
                    self.file_table.setItem(
                        row, 3, QTableWidgetItem(str(entry.get("last_modified", "")))
                    )

            except AttributeError:
                # Fallback if method doesn't exist
                self.file_table.setRowCount(0)

        except Exception as e:
            logger.error(f"Error loading index data: {e}")

    def refresh_index(self):
        """Refresh the file index"""
        try:
            optimizer = get_file_system_optimizer()
            optimizer.update_index()
            self.load_index_data()
        except Exception as e:
            logger.error(f"Error refreshing index: {e}")

    def on_file_double_clicked(self, item: QTableWidgetItem):
        """Handle file double click"""
        row = item.row()
        filename_item = self.file_table.item(row, 0)
        if filename_item:
            # Emit file selection signal with filename
            self.file_selected.emit(filename_item.text())


# ========== ENHANCED VALIDATION RULES ==========
# Validation rules have been moved to core.validation.validation_rules
