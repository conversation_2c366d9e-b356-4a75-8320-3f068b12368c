"""
RemoveObstacle tag configuration for ability editor.
Handles remove obstacle ability configurations with obstacle type filtering.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from config import OBSTACLE_TYPES


class RemoveObstacleConfig(BaseTagConfig):
    """Configuration for removeObstacle tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "removeObstacle")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for remove obstacle configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting remove obstacle UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Removes obstacles in the targeted area.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Obstacle type filter selection
            remove_obstacle_type_combo = QComboBox()
            obstacle_options = [("any", "Any obstacle type")] + list(OBSTACLE_TYPES)
            remove_obstacle_type_combo.addItems([f"{code} - {desc}" for code, desc in obstacle_options])
            remove_obstacle_type_combo.setToolTip("Type of obstacles to remove (optional filter)")
            self.store_widget("remove_obstacle_type_combo", remove_obstacle_type_combo)
            form_layout.addRow("Filter Type:", remove_obstacle_type_combo)

            layout.addLayout(form_layout)
            
            # Additional info
            info_label = QLabel("Use the Range configuration to define the removal area.")
            layout.addWidget(info_label)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Remove obstacle UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating remove obstacle data")

            # Populate obstacle type filter
            remove_obstacle_type_combo = self.get_widget_by_name("remove_obstacle_type_combo")
            if remove_obstacle_type_combo and "removeObstacleType" in data:
                obstacle_type = data["removeObstacleType"]
                for i in range(remove_obstacle_type_combo.count()):
                    if obstacle_type in remove_obstacle_type_combo.itemText(i):
                        remove_obstacle_type_combo.setCurrentIndex(i)
                        break

            self.log_debug("Remove obstacle data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting remove obstacle data")
            data = {}

            # Collect obstacle type filter
            remove_obstacle_type_combo = self.get_widget_by_name("remove_obstacle_type_combo")
            if remove_obstacle_type_combo:
                obstacle_text = remove_obstacle_type_combo.currentText()
                if obstacle_text:
                    obstacle_type = obstacle_text.split(" - ")[0]
                    data["removeObstacleType"] = obstacle_type

            self.log_debug("Remove obstacle data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
