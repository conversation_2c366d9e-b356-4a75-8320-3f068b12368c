"""
Core module for Adventure Chess Creator

Location: core/

This module contains the foundational classes and interfaces that form
the backbone of the Adventure Chess Creator application.

Directory structure:
/base_classes/     - Abstract base classes for widgets, managers, handlers, and editors
/handlers/         - Specialized data handlers for pieces and abilities
/managers/         - Manager classes for various application components
/interfaces/       - Interface definitions for data handling and UI interaction
/ui/              - Core UI components and utilities
/error_handling/  - Error handling and user-friendly error systems
/performance/     - Performance optimization and caching systems
/security/        - Security validation and data protection
/validation/      - Data validation rules and real-time validation
/workflow/        - Template and workflow management systems

Used by: All editors and application components for foundational functionality
"""

from .base_classes.base_data_handler import BaseDataHandler
from .base_classes.base_editor import BaseEditor
from .base_classes.base_manager import (
    BaseConfigurationManager,
    BaseDataManager,
    BaseManager,
    BaseUIManager,
)

# Import key base classes for easy access
from .base_classes.base_widgets import (
    BaseDataWidget,
    BaseFormWidget,
    BaseTabWidget,
    BaseWidget,
)

# Import error handling system
from .error_handling import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rror<PERSON><PERSON><PERSON>,
    ErrorMessageTranslator,
    ErrorSeverity,
    UserFriendlyError,
    UserFriendlyErrorDialog,
    error_handler,
    safe_file_load,
    safe_file_save,
    show_user_friendly_error,
)

# Import specialized implementations
from .handlers.specialized_data_handlers import (
    EnhancedAbilityDataHandler,
    EnhancedPieceDataHandler,
)
from .interfaces.editor_data_interface import EditorDataInterface

# Import performance system
from .performance import (
    FileSystemOptimizer,
    LazyDataManager,
    SearchResult,
    get_cache_manager,
    get_file_system_optimizer,
    get_lazy_manager,
)

# Import security system
from .security import (
    SecureDataManager,
    SecurityValidator,
    get_recovery_options,
    secure_data_manager,
    security_validator,
)

# Import validation system
from .validation import (
    EnhancedValidationMixin,
    RealTimeValidationWidget,
    ValidationRules,
    create_ability_validation_rules,
    create_piece_validation_rules,
)

# Note: DirectDataManager moved to schemas.data_manager for consolidation


# Streamlined data management system available via schemas.data_manager

__all__ = [
    # Base classes
    "BaseWidget",
    "BaseFormWidget",
    "BaseTabWidget",
    "BaseDataWidget",
    "BaseManager",
    "BaseUIManager",
    "BaseDataManager",
    "BaseConfigurationManager",
    "BaseDataHandler",
    "BaseEditor",
    # Specialized implementations
    "EnhancedPieceDataHandler",
    "EnhancedAbilityDataHandler",
    "EditorDataInterface",
    # Error handling system
    "ErrorSeverity",
    "ErrorCategory",
    "ErrorContext",
    "ErrorHandler",
    "UserFriendlyError",
    "ErrorMessageTranslator",
    "UserFriendlyErrorDialog",
    "safe_file_load",
    "safe_file_save",
    "show_user_friendly_error",
    "error_handler",
    # Note: DirectDataManager moved to schemas.data_manager
    # Performance system
    "get_file_system_optimizer",
    "SearchResult",
    "FileSystemOptimizer",
    "get_lazy_manager",
    "LazyDataManager",
    "get_cache_manager",
    # Security system
    "SecurityValidator",
    "security_validator",
    "SecureDataManager",
    "secure_data_manager",
    "get_recovery_options",
    # Validation system
    "ValidationRules",
    "EnhancedValidationMixin",
    "create_piece_validation_rules",
    "create_ability_validation_rules",
    "RealTimeValidationWidget",
    # Note: Streamlined data management system available via schemas.data_manager
]
