"""
Ability Editor UI Components for Adventure Chess Creator

This module contains all UI creation and layout logic for the ability editor:
- Main UI initialization
- Tab creation (Basic, Tags, Configuration)
- Widget setup and styling
- Layout management

Extracted from ability_editor.py to improve maintainability and separate
UI concerns from business logic.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, QComboBox,
    QLineEdit, QTextEdit, QSpinBox, QCheckBox, QSizePolicy, QPushButton, QGroupBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# Local imports
from core.ui import ResponsiveLayout, ResponsiveScrollArea, TabWidgetResponsive, make_widget_responsive
from core.ui import ValidationStatusWidget

# Import theme system for consistent styling
from core.ui import (
    apply_theme_to_widget,
    apply_manual_themes,
    create_themed_button,
    create_themed_label,
    create_themed_line_edit,
    create_themed_group_box
)

logger = logging.getLogger(__name__)


class AbilityUIComponents:
    """
    Handles all UI component creation and layout for the ability editor.
    
    This class separates UI creation from business logic, making the code
    more maintainable and easier to modify.
    """
    
    def __init__(self, editor_instance):
        """
        Initialize UI components handler.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
        """
        self.editor = editor_instance
    
    def init_main_ui(self) -> QWidget:
        """
        Initialize the main user interface.
        
        Returns:
            The central widget for the main window
        """
        try:
            main_layout = ResponsiveLayout.create_vbox()
            
            # Top toolbar with file operations
            toolbar_layout = self._create_toolbar()
            main_layout.addLayout(toolbar_layout)
            
            # Main content in tabs
            self.editor.tab_widget = self._create_tab_widget()
            main_layout.addWidget(self.editor.tab_widget)
            
            # Status display
            self.editor.status_widget = ValidationStatusWidget()
            main_layout.addWidget(self.editor.status_widget)
            
            # Keep status_label for backward compatibility
            self.editor.status_label = self.editor.status_widget.status_label
            
            # Create central widget
            central_widget = QWidget()
            central_widget.setLayout(main_layout)
            
            # Apply manual theming to all widgets for consistent styling
            apply_manual_themes(central_widget)
            
            logger.info("Main UI initialized successfully")
            return central_widget
            
        except Exception as e:
            logger.error(f"Error initializing main UI: {e}")
            raise
    
    def _create_toolbar(self) -> QHBoxLayout:
        """
        Create the top toolbar with file operations.
        Standardized layout: New → Save → Delete → Load-Quick dropdown

        Returns:
            The toolbar layout
        """
        toolbar_layout = ResponsiveLayout.create_hbox(margin=5, spacing=5)

        # Standardized file operations buttons in specified order: New → Save → Delete → Load-Quick

        # 1. New button
        self.editor.new_btn = QPushButton("📄 New Ability")
        self.editor.new_btn.clicked.connect(self.editor.new_ability)
        self.editor.new_btn.setToolTip("Create a new ability")
        toolbar_layout.addWidget(self.editor.new_btn)

        # 2. Save button
        self.editor.save_btn = QPushButton("💾 Save Ability")
        self.editor.save_btn.clicked.connect(self.editor.save_ability)
        self.editor.save_btn.setToolTip("Save the current ability")
        toolbar_layout.addWidget(self.editor.save_btn)

        # 3. Delete button
        self.editor.delete_btn = create_themed_button("🗑️ Delete Ability", 'danger_button')
        self.editor.delete_btn.clicked.connect(self.editor.delete_current_ability)
        self.editor.delete_btn.setToolTip("Delete the current ability")
        toolbar_layout.addWidget(self.editor.delete_btn)

        # Separator
        toolbar_layout.addSpacing(20)

        # 4. Load-Quick dropdown for ability search/selection
        toolbar_layout.addWidget(QLabel("Load-Quick:"))

        self.editor.load_combo = QComboBox()
        self.editor.load_combo.setEditable(True)
        self.editor.load_combo.setPlaceholderText("Search abilities...")
        self.editor.load_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.editor.load_combo.setMinimumHeight(30)
        self.editor.load_combo.currentTextChanged.connect(self.editor.on_load_selection_changed)
        toolbar_layout.addWidget(self.editor.load_combo)

        toolbar_layout.addStretch()

        return toolbar_layout
    
    def _create_tab_widget(self) -> QTabWidget:
        """
        Create the main tab widget with all tabs.
        
        Returns:
            The configured tab widget
        """
        tab_widget = QTabWidget()
        TabWidgetResponsive.setup_tab_widget(tab_widget)
        make_widget_responsive(tab_widget)
        
        # Basic Info Tab
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "Basic Info")
        
        # Tags Tab
        tags_tab = self.create_tags_tab()
        tab_widget.addTab(tags_tab, "Ability Tags")
        
        # Configuration Tab (dynamic based on selected tags)
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "Configuration")

        # Preview Tab
        preview_tab = self.create_preview_tab()
        tab_widget.addTab(preview_tab, "Preview")

        return tab_widget
    
    def create_basic_tab(self) -> QWidget:
        """
        Create the basic info tab with compact, space-efficient layout.

        Returns:
            The basic tab widget
        """
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()

            # Main container with ultra-compact layout
            main_widget = QWidget()
            main_layout = QVBoxLayout()
            main_layout.setSpacing(5)  # Reduced from 10
            main_layout.setContentsMargins(8, 8, 8, 8)  # Reduced from 15

            # Top row: Name and Activation Mode side by side
            top_row_layout = QHBoxLayout()
            top_row_layout.setSpacing(10)  # Reduced from 20

            # Name field (takes 2/3 of width)
            name_group = QGroupBox("Ability Name")
            name_layout = QVBoxLayout()
            name_layout.setContentsMargins(6, 6, 6, 6)  # Reduced from 10

            self.editor.name_edit = QLineEdit()
            self.editor.name_edit.setPlaceholderText("Enter ability name...")
            self.editor.name_edit.textChanged.connect(self.editor.mark_unsaved_changes)
            name_layout.addWidget(self.editor.name_edit)
            name_group.setLayout(name_layout)

            # Activation mode (takes 1/3 of width)
            activation_group = QGroupBox("Activation")
            activation_layout = QVBoxLayout()
            activation_layout.setContentsMargins(6, 6, 6, 6)  # Reduced from 10

            self.editor.activation_combo = QComboBox()
            self.editor.activation_combo.addItems(["auto", "click"])
            self.editor.activation_combo.currentTextChanged.connect(self.editor.mark_unsaved_changes)
            activation_layout.addWidget(self.editor.activation_combo)
            activation_group.setLayout(activation_layout)

            # Add to top row with proper proportions
            top_row_layout.addWidget(name_group, 2)  # 2/3 width
            top_row_layout.addWidget(activation_group, 1)  # 1/3 width
            main_layout.addLayout(top_row_layout)

            # Middle row: Cost configuration in compact horizontal layout
            cost_group = QGroupBox("Cost Configuration")
            cost_layout = QHBoxLayout()
            cost_layout.setContentsMargins(6, 6, 6, 6)  # Reduced from 10
            cost_layout.setSpacing(10)  # Reduced from 15

            # Cost spinner with label
            cost_layout.addWidget(QLabel("Cost:"))
            self.editor.cost_spin = QSpinBox()
            self.editor.cost_spin.setRange(0, 999)
            self.editor.cost_spin.setValue(0)
            self.editor.cost_spin.setMinimumWidth(80)
            self.editor.cost_spin.valueChanged.connect(self.editor.mark_unsaved_changes)
            cost_layout.addWidget(self.editor.cost_spin)

            # Auto-calculate checkbox
            self.editor.auto_cost_check = QCheckBox("Auto-calculate cost")
            self.editor.auto_cost_check.stateChanged.connect(self.editor.mark_unsaved_changes)
            cost_layout.addWidget(self.editor.auto_cost_check)

            cost_layout.addStretch()  # Push everything to the left
            cost_group.setLayout(cost_layout)
            main_layout.addWidget(cost_group)

            # Bottom row: Description (full width, compact height)
            desc_group = QGroupBox("Description")
            desc_layout = QVBoxLayout()
            desc_layout.setContentsMargins(4, 4, 4, 4)  # Further reduced to match input field sizing

            self.editor.description_edit = QTextEdit()
            self.editor.description_edit.setPlaceholderText("Enter ability description...")
            self.editor.description_edit.setMaximumHeight(60)  # Even more compact height
            self.editor.description_edit.setMinimumHeight(45)  # Reduced minimum height
            # Apply theme styling instead of inline CSS
            apply_theme_to_widget(self.editor.description_edit, 'text_edit')
            self.editor.description_edit.textChanged.connect(self.editor.mark_unsaved_changes)
            desc_layout.addWidget(self.editor.description_edit)
            desc_group.setLayout(desc_layout)
            main_layout.addWidget(desc_group)

            # Add stretch to push content to top
            main_layout.addStretch()

            main_widget.setLayout(main_layout)
            scroll_area.add_widget(main_widget)

            logger.debug("Compact basic tab created successfully")
            return scroll_area

        except Exception as e:
            logger.error(f"Error creating basic tab: {e}")
            raise
    
    def create_tags_tab(self) -> QWidget:
        """
        Create the ability tags tab.
        
        Returns:
            The tags tab widget
        """
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            
            # Add instructions at the top
            instructions = create_themed_label(
                "Select the tags that apply to this ability. "
                "The Configuration tab will update based on your selections.",
                'muted_label'
            )
            instructions.setWordWrap(True)
            
            # Create layout for tags
            tags_layout = ResponsiveLayout.create_vbox()
            tags_layout.addWidget(instructions)
            
            # This will be populated by the tag manager
            self.editor.tags_layout = tags_layout
            
            # Create tags widget and add to scroll area
            tags_widget = QWidget()
            tags_widget.setLayout(tags_layout)
            scroll_area.add_widget(tags_widget)
            
            logger.debug("Tags tab created successfully")
            return scroll_area
            
        except Exception as e:
            logger.error(f"Error creating tags tab: {e}")
            raise
    
    def create_config_tab(self) -> QWidget:
        """
        Create the configuration tab.
        
        Returns:
            The configuration tab widget
        """
        try:
            # Use responsive scroll area directly
            self.editor.config_scroll_area = ResponsiveScrollArea()
            
            # Store reference to the content layout for dynamic updates
            self.editor.config_content_layout = ResponsiveLayout.create_vbox()
            
            # Initial message
            initial_label = create_themed_label("Select ability tags to see configuration options.", 'muted_label')
            initial_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.editor.config_content_layout.addWidget(initial_label)
            
            # Add stretch to push content to top
            self.editor.config_content_layout.addStretch()
            
            # Create config widget and add to scroll area
            config_widget = QWidget()
            config_widget.setLayout(self.editor.config_content_layout)
            self.editor.config_scroll_area.add_widget(config_widget)
            
            logger.debug("Configuration tab created successfully")
            return self.editor.config_scroll_area

        except Exception as e:
            logger.error(f"Error creating configuration tab: {e}")
            raise

    def create_preview_tab(self) -> QWidget:
        """
        Create the preview tab showing JSON representation.

        Returns:
            The preview tab widget
        """
        try:
            from PyQt6.QtWidgets import QTextEdit, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
            from PyQt6.QtCore import Qt
            import json

            # Create main widget
            preview_widget = QWidget()
            layout = QVBoxLayout()

            # Header with refresh button
            header_layout = QHBoxLayout()
            header_label = create_themed_label("JSON Preview", 'header_label')

            refresh_btn = create_themed_button("Refresh Preview", 'secondary_button')
            refresh_btn.clicked.connect(self.refresh_preview)

            header_layout.addWidget(header_label)
            header_layout.addStretch()
            header_layout.addWidget(refresh_btn)
            layout.addLayout(header_layout)

            # JSON display area
            self.editor.preview_text = QTextEdit()
            self.editor.preview_text.setReadOnly(True)
            self.editor.preview_text.setFont(QFont("Consolas", 10))
            # Apply theme styling for text display
            apply_theme_to_widget(self.editor.preview_text, 'text_edit')
            layout.addWidget(self.editor.preview_text)

            preview_widget.setLayout(layout)

            # Initial preview
            self.refresh_preview()

            logger.debug("Preview tab created successfully")
            return preview_widget

        except Exception as e:
            logger.error(f"Error creating preview tab: {e}")
            raise

    def refresh_preview(self):
        """Refresh the JSON preview with current form data."""
        try:
            import json

            # Collect current form data
            data = self.editor.collect_form_data()

            # Format as pretty JSON
            json_str = json.dumps(data, indent=2, ensure_ascii=False)

            # Update preview
            if hasattr(self.editor, 'preview_text'):
                self.editor.preview_text.setPlainText(json_str)

        except Exception as e:
            logger.error(f"Error refreshing preview: {e}")
            if hasattr(self.editor, 'preview_text'):
                self.editor.preview_text.setPlainText(f"Error generating preview: {e}")
    
    def setup_change_tracking(self) -> None:
        """Setup automatic change tracking for UI widgets."""
        try:
            # This method can be called to connect change signals
            # to the editor's mark_unsaved_changes method
            
            # Basic tab widgets are already connected in create_basic_tab
            # Additional widgets can be connected here as needed
            
            logger.debug("Change tracking setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up change tracking: {e}")
    
    def refresh_ui_state(self) -> None:
        """Refresh the UI state after data changes."""
        try:
            # This method can be used to update UI state
            # after loading or saving data
            
            # Update combo box if needed
            if hasattr(self.editor, 'refresh_ability_list'):
                self.editor.refresh_ability_list()
            
            logger.debug("UI state refreshed")
            
        except Exception as e:
            logger.error(f"Error refreshing UI state: {e}")
