"""
Core Workflow Module for Adventure Chess Creator

This module contains workflow optimization and enhancement systems.
Consolidated from the enhancements/workflow/ folder for better organization.
"""

from .auto_save_system import <PERSON>Save<PERSON>ana<PERSON>, BackupManager
from .keyboard_shortcuts import KeyboardShortcutManager
from .template_system import TemplateDialog, TemplateManager
from .undo_redo_system import (
    DataChangeCommand,
    EditorCommand,
    FieldChangeCommand,
    UndoRedoManager,
)
from .workflow_integration import (
    WorkflowIntegrator,
    add_workflow_menu,
    integrate_workflow_optimization,
)

__all__ = [
    "EditorCommand",
    "FieldChangeCommand",
    "DataChangeCommand",
    "UndoRedoManager",
    "KeyboardShortcutManager",
    "AutoSaveManager",
    "BackupManager",
    "TemplateManager",
    "TemplateDialog",
    "WorkflowIntegrator",
    "integrate_workflow_optimization",
    "add_workflow_menu",
]
