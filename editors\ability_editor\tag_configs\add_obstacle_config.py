"""
AddObstacle tag configuration for ability editor.
Handles add obstacle ability configurations with obstacle type selection.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from config import OBSTACLE_TYPES


class AddObstacleConfig(BaseTagConfig):
    """Configuration for addObstacle tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "addObstacle")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for add obstacle configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting add obstacle UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Spawns terrain obstacles like walls or spikes.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Obstacle type selection
            obstacle_type_combo = QComboBox()
            obstacle_type_combo.addItems([f"{code} - {desc}" for code, desc in OBSTACLE_TYPES])
            obstacle_type_combo.setToolTip("Type of obstacle to create")
            self.store_widget("obstacle_type_combo", obstacle_type_combo)
            form_layout.addRow("Obstacle Type:", obstacle_type_combo)

            layout.addLayout(form_layout)
            
            # Additional info
            info_label = QLabel("Use the Range configuration to define where obstacles can be placed.")
            layout.addWidget(info_label)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Add obstacle UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating add obstacle data")

            # Populate obstacle type
            obstacle_type_combo = self.get_widget_by_name("obstacle_type_combo")
            if obstacle_type_combo and "obstacleType" in data:
                obstacle_type = data["obstacleType"]
                for i in range(obstacle_type_combo.count()):
                    if obstacle_type in obstacle_type_combo.itemText(i):
                        obstacle_type_combo.setCurrentIndex(i)
                        break

            self.log_debug("Add obstacle data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting add obstacle data")
            data = {}

            # Collect obstacle type
            obstacle_type_combo = self.get_widget_by_name("obstacle_type_combo")
            if obstacle_type_combo:
                obstacle_text = obstacle_type_combo.currentText()
                if obstacle_text:
                    obstacle_type = obstacle_text.split(" - ")[0]
                    data["obstacleType"] = obstacle_type

            self.log_debug("Add obstacle data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
