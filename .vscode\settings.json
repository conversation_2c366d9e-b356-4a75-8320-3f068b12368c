{
    "zencoder.enableRepoIndexing": true,

    // Python/Pylance Configuration for Adventure Chess
    // Note: Analysis settings are configured in pyrightconfig.json
    "python.defaultInterpreterPath": "python",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.diagnosticMode": "workspace",
    
    // File associations for Adventure Chess
    "files.associations": {
        "*.json": "json"
    },
    
    // Exclude patterns to improve performance
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.*": false
    },
    
    // Python formatting
    "python.formatting.provider": "none",
    "[python]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "editor.rulers": [88, 120],
        "editor.wordWrap": "on",
        "editor.wordWrapColumn": 88
    },

    
    // File watcher settings for Adventure Chess
    "files.watcherExclude": {
        "**/__pycache__/**": true,
        "**/*.pyc": true,
        "**/data/logs/**": true
    },
    
    // Adventure Chess file templates
    "files.defaultLanguage": "python",
    "emmet.includeLanguages": {
        "python": "python"
    }
}