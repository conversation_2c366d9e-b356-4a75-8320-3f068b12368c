"""
Adventure Chess Ability Editor Package

This package contains the refactored ability editor components:
- ability_editor_main.py: Main AbilityEditorWindow class
- ability_ui_components.py: UI widget creation and layout
- ability_data_handlers.py: Data collection and population logic
- ability_tag_managers.py: Tag-specific configuration management

The refactoring improves maintainability by:
1. Separating UI creation from business logic
2. Isolating data handling operations
3. Making tag management more modular
4. Reducing file complexity for easier debugging
"""

# Import main classes for backward compatibility
from .ability_editor_main import AbilityEditorWindow

# Version info
__version__ = "1.1.0"
__author__ = "Adventure Chess Team"

# Public API
__all__ = [
    'AbilityEditorWindow',
]
