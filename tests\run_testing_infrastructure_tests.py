#!/usr/bin/env python3
"""
Testing Infrastructure Test Runner
Runs all new testing infrastructure tests and generates comprehensive reports
"""

import pytest
import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestingInfrastructureRunner:
    """Comprehensive test runner for the new testing infrastructure"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.test_files = [
            "test_pydantic_models.py",
            "test_save_load_workflows.py", 
            "test_ui_automation.py",
            "test_dialog_integration.py"
        ]
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all testing infrastructure tests"""
        print("🧪 Starting Testing Infrastructure Test Suite")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # Run each test file individually for detailed reporting
        for test_file in self.test_files:
            print(f"\n📋 Running {test_file}...")
            self._run_test_file(test_file)
        
        self.end_time = time.time()
        
        # Generate comprehensive report
        report = self._generate_report()
        self._save_report(report)
        self._print_summary(report)
        
        return report
    
    def _run_test_file(self, test_file: str):
        """Run a specific test file and capture results"""
        test_path = Path(__file__).parent / test_file
        
        if not test_path.exists():
            self.test_results[test_file] = {
                "status": "SKIPPED",
                "reason": "File not found",
                "tests_run": 0,
                "tests_passed": 0,
                "tests_failed": 0,
                "duration": 0
            }
            print(f"   ⚠️  SKIPPED: {test_file} not found")
            return
        
        # Run pytest on the specific file
        start_time = time.time()
        
        try:
            # Capture pytest results
            result = pytest.main([
                str(test_path),
                "-v",
                "--tb=short",
                "-q"
            ])
            
            duration = time.time() - start_time
            
            # Parse pytest exit codes
            if result == 0:
                status = "PASSED"
                print(f"   ✅ PASSED: {test_file}")
            elif result == 1:
                status = "FAILED"
                print(f"   ❌ FAILED: {test_file}")
            elif result == 2:
                status = "ERROR"
                print(f"   🔥 ERROR: {test_file}")
            elif result == 5:
                status = "NO_TESTS"
                print(f"   📭 NO TESTS: {test_file}")
            else:
                status = "UNKNOWN"
                print(f"   ❓ UNKNOWN: {test_file} (exit code: {result})")
            
            self.test_results[test_file] = {
                "status": status,
                "exit_code": result,
                "duration": duration,
                "tests_run": "N/A",  # Would need pytest-json-report for detailed stats
                "tests_passed": "N/A",
                "tests_failed": "N/A"
            }
            
        except Exception as e:
            duration = time.time() - start_time
            self.test_results[test_file] = {
                "status": "EXCEPTION",
                "error": str(e),
                "duration": duration,
                "tests_run": 0,
                "tests_passed": 0,
                "tests_failed": 0
            }
            print(f"   💥 EXCEPTION: {test_file} - {str(e)}")
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_duration = self.end_time - self.start_time if self.start_time and self.end_time else 0
        
        # Count results by status
        status_counts = {}
        for result in self.test_results.values():
            status = result["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Calculate success rate
        total_files = len(self.test_files)
        passed_files = status_counts.get("PASSED", 0)
        success_rate = (passed_files / total_files * 100) if total_files > 0 else 0
        
        report = {
            "testing_infrastructure_report": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_duration_seconds": round(total_duration, 2),
                "total_test_files": total_files,
                "status_summary": status_counts,
                "success_rate_percent": round(success_rate, 1),
                "individual_results": self.test_results
            },
            "test_categories": {
                "pydantic_models": {
                    "file": "test_pydantic_models.py",
                    "description": "Tests for all Pydantic model validation, serialization, and edge cases",
                    "status": self.test_results.get("test_pydantic_models.py", {}).get("status", "NOT_RUN")
                },
                "save_load_workflows": {
                    "file": "test_save_load_workflows.py", 
                    "description": "Tests for complete save/load workflows across all data managers",
                    "status": self.test_results.get("test_save_load_workflows.py", {}).get("status", "NOT_RUN")
                },
                "ui_automation": {
                    "file": "test_ui_automation.py",
                    "description": "Automated UI testing framework for editors and components",
                    "status": self.test_results.get("test_ui_automation.py", {}).get("status", "NOT_RUN")
                },
                "dialog_integration": {
                    "file": "test_dialog_integration.py",
                    "description": "Tests for dialog integration with main editors and data flow",
                    "status": self.test_results.get("test_dialog_integration.py", {}).get("status", "NOT_RUN")
                }
            },
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check for failed tests
        failed_tests = [name for name, result in self.test_results.items() 
                       if result["status"] in ["FAILED", "ERROR", "EXCEPTION"]]
        
        if failed_tests:
            recommendations.append(f"🔧 Fix failing tests: {', '.join(failed_tests)}")
        
        # Check for skipped tests
        skipped_tests = [name for name, result in self.test_results.items() 
                        if result["status"] == "SKIPPED"]
        
        if skipped_tests:
            recommendations.append(f"📝 Address skipped tests: {', '.join(skipped_tests)}")
        
        # Check for UI tests (might be skipped in CI)
        ui_test_status = self.test_results.get("test_ui_automation.py", {}).get("status")
        if ui_test_status in ["SKIPPED", "NO_TESTS"]:
            recommendations.append("🖥️  UI tests may require display environment for full execution")
        
        # Check for dialog tests
        dialog_test_status = self.test_results.get("test_dialog_integration.py", {}).get("status")
        if dialog_test_status in ["SKIPPED", "NO_TESTS"]:
            recommendations.append("🗂️  Dialog tests may require Qt environment for full execution")
        
        # General recommendations
        if not failed_tests:
            recommendations.append("✅ All tests passing - consider adding more edge case tests")
        
        recommendations.append("📊 Consider integrating with CI/CD pipeline for automated testing")
        recommendations.append("🔄 Run tests regularly during development to catch regressions early")
        
        return recommendations
    
    def _save_report(self, report: Dict[str, Any]):
        """Save test report to file"""
        report_file = Path(__file__).parent / "testing_infrastructure_report.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n📄 Report saved to: {report_file}")
        except Exception as e:
            print(f"\n⚠️  Could not save report: {e}")
    
    def _print_summary(self, report: Dict[str, Any]):
        """Print test summary to console"""
        print("\n" + "=" * 60)
        print("🧪 TESTING INFRASTRUCTURE SUMMARY")
        print("=" * 60)
        
        summary = report["testing_infrastructure_report"]
        
        print(f"📊 Total Test Files: {summary['total_test_files']}")
        print(f"⏱️  Total Duration: {summary['total_duration_seconds']}s")
        print(f"📈 Success Rate: {summary['success_rate_percent']}%")
        
        print("\n📋 Status Breakdown:")
        for status, count in summary["status_summary"].items():
            emoji = {
                "PASSED": "✅",
                "FAILED": "❌", 
                "ERROR": "🔥",
                "SKIPPED": "⚠️",
                "NO_TESTS": "📭",
                "EXCEPTION": "💥"
            }.get(status, "❓")
            print(f"   {emoji} {status}: {count}")
        
        print("\n🎯 Test Categories:")
        for category, info in report["test_categories"].items():
            status_emoji = {
                "PASSED": "✅",
                "FAILED": "❌",
                "ERROR": "🔥", 
                "SKIPPED": "⚠️",
                "NOT_RUN": "❓"
            }.get(info["status"], "❓")
            print(f"   {status_emoji} {category}: {info['status']}")
        
        print("\n💡 Recommendations:")
        for rec in report["recommendations"]:
            print(f"   {rec}")
        
        print("\n" + "=" * 60)


def main():
    """Main entry point"""
    runner = TestingInfrastructureRunner()
    report = runner.run_all_tests()
    
    # Return exit code based on results
    failed_count = sum(1 for result in runner.test_results.values() 
                      if result["status"] in ["FAILED", "ERROR", "EXCEPTION"])
    
    if failed_count > 0:
        print(f"\n❌ {failed_count} test file(s) failed")
        return 1
    else:
        print(f"\n✅ All test files completed successfully")
        return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
