#!/usr/bin/env python3
"""
Test suite for performance optimization features in Adventure Chess Creator

Tests memory management, async file operations, cache optimization, and threading improvements.
"""

import pytest
import asyncio
import tempfile
import json
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import performance modules
from core.performance.file_system_optimization import FileSystemOptimizer, OptimizedDataManager
from core.performance.lazy_loading_system import LazyDataManager
from core.performance.editor_integration import <PERSON>hancedCacheMana<PERSON>, CacheIntegratedDataManager


class TestFileSystemOptimization:
    """Test file system optimization features"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_path = os.path.join(self.temp_dir, "test_index.db")
        
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_memory_monitoring_initialization(self):
        """Test that memory monitoring is properly initialized"""
        optimizer = FileSystemOptimizer(
            index_db_path=self.test_db_path,
            index_update_interval=1  # Short interval for testing
        )
        
        # Check memory monitoring attributes
        assert hasattr(optimizer, 'memory_threshold_mb')
        assert hasattr(optimizer, 'max_cache_size_mb')
        assert hasattr(optimizer, 'last_memory_check')
        assert hasattr(optimizer, 'memory_check_interval')
        
        # Check default values
        assert optimizer.memory_threshold_mb == 500
        assert optimizer.max_cache_size_mb == 100
        assert optimizer.memory_check_interval == 30
        
    def test_memory_cleanup_functionality(self):
        """Test memory cleanup operations"""
        optimizer = FileSystemOptimizer(index_db_path=self.test_db_path)
        
        # Test memory check method exists
        assert hasattr(optimizer, '_check_memory_usage')
        assert hasattr(optimizer, '_cleanup_memory')
        
        # Test cleanup doesn't crash
        try:
            optimizer._cleanup_memory()
        except Exception as e:
            pytest.fail(f"Memory cleanup failed: {e}")
    
    @pytest.mark.asyncio
    async def test_async_file_operations(self):
        """Test async file loading and saving operations"""
        # Create test data
        test_piece_data = {
            "name": "Test Piece",
            "description": "A test piece for async operations",
            "movement": ["forward_1"]
        }
        
        # Create temporary config
        with patch('config.PIECES_DIR', self.temp_dir):
            data_manager = OptimizedDataManager()
            
            # Test async save
            success = await data_manager.save_piece_async(test_piece_data, "test_piece")
            assert success is True
            
            # Verify file was created
            test_file = Path(self.temp_dir) / "test_piece.json"
            assert test_file.exists()
            
            # Test async load
            loaded_data = await data_manager.load_piece_async("test_piece")
            assert loaded_data is not None
            assert loaded_data["name"] == "Test Piece"
            assert loaded_data["description"] == "A test piece for async operations"
    
    @pytest.mark.asyncio
    async def test_async_ability_operations(self):
        """Test async ability loading and saving operations"""
        # Create test data
        test_ability_data = {
            "name": "Test Ability",
            "description": "A test ability for async operations",
            "cost": 2,
            "target_type": "enemy"
        }
        
        # Create temporary config
        with patch('config.ABILITIES_DIR', self.temp_dir):
            data_manager = OptimizedDataManager()
            
            # Test async save
            success = await data_manager.save_ability_async(test_ability_data, "test_ability")
            assert success is True
            
            # Verify file was created
            test_file = Path(self.temp_dir) / "test_ability.json"
            assert test_file.exists()
            
            # Test async load
            loaded_data = await data_manager.load_ability_async("test_ability")
            assert loaded_data is not None
            assert loaded_data["name"] == "Test Ability"
            assert loaded_data["cost"] == 2


class TestLazyLoadingOptimization:
    """Test lazy loading system optimizations"""
    
    def test_memory_management_initialization(self):
        """Test that memory management is properly initialized in lazy loading"""
        lazy_manager = LazyDataManager(max_workers=2)
        
        # Check memory management attributes
        assert hasattr(lazy_manager, 'memory_threshold_mb')
        assert hasattr(lazy_manager, 'max_cache_entries')
        assert hasattr(lazy_manager, 'last_memory_check')
        assert hasattr(lazy_manager, 'memory_check_interval')
        
        # Check default values
        assert lazy_manager.memory_threshold_mb == 200
        assert lazy_manager.max_cache_entries == 1000
        assert lazy_manager.memory_check_interval == 60
    
    def test_memory_usage_checking(self):
        """Test memory usage checking functionality"""
        lazy_manager = LazyDataManager(max_workers=2)
        
        # Test memory check method exists
        assert hasattr(lazy_manager, 'check_memory_usage')
        assert hasattr(lazy_manager, 'cleanup_memory')
        
        # Test memory check returns statistics
        try:
            stats = lazy_manager.check_memory_usage()
            assert isinstance(stats, dict)
            
            # Check expected keys in stats
            expected_keys = ['memory_mb', 'cache_entries', 'memory_threshold_mb', 'max_cache_entries']
            for key in expected_keys:
                assert key in stats or 'error' in stats  # Allow error case
                
        except Exception as e:
            pytest.fail(f"Memory usage check failed: {e}")
    
    def test_cache_cleanup_functionality(self):
        """Test cache cleanup operations"""
        lazy_manager = LazyDataManager(max_workers=2)
        
        # Add some test metadata to trigger cleanup
        for i in range(1200):  # Exceed max_cache_entries
            lazy_manager.file_metadata[f"test_file_{i}"] = {
                'name': f'Test File {i}',
                'last_accessed': time.time() - i  # Older files have lower timestamps
            }
        
        # Test cleanup
        try:
            lazy_manager.cleanup_memory()
            
            # Verify cache was reduced
            assert len(lazy_manager.file_metadata) <= lazy_manager.max_cache_entries
            
        except Exception as e:
            pytest.fail(f"Cache cleanup failed: {e}")


class TestCacheOptimization:
    """Test cache management optimizations"""
    
    def test_enhanced_cache_manager_initialization(self):
        """Test enhanced cache manager initialization"""
        cache_manager = EnhancedCacheManager(
            max_cache_size_mb=50,
            max_entries=500,
            cleanup_interval_seconds=60
        )
        
        # Check initialization
        assert cache_manager.max_cache_size_bytes == 50 * 1024 * 1024
        assert cache_manager.max_entries == 500
        assert cache_manager.cleanup_interval == 60
        
        # Check cache storage
        assert hasattr(cache_manager, 'piece_cache')
        assert hasattr(cache_manager, 'ability_cache')
        assert hasattr(cache_manager, 'stats')
    
    @pytest.mark.asyncio
    async def test_cache_integrated_data_manager_async_operations(self):
        """Test async operations in cache integrated data manager"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test data
            test_piece_data = {
                "name": "Cached Test Piece",
                "description": "A test piece for cache integration",
                "movement": ["forward_1"]
            }
            
            # Create cache integrated data manager
            data_manager = CacheIntegratedDataManager()
            
            # Test async operations with patched config
            with patch('config.PIECES_DIR', temp_dir):
                # Test async save
                success, error = await data_manager.save_piece_async(test_piece_data, "cached_test_piece")
                assert success is True
                assert error is None
                
                # Test async load
                loaded_data, error = await data_manager.load_piece_async("cached_test_piece")
                assert loaded_data is not None
                assert error is None
                assert loaded_data["name"] == "Cached Test Piece"


class TestPerformanceIntegration:
    """Test integration of all performance optimizations"""
    
    def test_all_modules_import_successfully(self):
        """Test that all performance modules can be imported without errors"""
        try:
            from core.performance.file_system_optimization import FileSystemOptimizer
            from core.performance.lazy_loading_system import LazyDataManager
            from core.performance.editor_integration import EnhancedCacheManager
            
            # Test basic instantiation
            optimizer = FileSystemOptimizer()
            lazy_manager = LazyDataManager()
            cache_manager = EnhancedCacheManager()
            
            # Verify they have expected methods
            assert hasattr(optimizer, '_check_memory_usage')
            assert hasattr(lazy_manager, 'check_memory_usage')
            assert hasattr(cache_manager, 'get_cache_stats')
            
        except Exception as e:
            pytest.fail(f"Performance module integration failed: {e}")
    
    def test_memory_monitoring_across_modules(self):
        """Test that memory monitoring works across all performance modules"""
        try:
            # Test file system optimizer
            optimizer = FileSystemOptimizer()
            optimizer._check_memory_usage()
            
            # Test lazy loading manager
            lazy_manager = LazyDataManager()
            stats = lazy_manager.check_memory_usage()
            assert isinstance(stats, dict)
            
            # Test cache manager
            cache_manager = EnhancedCacheManager()
            cache_stats = cache_manager.get_cache_stats()
            assert isinstance(cache_stats, dict)
            
        except Exception as e:
            pytest.fail(f"Cross-module memory monitoring failed: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
