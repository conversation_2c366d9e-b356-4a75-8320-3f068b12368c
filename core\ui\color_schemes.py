"""
Enhanced Color Schemes for Adventure Chess Creator

This module provides comprehensive color schemes for consistent visual feedback
throughout the application.
"""


class ColorSchemes:
    """Enhanced color schemes for better visual feedback"""

    # Status colors
    SUCCESS = "#28a745"
    ERROR = "#dc3545"
    WARNING = "#ffc107"
    INFO = "#17a2b8"
    LOADING = "#007bff"

    # Grid colors - Enhanced for better visibility
    GRID_EMPTY = "#2d3748"
    GRID_MOVE = "#4299e1"  # Blue - movement
    GRID_ATTACK = "#f56565"  # Red - attack
    GRID_BOTH = "#9f7aea"  # Purple - both move and attack
    GRID_ACTION = "#48bb78"  # Green - special action
    GRID_ANY = "#ed8936"  # Orange - any action
    GRID_TARGET = "#ff9800"  # Amber - target position
    GRID_BORDER = "#4a5568"  # Border color

    # Validation colors
    VALID = "#d4edda"
    INVALID = "#f8d7da"
    PENDING = "#fff3cd"

    # Background colors
    DARK_BG = "#1a202c"
    LIGHT_BG = "#f7fafc"
    CARD_BG = "#ffffff"
    HOVER_BG = "#e2e8f0"
