"""
Unified Data Manager for Adventure Chess Creator

This is the single, streamlined data management system that combines:
- Validation capabilities with Pydantic models
- Performance optimizations with intelligent caching
- Standardized error handling with recovery
- Backup integration and multi-stage recovery

This replaces multiple data manager approaches with one unified system.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from pathlib import Path

from .piece_schema import Piece
from .ability_schema import Ability
from config import PIECES_DIR, ABILITIES_DIR, PIECE_EXTENSION, ABILITY_EXTENSION
from core.error_handling import (
    error_handler, ErrorSeverity, ErrorCategory,
    data_load_handler, data_save_handler, data_delete_handler
)

logger = logging.getLogger(__name__)

# Global instance for backward compatibility
pydantic_data_manager = None


class PydanticDataManager:
    """
    Unified data management system for Adventure Chess Creator.

    Combines validation, performance, caching, and error handling into one streamlined system.
    Routes operations intelligently based on context and requirements.
    """

    def __init__(self):
        # Unified caching system
        self.piece_cache: Dict[str, Piece] = {}
        self.ability_cache: Dict[str, Ability] = {}
        self.metadata_cache: Dict[str, Dict] = {}

        # Error tracking
        self.error_log: List[str] = []

        # Performance tracking
        self.cache_hits = 0
        self.cache_misses = 0

        # Change listeners for backward compatibility
        self._change_listeners: List[Callable] = []

        logger.info("Unified Data Manager initialized")

    # ========== PERFORMANCE AND CACHE MANAGEMENT ==========

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache performance statistics"""
        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
            'piece_cache_size': len(self.piece_cache),
            'ability_cache_size': len(self.ability_cache)
        }

    def clear_cache(self):
        """Clear all caches"""
        self.piece_cache.clear()
        self.ability_cache.clear()
        self.metadata_cache.clear()
        logger.info("All caches cleared")

    def clear_piece_cache(self):
        """Clear only piece cache"""
        self.piece_cache.clear()
        logger.info("Piece cache cleared")

    def clear_ability_cache(self):
        """Clear only ability cache"""
        self.ability_cache.clear()
        logger.info("Ability cache cleared")
    
    # ========== UNIFIED PIECE OPERATIONS ==========

    @data_load_handler()
    def load_piece(self, filename: str, validate: bool = True) -> Tuple[Optional[Piece], Optional[str]]:
        """
        Unified piece loading with intelligent validation and caching.

        Args:
            filename: Name of piece file to load
            validate: Whether to perform full Pydantic validation (default: True)

        Returns:
            Tuple of (piece_model, error_message)
        """
        # Normalize filename
        if not filename.endswith(PIECE_EXTENSION):
            filename += PIECE_EXTENSION

        filepath = Path(PIECES_DIR) / filename

        if not filepath.exists():
            return None, f"Piece file not found: {filename}"

        # Check cache first
        cache_key = str(filepath)
        if cache_key in self.piece_cache:
            self.cache_hits += 1
            logger.debug(f"Loaded piece from cache: {filename}")
            return self.piece_cache[cache_key], None

        self.cache_misses += 1

        # Load and parse JSON
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if validate:
            # Use Pydantic validation for user operations
            piece = Piece.from_legacy_dict(data)

            # Validate the piece
            try:
                piece.model_validate(piece.model_dump())
            except Exception as e:
                return None, f"Validation failed for piece {filename}: {e}"
        else:
            # Direct loading for performance-critical operations
            piece = Piece.from_legacy_dict(data)

        # Cache the result
        self.piece_cache[cache_key] = piece

        # Notify change listeners
        self._notify_change_listeners('piece_loaded', piece)

        logger.debug(f"Successfully loaded piece: {filename}")
        return piece, None

    # ========== BACKWARD COMPATIBILITY LAYER ==========
    # Static methods to maintain compatibility with old DirectDataManager interface

    @staticmethod
    def save_piece_dict(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Compatibility method for saving piece data as dictionary (old DirectDataManager interface)
        """
        try:
            # Convert dict to Pydantic model
            piece = Piece.from_legacy_dict(piece_data)

            # Use instance method
            manager = pydantic_data_manager
            return manager.save_piece(piece, filename, validate=False)

        except Exception as e:
            return False, str(e)

    @staticmethod
    def load_piece_dict(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Compatibility method for loading piece data as dictionary (old DirectDataManager interface)
        """
        try:
            # Use instance method
            manager = pydantic_data_manager
            piece, error = manager.load_piece(filename, validate=False)

            if piece is None:
                return None, error

            # Convert to dictionary format
            return piece.model_dump(), None

        except Exception as e:
            return None, str(e)

    @staticmethod
    def save_ability_dict(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Compatibility method for saving ability data as dictionary (old DirectDataManager interface)
        """
        try:
            # Convert dict to Pydantic model
            ability = Ability.from_legacy_dict(ability_data)

            # Use instance method
            manager = pydantic_data_manager
            return manager.save_ability(ability, filename)

        except Exception as e:
            return False, str(e)

    @staticmethod
    def load_ability_dict(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Compatibility method for loading ability data as dictionary (old DirectDataManager interface)
        """
        try:
            # Use instance method
            manager = pydantic_data_manager
            ability, error = manager.load_ability(filename)

            if ability is None:
                return None, error

            # Convert to dictionary format
            return ability.model_dump(), None

        except Exception as e:
            return None, str(e)

    @staticmethod
    def list_pieces_static() -> List[str]:
        """Compatibility method for listing pieces (old DirectDataManager interface)"""
        return pydantic_data_manager.list_pieces()

    @staticmethod
    def list_abilities_static() -> List[str]:
        """Compatibility method for listing abilities (old DirectDataManager interface)"""
        return pydantic_data_manager.list_abilities()

    def _notify_change_listeners(self, event_type: str, data: Any):
        """Notify registered change listeners of data changes"""
        for listener in self._change_listeners:
            try:
                listener(event_type, data)
            except Exception as e:
                logger.warning(f"Change listener failed: {e}")

    def add_change_listener(self, listener: Callable):
        """Add a change listener for data events"""
        self._change_listeners.append(listener)

    def remove_change_listener(self, listener: Callable):
        """Remove a change listener"""
        if listener in self._change_listeners:
            self._change_listeners.remove(listener)

    @data_save_handler()
    def save_piece(self, piece: Piece, filename: Optional[str] = None, validate: bool = True) -> Tuple[bool, Optional[str]]:
        """
        Save a piece to file with Pydantic validation
        Returns: (success, error_message)
        """
        try:
            # Validate the piece model
            piece.model_dump()  # This will raise ValidationError if invalid
            
            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(piece.name)
            
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            # Convert to new schema format for file storage (no legacy compatibility)
            data = piece.model_dump(exclude_unset=True, by_alias=False)
            
            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file with readable movement pattern formatting
            from utils.readable_json_formatter import save_with_readable_patterns
            save_with_readable_patterns(data, filepath)
            
            # Update cache
            cache_key = str(filepath)
            self.piece_cache[cache_key] = piece
            
            logger.info(f"Successfully saved piece: {piece.name} to {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error saving piece {piece.name}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg
    
    def load_piece_by_name(self, name: str) -> Tuple[Optional[Piece], Optional[str]]:
        """Load a piece by its name (searches all piece files)"""
        try:
            pieces_dir = Path(PIECES_DIR)
            if not pieces_dir.exists():
                return None, "Pieces directory not found"
            
            # Search for piece by name
            for filepath in pieces_dir.glob(f"*{PIECE_EXTENSION}"):
                piece, error = self.load_piece(filepath.name)
                if piece and piece.name == name:
                    return piece, None
            
            return None, f"Piece '{name}' not found"
            
        except Exception as e:
            error_msg = f"Error searching for piece '{name}': {e}"
            logger.error(error_msg)
            return None, error_msg
    
    def list_pieces(self) -> List[str]:
        """Get list of all available piece files (regardless of validation status)"""
        pieces = []
        try:
            pieces_dir = Path(PIECES_DIR)
            if pieces_dir.exists():
                for filepath in pieces_dir.glob(f"*{PIECE_EXTENSION}"):
                    # Extract filename without extension as piece name
                    piece_name = filepath.stem
                    pieces.append(piece_name)
        except Exception as e:
            logger.error(f"Error listing pieces: {e}")

        return sorted(pieces)
    
    # ========== ABILITY OPERATIONS ==========
    
    def load_ability(self, filename: str) -> Tuple[Optional[Ability], Optional[str]]:
        """
        Load an ability from file using Pydantic validation
        Returns: (ability_model, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION
            
            filepath = Path(ABILITIES_DIR) / filename
            
            if not filepath.exists():
                return None, f"Ability file not found: {filename}"
            
            # Check cache first
            cache_key = str(filepath)
            if cache_key in self.ability_cache:
                logger.debug(f"Loaded ability from cache: {filename}")
                return self.ability_cache[cache_key], None
            
            # Load and parse JSON
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Create Pydantic model from legacy data
            ability = Ability.from_legacy_dict(data)
            
            # Cache the ability
            self.ability_cache[cache_key] = ability
            
            logger.info(f"Successfully loaded ability: {ability.name}")
            return ability, None
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in ability file {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
            
        except Exception as e:
            error_msg = f"Error loading ability {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
    
    def save_ability(self, ability: Ability, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save an ability to file with Pydantic validation
        Returns: (success, error_message)
        """
        try:
            # Validate the ability model
            ability.model_dump()  # This will raise ValidationError if invalid
            
            # Validate all tag data
            validation_errors = ability.validate_all_tags()
            if validation_errors:
                error_msg = f"Tag validation errors: {validation_errors}"
                logger.error(error_msg)
                return False, error_msg
            
            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(ability.name)
            
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION
            
            filepath = Path(ABILITIES_DIR) / filename
            
            # Convert to new schema format for file storage (no legacy compatibility)
            data = ability.model_dump(exclude_unset=True, by_alias=False)
            
            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file with readable movement pattern formatting
            from utils.readable_json_formatter import save_with_readable_patterns
            save_with_readable_patterns(data, filepath)
            
            # Update cache
            cache_key = str(filepath)
            self.ability_cache[cache_key] = ability
            
            logger.info(f"Successfully saved ability: {ability.name} to {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error saving ability {ability.name}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg
    
    def load_ability_by_name(self, name: str) -> Tuple[Optional[Ability], Optional[str]]:
        """Load an ability by its name (searches all ability files)"""
        try:
            abilities_dir = Path(ABILITIES_DIR)
            if not abilities_dir.exists():
                return None, "Abilities directory not found"
            
            # Search for ability by name
            for filepath in abilities_dir.glob(f"*{ABILITY_EXTENSION}"):
                ability, error = self.load_ability(filepath.name)
                if ability and ability.name == name:
                    return ability, None
            
            return None, f"Ability '{name}' not found"
            
        except Exception as e:
            error_msg = f"Error searching for ability '{name}': {e}"
            logger.error(error_msg)
            return None, error_msg
    
    def list_abilities(self) -> List[str]:
        """Get list of all available ability names"""
        abilities = []
        try:
            abilities_dir = Path(ABILITIES_DIR)
            if abilities_dir.exists():
                for filepath in abilities_dir.glob(f"*{ABILITY_EXTENSION}"):
                    ability, error = self.load_ability(filepath.name)
                    if ability:
                        abilities.append(ability.name)
        except Exception as e:
            logger.error(f"Error listing abilities: {e}")

        return sorted(abilities)
    
    # ========== UTILITY METHODS ==========
    
    def _sanitize_filename(self, name: str) -> str:
        """Sanitize a name for use as a filename"""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')
        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed"
        return sanitized
    

    
    def get_error_log(self) -> List[str]:
        """Get list of all errors that occurred"""
        return self.error_log.copy()
    
    def clear_error_log(self):
        """Clear the error log"""
        self.error_log.clear()


# ========== COMPATIBILITY LAYER ==========

class DirectDataManager:
    """
    Compatibility class that provides the old DirectDataManager interface
    while using the new unified PydanticDataManager internally
    """

    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save piece data (compatibility method)"""
        return PydanticDataManager.save_piece_dict(piece_data, filename)

    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load piece data (compatibility method)"""
        return PydanticDataManager.load_piece_dict(filename)

    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save ability data (compatibility method)"""
        return PydanticDataManager.save_ability_dict(ability_data, filename)

    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load ability data (compatibility method)"""
        return PydanticDataManager.load_ability_dict(filename)

    @staticmethod
    def list_pieces() -> List[str]:
        """List all pieces (compatibility method)"""
        return PydanticDataManager.list_pieces_static()

    @staticmethod
    def list_abilities() -> List[str]:
        """List all abilities (compatibility method)"""
        return PydanticDataManager.list_abilities_static()


# Global instance for use throughout the application
pydantic_data_manager = PydanticDataManager()
