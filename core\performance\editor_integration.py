#!/usr/bin/env python3
"""
Comprehensive Editor Integration and Cache Management for Adventure Chess Creator

This consolidated module provides:
- Enhanced cache management with size limits and automatic cleanup
- Memory monitoring and file invalidation
- Editor integration for lazy loading and performance optimization
- UI component integration with caching

Key Features:
- Cache size limits and automatic cleanup
- Memory monitoring and warnings
- File watching for cache invalidation
- Editor integration with lazy loading
- Performance optimization for UI components

Consolidates functionality from:
- enhanced_cache_manager.py (cache management)
- lazy_editor_integration.py (editor integration)
"""

import json
import logging
import os
import threading
from collections import OrderedDict
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

import aiofiles
import psutil
from PyQt6.QtWidgets import QVBoxLayout, QWidget

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Represents a single cache entry with metadata"""

    data: Any
    access_time: datetime
    creation_time: datetime
    file_path: Optional[str] = None
    file_mtime: Optional[float] = None
    access_count: int = 0
    size_bytes: int = 0


class EnhancedCacheManager:
    """
    Enhanced cache management system with size limits, automatic cleanup, and monitoring
    """

    def __init__(
        self,
        max_cache_size_mb: int = 100,
        max_entries: int = 1000,
        cleanup_interval_seconds: int = 300,  # 5 minutes
        memory_warning_threshold: float = 0.8,  # 80% of system memory
        enable_file_watching: bool = True,
    ):

        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        self.max_entries = max_entries
        self.cleanup_interval = cleanup_interval_seconds
        self.memory_warning_threshold = memory_warning_threshold
        self.enable_file_watching = enable_file_watching

        # Cache storage using OrderedDict for LRU behavior
        self.piece_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.ability_cache: OrderedDict[str, CacheEntry] = OrderedDict()

        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "invalidations": 0,
            "memory_warnings": 0,
            "cleanup_runs": 0,
        }

        # File watching for cache invalidation
        self.watched_files: Set[str] = set()
        self.file_mtimes: Dict[str, float] = {}

        # Background cleanup thread
        self.cleanup_thread: Optional[threading.Thread] = None
        self.cleanup_stop_event = threading.Event()

        # Memory monitoring
        self.last_memory_warning = datetime.min

        # Start background cleanup
        self._start_cleanup_thread()

    def get_piece(self, cache_key: str) -> Optional[Any]:
        """Get piece from cache with LRU update"""
        return self._get_from_cache(self.piece_cache, cache_key)

    def set_piece(
        self, cache_key: str, data: Any, file_path: Optional[str] = None
    ) -> None:
        """Set piece in cache with metadata"""
        self._set_in_cache(self.piece_cache, cache_key, data, file_path)

    def get_ability(self, cache_key: str) -> Optional[Any]:
        """Get ability from cache with LRU update"""
        return self._get_from_cache(self.ability_cache, cache_key)

    def set_ability(
        self, cache_key: str, data: Any, file_path: Optional[str] = None
    ) -> None:
        """Set ability in cache with metadata"""
        self._set_in_cache(self.ability_cache, cache_key, data, file_path)

    def _get_from_cache(self, cache: OrderedDict, cache_key: str) -> Optional[Any]:
        """Internal method to get data from cache"""
        if cache_key not in cache:
            self.stats["misses"] += 1
            return None

        # Check if file has been modified (cache invalidation)
        entry = cache[cache_key]
        if self._is_file_modified(entry):
            logger.debug(f"Cache invalidated for modified file: {cache_key}")
            del cache[cache_key]
            self.stats["invalidations"] += 1
            self.stats["misses"] += 1
            return None

        # Update access metadata and move to end (LRU)
        entry.access_time = datetime.now()
        entry.access_count += 1
        cache.move_to_end(cache_key)

        self.stats["hits"] += 1
        return entry.data

    def _set_in_cache(
        self,
        cache: OrderedDict,
        cache_key: str,
        data: Any,
        file_path: Optional[str] = None,
    ) -> None:
        """Internal method to set data in cache"""
        now = datetime.now()

        # Calculate approximate size
        size_bytes = self._estimate_size(data)

        # Get file modification time if file path provided
        file_mtime = None
        if file_path and os.path.exists(file_path):
            file_mtime = os.path.getmtime(file_path)
            self.watched_files.add(file_path)
            self.file_mtimes[file_path] = file_mtime

        # Create cache entry
        entry = CacheEntry(
            data=data,
            access_time=now,
            creation_time=now,
            file_path=file_path,
            file_mtime=file_mtime,
            access_count=1,
            size_bytes=size_bytes,
        )

        # Remove existing entry if present
        if cache_key in cache:
            del cache[cache_key]

        # Add new entry
        cache[cache_key] = entry

        # Enforce cache limits
        self._enforce_cache_limits()

        # Check memory usage
        self._check_memory_usage()

    def _is_file_modified(self, entry: CacheEntry) -> bool:
        """Check if the file associated with cache entry has been modified"""
        if not entry.file_path or not entry.file_mtime:
            return False

        if not os.path.exists(entry.file_path):
            return True  # File was deleted

        current_mtime = os.path.getmtime(entry.file_path)
        return current_mtime != entry.file_mtime

    def _estimate_size(self, data: Any) -> int:
        """Estimate the memory size of cached data"""
        try:
            # Simple estimation based on string representation
            return len(str(data).encode("utf-8"))
        except (TypeError, AttributeError, UnicodeError):
            return 1024  # Default estimate

    def _enforce_cache_limits(self) -> None:
        """Enforce cache size and entry limits"""
        # Combine both caches for size calculation
        all_entries = list(self.piece_cache.values()) + list(
            self.ability_cache.values()
        )
        total_size = sum(entry.size_bytes for entry in all_entries)
        total_entries = len(self.piece_cache) + len(self.ability_cache)

        # Check if we need to evict entries
        evictions_needed = 0

        if total_size > self.max_cache_size_bytes:
            evictions_needed = max(evictions_needed, len(all_entries) // 4)  # Evict 25%

        if total_entries > self.max_entries:
            evictions_needed = max(evictions_needed, total_entries - self.max_entries)

        if evictions_needed > 0:
            self._evict_lru_entries(evictions_needed)

    def _evict_lru_entries(self, count: int) -> None:
        """Evict least recently used entries"""
        # Collect all entries with their cache and key
        all_entries = []
        for key, entry in self.piece_cache.items():
            all_entries.append((entry.access_time, "piece", key, entry))
        for key, entry in self.ability_cache.items():
            all_entries.append((entry.access_time, "ability", key, entry))

        # Sort by access time (oldest first)
        all_entries.sort(key=lambda x: x[0])

        # Evict oldest entries
        evicted = 0
        for access_time, cache_type, key, entry in all_entries:
            if evicted >= count:
                break

            if cache_type == "piece" and key in self.piece_cache:
                del self.piece_cache[key]
                evicted += 1
            elif cache_type == "ability" and key in self.ability_cache:
                del self.ability_cache[key]
                evicted += 1

        self.stats["evictions"] += evicted
        logger.debug(f"Evicted {evicted} cache entries to enforce limits")

    def _check_memory_usage(self) -> None:
        """Check system memory usage and warn if high"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent / 100.0

            if memory_percent > self.memory_warning_threshold:
                # Only warn once per hour to avoid spam
                now = datetime.now()
                if now - self.last_memory_warning > timedelta(hours=1):
                    logger.warning(
                        f"High memory usage detected: {memory_percent:.1%} of system memory in use"
                    )
                    self.stats["memory_warnings"] += 1
                    self.last_memory_warning = now

                    # Aggressive cleanup if memory is very high
                    if memory_percent > 0.9:  # 90%
                        self._evict_lru_entries(
                            len(self.piece_cache) // 2 + len(self.ability_cache) // 2
                        )

        except Exception as e:
            logger.debug(f"Could not check memory usage: {e}")

    def _start_cleanup_thread(self) -> None:
        """Start background cleanup thread"""
        if self.cleanup_thread is None or not self.cleanup_thread.is_alive():
            self.cleanup_thread = threading.Thread(
                target=self._cleanup_worker, daemon=True
            )
            self.cleanup_thread.start()
            logger.debug("Started cache cleanup thread")

    def _cleanup_worker(self) -> None:
        """Background worker for periodic cache cleanup"""
        while not self.cleanup_stop_event.wait(self.cleanup_interval):
            try:
                self._periodic_cleanup()
            except Exception as e:
                logger.error(f"Error in cache cleanup worker: {e}")

    def _periodic_cleanup(self) -> None:
        """Perform periodic cache cleanup"""
        logger.debug("Running periodic cache cleanup")

        # Remove entries for deleted files
        self._cleanup_deleted_files()

        # Remove old entries (older than 1 hour with no recent access)
        self._cleanup_old_entries()

        # Check memory usage
        self._check_memory_usage()

        self.stats["cleanup_runs"] += 1

    def _cleanup_deleted_files(self) -> None:
        """Remove cache entries for files that no longer exist"""
        to_remove = []

        for key, entry in self.piece_cache.items():
            if entry.file_path and not os.path.exists(entry.file_path):
                to_remove.append(("piece", key))

        for key, entry in self.ability_cache.items():
            if entry.file_path and not os.path.exists(entry.file_path):
                to_remove.append(("ability", key))

        for cache_type, key in to_remove:
            if cache_type == "piece":
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats["invalidations"] += 1

        if to_remove:
            logger.debug(f"Removed {len(to_remove)} cache entries for deleted files")

    def _cleanup_old_entries(self) -> None:
        """Remove old, unused cache entries"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        to_remove = []

        for key, entry in self.piece_cache.items():
            if entry.access_time < cutoff_time and entry.access_count == 1:
                to_remove.append(("piece", key))

        for key, entry in self.ability_cache.items():
            if entry.access_time < cutoff_time and entry.access_count == 1:
                to_remove.append(("ability", key))

        for cache_type, key in to_remove:
            if cache_type == "piece":
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats["evictions"] += 1

        if to_remove:
            logger.debug(f"Removed {len(to_remove)} old cache entries")

    def invalidate_file(self, file_path: str) -> None:
        """Manually invalidate cache entries for a specific file"""
        to_remove = []

        for key, entry in self.piece_cache.items():
            if entry.file_path == file_path:
                to_remove.append(("piece", key))

        for key, entry in self.ability_cache.items():
            if entry.file_path == file_path:
                to_remove.append(("ability", key))

        for cache_type, key in to_remove:
            if cache_type == "piece":
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats["invalidations"] += 1

        if to_remove:
            logger.debug(
                f"Invalidated {len(to_remove)} cache entries for file: {file_path}"
            )

    def clear_all(self) -> None:
        """Clear all cache entries"""
        piece_count = len(self.piece_cache)
        ability_count = len(self.ability_cache)

        self.piece_cache.clear()
        self.ability_cache.clear()

        logger.info(
            f"Cleared all cache entries: {piece_count} pieces, {ability_count} abilities"
        )

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        piece_size = sum(entry.size_bytes for entry in self.piece_cache.values())
        ability_size = sum(entry.size_bytes for entry in self.ability_cache.values())
        total_size = piece_size + ability_size

        hit_rate = 0.0
        total_requests = self.stats["hits"] + self.stats["misses"]
        if total_requests > 0:
            hit_rate = self.stats["hits"] / total_requests

        return {
            "entries": {
                "pieces": len(self.piece_cache),
                "abilities": len(self.ability_cache),
                "total": len(self.piece_cache) + len(self.ability_cache),
            },
            "size": {
                "pieces_bytes": piece_size,
                "abilities_bytes": ability_size,
                "total_bytes": total_size,
                "total_mb": total_size / (1024 * 1024),
                "max_mb": self.max_cache_size_bytes / (1024 * 1024),
            },
            "performance": {
                "hit_rate": hit_rate,
                "hits": self.stats["hits"],
                "misses": self.stats["misses"],
                "total_requests": total_requests,
            },
            "maintenance": {
                "evictions": self.stats["evictions"],
                "invalidations": self.stats["invalidations"],
                "cleanup_runs": self.stats["cleanup_runs"],
                "memory_warnings": self.stats["memory_warnings"],
            },
            "limits": {
                "max_entries": self.max_entries,
                "max_size_mb": self.max_cache_size_bytes / (1024 * 1024),
                "cleanup_interval_seconds": self.cleanup_interval,
            },
        }

    def shutdown(self) -> None:
        """Shutdown the cache manager and cleanup resources"""
        self.cleanup_stop_event.set()
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)

        self.clear_all()
        logger.info("Cache manager shutdown complete")


# Global cache manager instance
cache_manager = EnhancedCacheManager()


def get_cache_manager() -> EnhancedCacheManager:
    """Get the global cache manager instance"""
    return cache_manager


class CacheIntegratedDataManager:
    """
    Enhanced data manager that integrates with the cache management system
    Drop-in replacement for PydanticDataManager with enhanced caching
    """

    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.error_log: List[str] = []

    def load_piece(self, filename: str) -> Tuple[Optional[Any], Optional[str]]:
        """Load piece with enhanced caching"""
        try:
            from config import PIECE_EXTENSION, PIECES_DIR

            # Normalize filename
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION

            filepath = Path(PIECES_DIR) / filename
            cache_key = str(filepath)

            if not filepath.exists():
                return None, f"Piece file not found: {filename}"

            # Check enhanced cache first
            cached_data = self.cache_manager.get_piece(cache_key)
            if cached_data is not None:
                logger.debug(f"Loaded piece from enhanced cache: {filename}")
                return cached_data, None

            # Load from file
            with open(filepath, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Cache with file path for invalidation
            self.cache_manager.set_piece(cache_key, data, str(filepath))

            logger.info(f"Successfully loaded and cached piece: {filename}")
            return data, None

        except Exception as e:
            error_msg = f"Error loading piece {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    def save_piece(
        self, piece_data: Dict[str, Any], filename: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Save piece and update cache"""
        try:
            from config import PIECE_EXTENSION, PIECES_DIR

            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(piece_data.get("name", "unnamed"))

            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION

            filepath = Path(PIECES_DIR) / filename
            cache_key = str(filepath)

            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # Save to file with readable movement pattern formatting
            from utils.readable_json_formatter import save_with_readable_patterns

            save_with_readable_patterns(piece_data, filepath)

            # Update cache
            self.cache_manager.set_piece(cache_key, piece_data, str(filepath))

            logger.info(f"Successfully saved and cached piece: {filename}")
            return True, None

        except Exception as e:
            error_msg = f"Error saving piece: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg

    def load_ability(self, filename: str) -> Tuple[Optional[Any], Optional[str]]:
        """Load ability with enhanced caching"""
        try:
            from config import ABILITIES_DIR, ABILITY_EXTENSION

            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename
            cache_key = str(filepath)

            if not filepath.exists():
                return None, f"Ability file not found: {filename}"

            # Check enhanced cache first
            cached_data = self.cache_manager.get_ability(cache_key)
            if cached_data is not None:
                logger.debug(f"Loaded ability from enhanced cache: {filename}")
                return cached_data, None

            # Load from file
            with open(filepath, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Cache with file path for invalidation
            self.cache_manager.set_ability(cache_key, data, str(filepath))

            logger.info(f"Successfully loaded and cached ability: {filename}")
            return data, None

        except Exception as e:
            error_msg = f"Error loading ability {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    def save_ability(
        self, ability_data: Dict[str, Any], filename: Optional[str] = None
    ) -> Tuple[bool, Optional[str]]:
        """Save ability and update cache"""
        try:
            from config import ABILITIES_DIR, ABILITY_EXTENSION

            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(ability_data.get("name", "unnamed"))

            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename
            cache_key = str(filepath)

            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # Save to file
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(ability_data, f, indent=2, ensure_ascii=False)

            # Update cache
            self.cache_manager.set_ability(cache_key, ability_data, str(filepath))

            logger.info(f"Successfully saved and cached ability: {filename}")
            return True, None

        except Exception as e:
            error_msg = f"Error saving ability: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg

    def _sanitize_filename(self, name: str) -> str:
        """Sanitize a name for use as a filename"""
        import re

        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', "_", name)
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(" .")
        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed"
        return sanitized

    def clear_cache(self):
        """Clear all cached data"""
        self.cache_manager.clear_all()
        logger.info("Enhanced cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return self.cache_manager.get_cache_stats()

    def get_error_log(self) -> List[str]:
        """Get list of all errors that occurred"""
        return self.error_log.copy()

    def clear_error_log(self):
        """Clear the error log"""
        self.error_log.clear()

    async def load_piece_async(
        self, filename: str
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load piece data asynchronously for better performance"""
        try:
            from config import PIECES_DIR

            file_path = Path(PIECES_DIR) / f"{filename}.json"

            # Check cache first
            f"piece_{filename}"
            cached_entry = self.cache_manager.get_piece(filename)
            if cached_entry:
                return cached_entry, None

            # Load asynchronously
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                data = json.loads(content)

            # Cache the result
            self.cache_manager.set_piece(filename, data, str(file_path))
            return data, None

        except Exception as e:
            error_msg = f"Error loading piece async {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    async def load_ability_async(
        self, filename: str
    ) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load ability data asynchronously for better performance"""
        try:
            from config import ABILITIES_DIR

            file_path = Path(ABILITIES_DIR) / f"{filename}.json"

            # Check cache first
            f"ability_{filename}"
            cached_entry = self.cache_manager.get_ability(filename)
            if cached_entry:
                return cached_entry, None

            # Load asynchronously
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                data = json.loads(content)

            # Cache the result
            self.cache_manager.set_ability(filename, data, str(file_path))
            return data, None

        except Exception as e:
            error_msg = f"Error loading ability async {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    async def save_piece_async(
        self, piece_data: Dict[str, Any], filename: str
    ) -> Tuple[bool, Optional[str]]:
        """Save piece data asynchronously for better performance"""
        try:
            from config import PIECES_DIR

            file_path = Path(PIECES_DIR) / f"{filename}.json"

            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Save asynchronously
            content = json.dumps(piece_data, indent=2, ensure_ascii=False)
            async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                await f.write(content)

            # Update cache
            self.cache_manager.set_piece(filename, piece_data, str(file_path))
            return True, None

        except Exception as e:
            error_msg = f"Error saving piece async {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg

    async def save_ability_async(
        self, ability_data: Dict[str, Any], filename: str
    ) -> Tuple[bool, Optional[str]]:
        """Save ability data asynchronously for better performance"""
        try:
            from config import ABILITIES_DIR

            file_path = Path(ABILITIES_DIR) / f"{filename}.json"

            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Save asynchronously
            content = json.dumps(ability_data, indent=2, ensure_ascii=False)
            async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                await f.write(content)

            # Update cache
            self.cache_manager.set_ability(filename, ability_data, str(file_path))
            return True, None

        except Exception as e:
            error_msg = f"Error saving ability async {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg


# ========== LAZY EDITOR INTEGRATION ==========


class LazyPieceEditorPatches:
    """
    Patches for piece editor to enable lazy loading
    """

    @staticmethod
    def patch_refresh_file_lists(editor_instance):
        """
        Patch the refresh_file_lists method to use lazy loading
        """
        original_method = getattr(editor_instance, "refresh_file_lists", None)

        def lazy_refresh_file_lists():
            """Enhanced refresh with lazy loading"""
            try:
                # Import here to avoid circular imports
                try:
                    from .lazy_loading_system import LazyComboBox, get_lazy_data_manager

                    data_manager = get_lazy_data_manager()
                except ImportError:
                    logger.warning("Lazy loading system not available")
                    if original_method:
                        original_method()
                    return

                # Get pieces manager
                data_manager.get_pieces_manager()

                # Check if editor has load_combo attribute
                if hasattr(editor_instance, "load_combo"):
                    # Replace with lazy combo if not already done
                    if not isinstance(editor_instance.load_combo, LazyComboBox):
                        try:
                            from config import PIECES_DIR

                            pieces_dir = PIECES_DIR
                        except ImportError:
                            pieces_dir = "data/pieces"

                        # Store current selection
                        current_text = editor_instance.load_combo.currentText()

                        # Replace with lazy combo
                        parent = editor_instance.load_combo.parent()
                        layout = parent.layout()

                        # Remove old combo
                        layout.removeWidget(editor_instance.load_combo)
                        editor_instance.load_combo.deleteLater()

                        # Create new lazy combo
                        editor_instance.load_combo = LazyComboBox(pieces_dir)
                        layout.addWidget(editor_instance.load_combo)

                        # Connect signals
                        editor_instance.load_combo.item_selected_lazy.connect(
                            lambda filename, data: editor_instance.on_piece_loaded_lazy(
                                filename, data
                            )
                        )

                        # Restore selection if possible
                        if current_text:
                            index = editor_instance.load_combo.findText(current_text)
                            if index >= 0:
                                editor_instance.load_combo.setCurrentIndex(index)

                # Start background preloading
                data_manager.preload_recent_files(max_files=10)

                logger.info("Piece editor file lists refreshed with lazy loading")

            except Exception as e:
                logger.error(f"Error in lazy refresh_file_lists: {e}")
                # Fallback to original method if available
                if original_method:
                    original_method()

        # Replace the method
        editor_instance.refresh_file_lists = lazy_refresh_file_lists

        # Add lazy loading callback method
        def on_piece_loaded_lazy(filename: str, data: Dict[str, Any]):
            """Handle piece loaded via lazy loading"""
            try:
                # Call existing load logic if available
                if hasattr(editor_instance, "load_piece_data"):
                    editor_instance.load_piece_data(data)
                elif hasattr(editor_instance, "populate_ui_from_data"):
                    editor_instance.populate_ui_from_data(data)

                logger.info(f"Piece loaded lazily: {filename}")

            except Exception as e:
                logger.error(f"Error handling lazy piece load: {e}")

        # Add the method to the editor instance
        editor_instance.on_piece_loaded_lazy = on_piece_loaded_lazy

    @staticmethod
    def patch_save_piece(editor_instance):
        """
        Patch the save piece method to use enhanced caching
        """
        original_method = getattr(editor_instance, "save_piece", None)

        def enhanced_save_piece(*args, **kwargs):
            """Enhanced save with caching"""
            try:
                # Call original save method
                result = None
                if original_method:
                    result = original_method(*args, **kwargs)

                # Invalidate cache for this piece
                try:
                    from lazy_loading_system import get_lazy_data_manager

                    data_manager = get_lazy_data_manager()

                    # Get filename from args or editor state
                    filename = None
                    if args:
                        filename = args[0]
                    elif hasattr(editor_instance, "current_filename"):
                        filename = editor_instance.current_filename

                    if filename and hasattr(data_manager, "lazy_manager"):
                        # Invalidate cache entry
                        cache_key = f"piece_{filename}"
                        if hasattr(data_manager.lazy_manager, "cache_manager"):
                            data_manager.lazy_manager.cache_manager.invalidate(
                                cache_key
                            )

                except Exception as cache_error:
                    logger.warning(
                        f"Could not invalidate cache after save: {cache_error}"
                    )

                return result

            except Exception as e:
                logger.error(f"Error in enhanced save_piece: {e}")
                # Fallback to original method
                if original_method:
                    return original_method(*args, **kwargs)

        # Replace the method
        editor_instance.save_piece = enhanced_save_piece


class LazyAbilityEditorPatches:
    """
    Patches for ability editor to enable lazy loading
    """

    @staticmethod
    def patch_refresh_file_lists(editor_instance):
        """
        Patch the refresh_file_lists method to use lazy loading for abilities
        """
        original_method = getattr(editor_instance, "refresh_file_lists", None)

        def lazy_refresh_file_lists():
            """Enhanced refresh with lazy loading for abilities"""
            try:
                # Import here to avoid circular imports
                try:
                    from .lazy_loading_system import LazyComboBox, get_lazy_data_manager

                    data_manager = get_lazy_data_manager()
                except ImportError:
                    logger.warning("Lazy loading system not available")
                    if original_method:
                        original_method()
                    return

                # Get abilities manager
                data_manager.get_abilities_manager()

                # Check if editor has load_combo attribute
                if hasattr(editor_instance, "load_combo"):
                    # Replace with lazy combo if not already done
                    if not isinstance(editor_instance.load_combo, LazyComboBox):
                        try:
                            from config import ABILITIES_DIR

                            abilities_dir = ABILITIES_DIR
                        except ImportError:
                            abilities_dir = "data/abilities"

                        # Store current selection
                        current_text = editor_instance.load_combo.currentText()

                        # Replace with lazy combo
                        parent = editor_instance.load_combo.parent()
                        layout = parent.layout()

                        # Remove old combo
                        layout.removeWidget(editor_instance.load_combo)
                        editor_instance.load_combo.deleteLater()

                        # Create new lazy combo
                        editor_instance.load_combo = LazyComboBox(abilities_dir)
                        layout.addWidget(editor_instance.load_combo)

                        # Connect signals
                        editor_instance.load_combo.item_selected_lazy.connect(
                            lambda filename, data: editor_instance.on_ability_loaded_lazy(
                                filename, data
                            )
                        )

                        # Restore selection if possible
                        if current_text:
                            index = editor_instance.load_combo.findText(current_text)
                            if index >= 0:
                                editor_instance.load_combo.setCurrentIndex(index)

                # Start background preloading
                data_manager.preload_recent_files(max_files=10)

                logger.info("Ability editor file lists refreshed with lazy loading")

            except Exception as e:
                logger.error(f"Error in lazy refresh_file_lists for abilities: {e}")
                # Fallback to original method if available
                if original_method:
                    original_method()

        # Replace the method
        editor_instance.refresh_file_lists = lazy_refresh_file_lists

        # Add lazy loading callback method
        def on_ability_loaded_lazy(filename: str, data: Dict[str, Any]):
            """Handle ability loaded via lazy loading"""
            try:
                # Call existing load logic if available
                if hasattr(editor_instance, "load_ability_data"):
                    editor_instance.load_ability_data(data)
                elif hasattr(editor_instance, "populate_ui_from_data"):
                    editor_instance.populate_ui_from_data(data)

                logger.info(f"Ability loaded lazily: {filename}")

            except Exception as e:
                logger.error(f"Error handling lazy ability load: {e}")

        # Add the method to the editor instance
        editor_instance.on_ability_loaded_lazy = on_ability_loaded_lazy


def apply_lazy_loading_patches(editor_instance, editor_type: str = "piece"):
    """
    Apply lazy loading patches to an editor instance

    Args:
        editor_instance: The editor instance to patch
        editor_type: Type of editor ("piece" or "ability")
    """
    try:
        if editor_type.lower() == "piece":
            LazyPieceEditorPatches.patch_refresh_file_lists(editor_instance)
            LazyPieceEditorPatches.patch_save_piece(editor_instance)
            logger.info("Applied lazy loading patches to piece editor")
        elif editor_type.lower() == "ability":
            LazyAbilityEditorPatches.patch_refresh_file_lists(editor_instance)
            logger.info("Applied lazy loading patches to ability editor")
        else:
            logger.warning(f"Unknown editor type: {editor_type}")

    except Exception as e:
        logger.error(f"Error applying lazy loading patches: {e}")


def create_lazy_file_selector_widget(
    directory: str, title: str = "Select File"
) -> QWidget:
    """
    Create a lazy file selector widget

    Args:
        directory: Directory to scan for files
        title: Title for the selector

    Returns:
        QWidget with lazy file selection capabilities
    """
    try:
        from .lazy_loading_system import LazyFileSelector

        return LazyFileSelector(directory, title)
    except ImportError:
        logger.warning("Lazy loading system not available, creating basic widget")
        # Fallback to basic widget
        widget = QWidget()
        layout = QVBoxLayout()
        from PyQt6.QtWidgets import QLabel

        layout.addWidget(QLabel(f"{title} (Basic Mode)"))
        widget.setLayout(layout)
        return widget
