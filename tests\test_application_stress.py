#!/usr/bin/env python3
"""
Application Stress Test for Adventure Chess Creator

This test suite performs stress testing to verify the application
runs smoothly under various load conditions and data operations.
"""

import unittest
import os
import sys
import tempfile
import json
import time
import gc
from typing import Dict, Any, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestApplicationStress(unittest.TestCase):
    """Stress test the application under various conditions"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_data_dir = "data"
        self.test_pieces_dir = os.path.join(self.test_data_dir, "pieces")
        self.test_abilities_dir = os.path.join(self.test_data_dir, "abilities")
    
    def test_data_manager_stress(self):
        """Stress test data managers with multiple operations"""
        try:
            from utils.simple_bridge import SimpleBridge
            
            # Test rapid piece loading
            pieces = SimpleBridge.list_pieces()
            self.assertGreater(len(pieces), 0, "No pieces found for testing")
            
            for i, piece_name in enumerate(pieces[:5]):  # Test first 5 pieces
                piece_data, error = SimpleBridge.load_piece_for_ui(piece_name)
                self.assertIsNone(error, f"Error loading piece {piece_name}: {error}")
                self.assertIsNotNone(piece_data, f"No data returned for piece {piece_name}")
                
                # Verify essential piece data structure
                self.assertIn("name", piece_data, f"Piece {piece_name} missing name field")
                self.assertIn("movement", piece_data, f"Piece {piece_name} missing movement field")
            
            # Test rapid ability loading
            abilities = SimpleBridge.list_abilities()
            self.assertGreater(len(abilities), 0, "No abilities found for testing")
            
            for i, ability_name in enumerate(abilities[:5]):  # Test first 5 abilities
                ability_data, error = SimpleBridge.load_ability_for_ui(ability_name)
                self.assertIsNone(error, f"Error loading ability {ability_name}: {error}")
                self.assertIsNotNone(ability_data, f"No data returned for ability {ability_name}")
                
                # Verify essential ability data structure
                self.assertIn("name", ability_data, f"Ability {ability_name} missing name field")
            
            print("✅ Data manager stress test passed")
        except Exception as e:
            self.fail(f"Data manager stress test failed: {e}")
    
    def test_core_module_imports_stress(self):
        """Stress test core module imports"""
        core_modules = [
            "core.ui.ui_utils",
            "core.ui.inline_selection_widgets", 
            "core.ui.visual_feedback_integration",
            "core.managers.data_ownership_registry",
            "core.interfaces.single_source_data_interface",
            "core.workflow.template_system",
            "core.performance.editor_integration",
            "core.validation.validation_rules",
            "core.error_handling.error_handling_system"
        ]
        
        # Import each module multiple times to test stability
        for iteration in range(3):
            for module_name in core_modules:
                try:
                    __import__(module_name)
                except ImportError as e:
                    self.fail(f"Failed to import {module_name} on iteration {iteration}: {e}")
        
        print("✅ Core module import stress test passed")
    
    def test_json_formatter_stress(self):
        """Stress test the readable JSON formatter"""
        try:
            from utils.readable_json_formatter import save_with_readable_patterns
            
            # Create test data with movement patterns
            test_data = {
                "name": "StressTestPiece",
                "movement": {
                    "pattern": [[0,0,0,1,0,0,0,0] for _ in range(8)]
                },
                "abilities": [
                    {
                        "name": "TestAbility",
                        "movement": {
                            "pattern": [[1,1,1,0,0,0,0,0] for _ in range(8)]
                        }
                    }
                ]
            }
            
            # Test multiple saves with different data
            temp_files = []
            try:
                for i in range(5):
                    test_data["name"] = f"StressTestPiece_{i}"
                    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                    temp_files.append(temp_file.name)
                    temp_file.close()
                    
                    save_with_readable_patterns(test_data, temp_file.name)
                    
                    # Verify file was created and is valid JSON
                    with open(temp_file.name, 'r') as read_f:
                        loaded_data = json.load(read_f)
                        self.assertEqual(loaded_data["name"], f"StressTestPiece_{i}")
                        
            finally:
                # Clean up temp files
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                    except:
                        pass  # Ignore cleanup errors
            
            print("✅ JSON formatter stress test passed")
        except Exception as e:
            self.fail(f"JSON formatter stress test failed: {e}")
    
    def test_memory_usage_patterns(self):
        """Test memory usage patterns during operations"""
        try:
            from utils.simple_bridge import SimpleBridge
            
            # Force garbage collection before test
            gc.collect()
            
            # Perform multiple data operations
            for i in range(10):
                pieces = SimpleBridge.list_pieces()
                abilities = SimpleBridge.list_abilities()
                
                # Load a few pieces and abilities
                if pieces:
                    piece_data, _ = SimpleBridge.load_piece_for_ui(pieces[0])
                if abilities:
                    ability_data, _ = SimpleBridge.load_ability_for_ui(abilities[0])
                
                # Force garbage collection every 3 iterations
                if i % 3 == 0:
                    gc.collect()
            
            print("✅ Memory usage pattern test passed")
        except Exception as e:
            self.fail(f"Memory usage pattern test failed: {e}")
    
    def test_concurrent_data_access(self):
        """Test concurrent-like data access patterns"""
        try:
            from utils.simple_bridge import SimpleBridge
            
            # Simulate concurrent access by rapidly switching between operations
            pieces = SimpleBridge.list_pieces()
            abilities = SimpleBridge.list_abilities()
            
            operations = []
            for i in range(20):
                if i % 2 == 0 and pieces:
                    operations.append(('piece', pieces[i % len(pieces)]))
                elif abilities:
                    operations.append(('ability', abilities[i % len(abilities)]))
            
            # Execute operations rapidly
            for op_type, filename in operations:
                if op_type == 'piece':
                    data, error = SimpleBridge.load_piece_for_ui(filename)
                    self.assertIsNone(error, f"Error in concurrent piece access: {error}")
                else:
                    data, error = SimpleBridge.load_ability_for_ui(filename)
                    self.assertIsNone(error, f"Error in concurrent ability access: {error}")
            
            print("✅ Concurrent data access test passed")
        except Exception as e:
            self.fail(f"Concurrent data access test failed: {e}")
    
    def test_performance_timing(self):
        """Test performance timing of key operations"""
        try:
            from utils.simple_bridge import SimpleBridge
            
            pieces = SimpleBridge.list_pieces()
            abilities = SimpleBridge.list_abilities()
            
            if pieces:
                # Time piece loading
                start_time = time.time()
                piece_data, error = SimpleBridge.load_piece_for_ui(pieces[0])
                load_time = time.time() - start_time
                
                self.assertIsNone(error, "Piece loading should not error")
                self.assertLess(load_time, 1.0, "Piece loading should be under 1 second")
            
            if abilities:
                # Time ability loading
                start_time = time.time()
                ability_data, error = SimpleBridge.load_ability_for_ui(abilities[0])
                load_time = time.time() - start_time
                
                self.assertIsNone(error, "Ability loading should not error")
                self.assertLess(load_time, 1.0, "Ability loading should be under 1 second")
            
            print("✅ Performance timing test passed")
        except Exception as e:
            self.fail(f"Performance timing test failed: {e}")

def run_stress_tests():
    """Run comprehensive stress tests"""
    print("🔥 Running Application Stress Tests...")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestApplicationStress)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All stress tests passed!")
        print("✅ Application runs smoothly under stress conditions")
        print("✅ Data integrity maintained under load")
    else:
        print("❌ Some stress tests failed")
        print("❌ Application may have stability issues")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(f"  {failure[1]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(f"  {error[1]}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_stress_tests()
    sys.exit(0 if success else 1)
