# Single Source of Truth Implementation

## Overview

The Single Source of Truth (SSOT) pattern ensures that every piece of data in the Adventure Chess Creator has exactly one authoritative location where it is stored and managed. This eliminates data inconsistencies, reduces bugs, and simplifies maintenance.

## Core Principles

### 1. One Authoritative Source
Every data type has exactly one component responsible for its storage and management:
- **Piece Data**: Stored in `PieceEditor.current_data`
- **Ability Data**: Stored in `AbilityEditor.current_data`
- **Movement Data**: Stored in `PieceEditor.current_movement_data`
- **UI State**: Stored in respective editor instances

### 2. Property-Based Redirection
Legacy fields are implemented as properties that redirect to the canonical source:

```python
# Legacy field access redirects to canonical source
@property
def current_custom_pattern(self):
    """Get current custom pattern from movement data"""
    return self.current_movement_data.get("pattern", [[0 for _ in range(8)] for _ in range(8)])

@current_custom_pattern.setter
def current_custom_pattern(self, value):
    """Set current custom pattern in movement data"""
    self.current_movement_data["pattern"] = value
```

### 3. Data Ownership Registry
The `DataOwnershipRegistry` maintains authoritative rules about data ownership:

```python
# Example ownership rule
DataOwnershipRule(
    data_type="movement_data",
    owner_component="PieceEditor",
    owner_property="current_movement_data",
    access_pattern=DataAccessPattern.COMPUTED_PROPERTY,
    description="Movement pattern, type, and piece position data",
    legacy_aliases=["current_custom_pattern", "selected_movement_type", "custom_pattern_piece_pos"]
)
```

## Implementation Components

### Data Ownership Registry (`core/managers/data_ownership_registry.py`)
- Central registry of all data ownership rules
- Maps data types to their authoritative owners
- Tracks legacy aliases and their canonical mappings
- Provides validation for data access patterns

### Single Source Data Interface (`core/interfaces/single_source_data_interface.py`)
- Unified interface for all data access operations
- Enforces ownership rules and access patterns
- Provides legacy compatibility through alias redirection
- Handles change notification and validation

### Base Editor Integration (`core/base_classes/base_editor.py`)
- Validates data ownership compliance
- Provides ownership information for debugging
- Integrates with the data ownership registry
- Ensures consistent data management patterns

## Data Access Patterns

### 1. Direct Property Access
Simple data accessed directly through object properties:
```python
# Accessing piece data
piece_name = editor.current_data.get('name')
editor.current_data['name'] = 'New Name'
```

### 2. Computed Property Access
Legacy fields that compute values from canonical sources:
```python
# Legacy access redirects to canonical source
pattern = editor.current_custom_pattern  # Gets from current_movement_data["pattern"]
editor.selected_movement_type = "knight"  # Sets current_movement_data["type"]
```

### 3. Manager-Mediated Access
Complex data accessed through specialized managers:
```python
# Tag data accessed through tag manager
tag_data = editor.tag_manager.collect_tag_data()
editor.tag_manager.populate_tag_data(data)
```

### 4. Interface-Mediated Access
Standardized data accessed through unified interfaces:
```python
# UI data accessed through data interface
ui_data = editor.data_interface.collect_data_from_ui(editor, "piece")
editor.data_interface.populate_ui_from_data(editor, data, "piece")
```

## Benefits

### 1. Data Consistency
- No duplicate storage of the same information
- Changes automatically propagate to all access points
- Eliminates synchronization issues between related fields

### 2. Simplified Debugging
- Clear ownership makes it easy to find where data is stored
- Single point of modification for each data type
- Consistent access patterns across the entire application

### 3. Legacy Compatibility
- Existing code continues to work through property redirection
- Gradual migration path from old patterns to new ones
- No breaking changes to existing functionality

### 4. Maintainability
- Clear documentation of data ownership in the registry
- Standardized patterns for new data types
- Reduced cognitive load for developers

## Migration Strategy

### Phase 1: Identify Redundant Sources
- Audit existing codebase for duplicate data storage
- Map legacy fields to their canonical equivalents
- Document current data flow patterns

### Phase 2: Establish Canonical Sources
- Designate single authoritative location for each data type
- Create data ownership rules in the registry
- Implement property-based redirection for legacy fields

### Phase 3: Update Access Patterns
- Modify data handlers to use canonical sources
- Update UI components to access data consistently
- Ensure all operations go through the single source

### Phase 4: Validation and Testing
- Validate data ownership compliance
- Test legacy compatibility
- Verify data consistency across all operations

## Best Practices

### For New Data Types
1. Register ownership rule in the data ownership registry
2. Choose appropriate access pattern based on complexity
3. Document the canonical source and access methods
4. Provide clear validation rules

### For Legacy Compatibility
1. Implement properties that redirect to canonical sources
2. Log deprecation warnings for direct legacy access
3. Maintain backward compatibility during transition
4. Document migration path for legacy code

### For Data Validation
1. Validate data at the canonical source
2. Use the data ownership registry for access validation
3. Implement change listeners for dependent components
4. Ensure consistency across all access patterns

## Troubleshooting

### Common Issues
1. **Data Not Updating**: Check if accessing through canonical source
2. **Inconsistent Values**: Verify no duplicate storage exists
3. **Legacy Code Breaking**: Ensure property redirection is implemented
4. **Performance Issues**: Consider caching for computed properties

### Debugging Tools
- Use `get_data_ownership_info()` to check ownership compliance
- Check the data ownership registry for canonical mappings
- Validate access patterns through the single source interface
- Monitor change notifications for data flow understanding

## Related Documentation
- [Data Flow Patterns](data_flow.md)
- [Base Class Hierarchy](base_classes.md)
- [Data Ownership Patterns](../patterns/data_ownership.md)
- [Legacy Field Mappings](../cross_references/legacy_mappings.md)
