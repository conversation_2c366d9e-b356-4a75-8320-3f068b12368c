# Adventure Chess Creator - Standardized Theming System

## Overview

This document explains how to use the standardized theming system to ensure consistent visual styling across all components of the Adventure Chess Creator application.

## Problem Solved

- **Inconsistent styling**: Widgets created by AI or different developers no longer have mismatched themes
- **Scattered styles**: No more inline `setStyleSheet()` calls scattered throughout the codebase
- **Maintenance nightmare**: Centralized theme management makes it easy to update the entire app's appearance
- **Manual styling**: Automatic theme detection and application for common widget patterns

## Quick Start

### 1. Import the Theme System

```python
from core.ui import (
    apply_theme_to_widget,
    auto_theme_widget_children,
    create_themed_button,
    ThemeManager,
    apply_adventure_chess_theme
)
```

### 2. Basic Usage - Apply Theme to Existing Widgets

```python
# Apply specific theme to a widget
save_button = QPushButton("Save")
apply_theme_to_widget(save_button, 'primary_button')

# Automatically theme all children of a widget
dialog = QDialog()
# ... create child widgets ...
auto_theme_widget_children(dialog)  # Themes all children automatically
```

### 3. Use Pre-themed Widget Factories

```python
# Instead of creating widgets manually and styling them
save_btn = create_themed_button("Save", 'primary_button')
cancel_btn = create_themed_button("Cancel", 'danger_button')
title_label = create_themed_label("Settings", 'header_label')
```

## Available Themes

### Button Themes
- `'primary_button'` - Blue gradient, for primary actions (Save, Apply, OK)
- `'secondary_button'` - Gray gradient, for secondary actions (Load, Browse)
- `'success_button'` - Green gradient, for creation actions (New, Add, Create)
- `'danger_button'` - Red gradient, for destructive actions (Delete, Cancel)

### Input Themes
- `'line_edit'` - Standard text input styling
- `'text_edit'` - Multi-line text input styling
- `'combo_box'` - Dropdown styling with custom arrow

### Container Themes
- `'group_box'` - Styled container with title
- `'tab_widget'` - Tab container styling
- `'chess_board_frame'` - Special chess board container

### Label Themes
- `'header_label'` - Large, bold text for headers
- `'subheader_label'` - Medium weight for subheaders
- `'body_label'` - Standard text
- `'muted_label'` - Smaller, muted text for hints

## Integration Methods

### Method 1: Automatic Theme Detection (Recommended for New Code)

The system can automatically detect appropriate themes based on widget content:

```python
class MyDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # Apply themes to all widgets automatically
        auto_theme_widget_children(self)
    
    def setup_ui(self):
        # Create widgets normally
        self.save_btn = QPushButton("Save")      # Will get 'primary_button'
        self.delete_btn = QPushButton("Delete")  # Will get 'danger_button'
        self.name_edit = QLineEdit()             # Will get 'line_edit'
        # ... etc
```

### Method 2: Decorator Pattern (Great for Widget Factories)

```python
from core.ui import themed_widget

@themed_widget('primary_button')
def create_save_button():
    return QPushButton("💾 Save")

@themed_widget('danger_button') 
def create_delete_button():
    return QPushButton("🗑️ Delete")

# Use them
save_btn = create_save_button()  # Automatically themed
delete_btn = create_delete_button()  # Automatically themed
```

### Method 3: Context Manager (For Bulk Operations)

```python
from core.ui import theme_context

with theme_context(my_dialog):
    # All widgets created in this context will be auto-themed
    button1 = QPushButton("Save")
    button2 = QPushButton("Cancel") 
    label1 = QLabel("Title")
    # They're all themed when the context exits
```

### Method 4: Manual Application (For Specific Control)

```python
# Apply specific themes manually
apply_theme_to_widget(my_button, 'primary_button')
apply_theme_to_widget(my_label, 'header_label')
apply_theme_to_widget(my_input, 'line_edit')
```

## Migrating Existing Code

### Replace Existing Inline Styles

**Before:**
```python
button.setStyleSheet("""
    QPushButton {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #4488cc, stop:1 #3366aa);
        border: 3px solid #66aaff;
        color: white;
        /* ... lots more CSS ... */
    }
""")
```

**After:**
```python
apply_theme_to_widget(button, 'primary_button')
```

### Update Widget Creation Functions

**Before:**
```python
def create_toolbar_buttons(self):
    self.save_btn = QPushButton("Save")
    self.save_btn.setStyleSheet("/* custom CSS */")
    
    self.delete_btn = QPushButton("Delete") 
    self.delete_btn.setStyleSheet("/* more custom CSS */")
```

**After:**
```python
def create_toolbar_buttons(self):
    self.save_btn = create_themed_button("Save", 'primary_button')
    self.delete_btn = create_themed_button("Delete", 'danger_button')
```

### Gradual Migration Helper

For existing code with complex inline styles:

```python
# This will try to map existing styles to themes automatically
migrate_existing_styles(my_widget)
```

## Advanced Usage

### Custom Theme Variants

```python
# Create a custom variant of an existing theme
custom_button = create_themed_button(
    "Custom", 
    'primary_button',
    background='#ff5722',  # Override background color
    border_color='#d84315'  # Override border color
)
```

### Adventure Chess Specific Theming

```python
# Apply Adventure Chess specific enhancements
apply_adventure_chess_theme(my_widget)
```

### Theme-Aware Widgets

```python
from core.ui import ThemeAwareWidget

class MyWidget(QWidget, ThemeAwareWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_theme()  # Apply theme automatically
    
    def setup_ui(self):
        # Create UI normally
        pass
```

## Best Practices

### 1. Use Automatic Theming for Most Cases
```python
# Good: Let the system detect appropriate themes
auto_theme_widget_children(parent_widget)

# Avoid: Manual styling for every widget
# widget.setStyleSheet("...")  # Don't do this
```

### 2. Use Semantic Theme Names
```python
# Good: Semantic naming
apply_theme_to_widget(save_btn, 'primary_button')
apply_theme_to_widget(delete_btn, 'danger_button')

# Avoid: Non-semantic naming
# apply_theme_to_widget(btn, 'blue_button')  # What does "blue" mean?
```

### 3. Apply Themes After Widget Creation
```python
# Good: Create all widgets, then theme them
def setup_ui(self):
    # Create widgets
    self.button1 = QPushButton("Save")
    self.button2 = QPushButton("Cancel")
    self.input1 = QLineEdit()
    
    # Apply themes
    auto_theme_widget_children(self)

# Good alternative: Use themed factories
def setup_ui(self):
    self.button1 = create_themed_button("Save", 'primary_button')
    self.button2 = create_themed_button("Cancel", 'danger_button')
    self.input1 = create_themed_line_edit()
```

### 4. Adventure Chess Dialog Pattern
```python
class MyAdventureChessDialog(BaseChessBoardDialog):
    def setup_controls(self):
        # Create your controls normally
        super().setup_controls()
        
        # Apply Adventure Chess theming
        apply_adventure_chess_theme(self)
```

## Integration with AI Code Generation

When working with AI-generated code, add this pattern at the end of UI setup:

```python
def setup_ui(self):
    # ... AI-generated widget creation code ...
    
    # Apply consistent theming (add this line)
    auto_theme_widget_children(self)
```

Or use the context manager approach:

```python
def setup_ui(self):
    with theme_context(self):
        # ... AI-generated widget creation code ...
        # Themes will be applied automatically
```

## Color System

Access theme colors directly:

```python
from core.ui import get_color

# Get colors for custom styling
primary_color = get_color('primary')
text_color = get_color('text_primary')
background_color = get_color('background_primary')
```

## Theme Switching

```python
from core.ui import set_theme, ThemeVariant

# Switch to light theme
set_theme(ThemeVariant.LIGHT)

# Switch to dark theme (default)
set_theme(ThemeVariant.DARK)
```

## Common Patterns

### Dialog with Standard Buttons
```python
def create_dialog_buttons(self):
    buttons = create_theme_aware_dialog_buttons([
        {'text': 'Save', 'callback': self.accept},
        {'text': 'Cancel', 'style': 'danger_button', 'callback': self.reject},
    ])
    return buttons
```

### Toolbar Creation
```python
def create_toolbar(self):
    toolbar_layout = QHBoxLayout()
    
    # Use themed button factories
    new_btn = create_themed_button("📄 New", 'success_button')
    save_btn = create_themed_button("💾 Save", 'primary_button')
    delete_btn = create_themed_button("🗑️ Delete", 'danger_button')
    
    toolbar_layout.addWidget(new_btn)
    toolbar_layout.addWidget(save_btn)
    toolbar_layout.addWidget(delete_btn)
    
    return toolbar_layout
```

### Form Creation
```python
def create_form(self):
    form_layout = QFormLayout()
    
    # Use themed input factories
    name_input = create_themed_line_edit("Enter name...")
    description_input = create_themed_text_edit()
    role_combo = create_themed_combo_box()
    
    form_layout.addRow(create_themed_label("Name:", 'subheader_label'), name_input)
    form_layout.addRow(create_themed_label("Description:", 'subheader_label'), description_input)
    form_layout.addRow(create_themed_label("Role:", 'subheader_label'), role_combo)
    
    return form_layout
```

This theming system ensures that all widgets across your application have a consistent, professional appearance regardless of who (human or AI) creates them!
