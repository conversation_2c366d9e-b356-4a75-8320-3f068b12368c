"""
Real-time Validation Widgets for Adventure Chess Creator

This module provides real-time validation feedback widgets with debounced validation
and detailed error reporting.
"""

import logging
from typing import Any, Callable, Dict, List

from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtWidgets import (
    QFrame,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

from ..ui.color_schemes import ColorSchemes

logger = logging.getLogger(__name__)


class RealTimeValidationWidget(QWidget):
    """Real-time validation feedback widget"""

    validation_changed = pyqtSignal(bool, str)  # is_valid, message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.validation_rules = []
        self.current_data = {}
        self.setup_ui()

        # Validation timer for debouncing
        self.validation_timer = QTimer()
        self.validation_timer.setSingleShot(True)
        self.validation_timer.timeout.connect(self._perform_validation)

    def setup_ui(self):
        """Setup validation UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # Validation status
        self.status_frame = QFrame()
        self.status_frame.setFrameStyle(QFrame.Shape.Box)
        self.status_frame.setStyleSheet(
            f"""
            QFrame {{
                border: 1px solid {ColorSchemes.GRID_BORDER};
                border-radius: 6px;
                background-color: {ColorSchemes.CARD_BG};
                padding: 5px;
            }}
        """
        )

        status_layout = QHBoxLayout()

        self.status_icon = QLabel("✓")
        self.status_icon.setFixedSize(20, 20)
        self.status_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.status_message = QLabel("Ready for validation")
        self.status_message.setWordWrap(True)

        status_layout.addWidget(self.status_icon)
        status_layout.addWidget(self.status_message)
        self.status_frame.setLayout(status_layout)

        # Validation details (collapsible)
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout()
        self.details_widget.setLayout(self.details_layout)
        self.details_widget.hide()

        # Toggle button for details
        self.toggle_details_btn = QPushButton("Show Details")
        self.toggle_details_btn.clicked.connect(self.toggle_details)
        self.toggle_details_btn.setStyleSheet(
            """
            QPushButton {
                border: none;
                color: #007bff;
                text-decoration: underline;
                font-size: 11px;
                padding: 2px;
            }
            QPushButton:hover {
                color: #0056b3;
            }
        """
        )

        layout.addWidget(self.status_frame)
        layout.addWidget(self.toggle_details_btn)
        layout.addWidget(self.details_widget)
        self.setLayout(layout)

    def add_validation_rule(self, rule_name: str, validator: Callable[[Dict], tuple]):
        """
        Add a validation rule

        Args:
            rule_name: Name of the validation rule
            validator: Function that takes data dict and returns (is_valid, message)
        """
        self.validation_rules.append((rule_name, validator))

    def update_data(self, data: Dict[str, Any]):
        """Update data and trigger validation"""
        self.current_data = data.copy()

        # Debounce validation - wait 500ms after last update
        self.validation_timer.stop()
        self.validation_timer.start(500)

    def _perform_validation(self):
        """Perform validation on current data"""
        all_valid = True
        validation_results = []

        for rule_name, validator in self.validation_rules:
            try:
                is_valid, message = validator(self.current_data)
                validation_results.append((rule_name, is_valid, message))
                if not is_valid:
                    all_valid = False
            except Exception as e:
                validation_results.append((rule_name, False, f"Validation error: {e}"))
                all_valid = False

        self._update_validation_display(all_valid, validation_results)
        self.validation_changed.emit(
            all_valid, self._get_summary_message(validation_results)
        )

    def _update_validation_display(self, all_valid: bool, results: List[tuple]):
        """Update the validation display"""
        if all_valid:
            self.status_icon.setText("✓")
            self.status_icon.setStyleSheet(
                f"color: {ColorSchemes.SUCCESS}; font-weight: bold;"
            )
            self.status_message.setText("All validations passed")
            self.status_frame.setStyleSheet(
                f"""
                QFrame {{
                    border: 1px solid {ColorSchemes.SUCCESS};
                    border-radius: 6px;
                    background-color: {ColorSchemes.VALID};
                    padding: 5px;
                }}
            """
            )
        else:
            self.status_icon.setText("✗")
            self.status_icon.setStyleSheet(
                f"color: {ColorSchemes.ERROR}; font-weight: bold;"
            )

            # Count failed validations
            failed_count = sum(1 for _, is_valid, _ in results if not is_valid)
            self.status_message.setText(f"{failed_count} validation(s) failed")
            self.status_frame.setStyleSheet(
                f"""
                QFrame {{
                    border: 1px solid {ColorSchemes.ERROR};
                    border-radius: 6px;
                    background-color: {ColorSchemes.INVALID};
                    padding: 5px;
                }}
            """
            )

        # Update details
        self._update_details(results)

    def _update_details(self, results: List[tuple]):
        """Update validation details"""
        # Clear existing details
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)

        # Add new details
        for rule_name, is_valid, message in results:
            detail_frame = QFrame()
            detail_layout = QHBoxLayout()

            icon = QLabel("✓" if is_valid else "✗")
            icon.setFixedSize(16, 16)
            icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon.setStyleSheet(
                f"""
                color: {ColorSchemes.SUCCESS if is_valid else ColorSchemes.ERROR};
                font-weight: bold;
                font-size: 12px;
            """
            )

            rule_label = QLabel(f"{rule_name}: {message}")
            rule_label.setWordWrap(True)
            rule_label.setStyleSheet("font-size: 11px; padding: 2px;")

            detail_layout.addWidget(icon)
            detail_layout.addWidget(rule_label)
            detail_frame.setLayout(detail_layout)

            self.details_layout.addWidget(detail_frame)

    def _get_summary_message(self, results: List[tuple]) -> str:
        """Get summary validation message"""
        failed_rules = [rule_name for rule_name, is_valid, _ in results if not is_valid]
        if failed_rules:
            return f"Validation failed: {', '.join(failed_rules)}"
        else:
            return "All validations passed"

    def toggle_details(self):
        """Toggle validation details visibility"""
        if self.details_widget.isVisible():
            self.details_widget.hide()
            self.toggle_details_btn.setText("Show Details")
        else:
            self.details_widget.show()
            self.toggle_details_btn.setText("Hide Details")

    def validate_all(self):
        """
        Trigger immediate validation of all rules.

        This method is called by the visual feedback system to perform
        immediate validation without waiting for the debounce timer.
        """
        self.validation_timer.stop()  # Cancel any pending validation
        self._perform_validation()  # Perform validation immediately
