"""
Pydantic models for individual ability tags
Each tag has its own model with specific fields, defaults, and validation
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import Field, field_validator, model_validator

from .base import (
    BaseAdventureChessModel,
    Coordinate,
    Pattern8x8,
    RangeMask8x8,
    DisplacementDirection,
    TargetType,
    AreaEffectShape,
    VisionType,
    ReactionEventType,
    Points,
    Distance,
    Duration,
    create_default_pattern,
    create_default_range_mask,
    validate_pattern_8x8,
    validate_range_mask_8x8
)


# ========== ACTION ABILITIES ==========

class MoveTag(BaseAdventureChessModel):
    """Move ability tag - teleports piece to target square within range"""
    # Move has no additional configuration - uses only range
    pass


class SummonTag(BaseAdventureChessModel):
    """Summon ability tag - creates new pieces at target locations"""
    summon_list: List[Dict[str, Any]] = Field(default_factory=list, description="List of pieces to summon", alias="summonList")
    summon_max: int = Field(default=1, ge=1, le=10, description="Maximum pieces per use", alias="summonMax")

    @field_validator('summon_list')
    @classmethod
    def validate_summon_list(cls, v):
        """Validate summon list structure"""
        if not isinstance(v, list):
            raise ValueError("Summon list must be a list")

        for i, item in enumerate(v):
            if not isinstance(item, dict):
                raise ValueError(f"Summon list item {i} must be a dictionary")
            if 'piece' not in item:
                raise ValueError(f"Summon list item {i} must have a 'piece' field")

        return v


class RevivalTag(BaseAdventureChessModel):
    """Revival ability tag - resurrects destroyed pieces at target locations"""
    revival_list: List[Dict[str, Any]] = Field(default_factory=list, description="List of pieces to revive", alias="revivalList")
    revival_max: int = Field(default=1, ge=1, le=10, description="Maximum pieces per use", alias="revivalMax")
    revival_max_pieces_per_turn: int = Field(default=1, ge=1, le=10, description="Maximum pieces per turn")
    revival_sacrifice_mode: bool = Field(default=False, description="Use piece cost for revival")
    revival_max_cost: int = Field(default=0, ge=0, description="Maximum points for sacrifice mode")
    revival_with_starting_points: bool = Field(default=False, description="Revive with starting points")
    revival_points_amount: int = Field(default=0, ge=0, description="Points to revive with")
    revival_use_starting_points: bool = Field(default=False, description="Use piece's starting points")
    revival_within_turn: int = Field(default=0, ge=0, description="Must revive within N turns")


class CaptureTag(BaseAdventureChessModel):
    """Capture ability tag - destroys pieces at target locations"""
    capture_target: TargetType = Field(default=TargetType.ENEMY, description="Which pieces can be captured", alias="captureTarget")


class CarryPieceTag(BaseAdventureChessModel):
    """Carry piece ability tag - allows piece to carry other pieces while moving"""
    carry_list: List[Dict[str, Any]] = Field(default_factory=list, description="List of pieces that can be carried", alias="carryList")
    carry_range: int = Field(default=0, ge=0, le=8, description="Distance for picking up pieces (0=self)", alias="carryRange")
    carry_range_pattern: Optional[RangeMask8x8] = Field(default=None, description="Custom carry range pattern")
    carry_range_piece_position: Optional[List[int]] = Field(default=None, description="Piece position for carry range")
    carry_drop_on_death: bool = Field(default=False, description="Drop carried pieces when carrier dies", alias="carryDropOnDeath")
    carry_drop_mode: str = Field(default="random", description="How to drop pieces (random/selectable)")
    carry_drop_range: int = Field(default=1, ge=0, le=8, description="Distance for dropping pieces")
    carry_drop_range_pattern: Optional[RangeMask8x8] = Field(default=None, description="Custom drop range pattern")
    carry_drop_range_piece_position: Optional[List[int]] = Field(default=None, description="Piece position for drop range")
    carry_drop_can_capture: bool = Field(default=False, description="Dropped pieces can capture")
    carry_share_abilities: bool = Field(default=False, description="Carried pieces share abilities", alias="carryShareAbilities")
    carry_starting_piece: bool = Field(default=False, description="Piece starts carrying another piece", alias="carryStartingPiece")
    
    @field_validator('carry_range_pattern', 'carry_drop_range_pattern')
    @classmethod
    def validate_range_patterns(cls, v):
        """Validate range patterns"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v


class SwapPlacesTag(BaseAdventureChessModel):
    """Swap places ability tag - exchanges positions with target piece"""
    swap_list: List[Dict[str, Any]] = Field(default_factory=list, description="List of pieces that can be swapped with", alias="swapList")

    @field_validator('swap_list')
    @classmethod
    def validate_swap_list(cls, v):
        """Validate swap list structure"""
        if not isinstance(v, list):
            raise ValueError("Swap list must be a list")

        for i, item in enumerate(v):
            if not isinstance(item, dict):
                raise ValueError(f"Swap list item {i} must be a dictionary")
            if 'piece' not in item:
                raise ValueError(f"Swap list item {i} must have a 'piece' field")

        return v


class DisplacePieceTag(BaseAdventureChessModel):
    """Displace piece ability tag - pushes target piece in specified direction"""
    direction: DisplacementDirection = Field(default=DisplacementDirection.N, description="Direction to push piece", alias="displaceDirection")
    distance: Distance = Field(default=1, description="Distance to push piece", alias="displaceDistance")
    displace_target_list: List[Dict[str, Any]] = Field(default_factory=list, description="Valid displacement targets", alias="displaceTargetList")
    displace_target_type: str = Field(default="Enemy", description="Type of pieces to displace", alias="displaceTargetType")
    custom_displacement_enabled: bool = Field(default=False, description="Use custom displacement pattern", alias="displaceCustom")
    custom_displacement_map: Optional[RangeMask8x8] = Field(default=None, description="Custom displacement pattern", alias="displaceCustomPattern")
    
    @field_validator('custom_displacement_map')
    @classmethod
    def validate_displacement_map(cls, v):
        """Validate custom displacement map"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v


class ImmobilizeTag(BaseAdventureChessModel):
    """Immobilize ability tag - prevents piece movement for specified turns"""
    duration: Duration = Field(default=1, description="Turns to immobilize", alias="immobilizeDuration")
    duration_enabled: bool = Field(default=True, description="Whether duration is enabled", alias="immobilizeDurationEnabled")
    immobilize_target_list: List[Dict[str, Any]] = Field(default_factory=list, description="Valid immobilize targets", alias="immobilizeTargetList")


class ConvertPieceTag(BaseAdventureChessModel):
    """Convert piece ability tag - changes target pieces to different side/color"""
    convert_target_list: List[Dict[str, Any]] = Field(default_factory=list, description="Valid conversion targets", alias="convertTargetList")
    convert_target_type: str = Field(default="Enemy", description="Type of pieces to convert", alias="convertTargetType")


class DuplicateTag(BaseAdventureChessModel):
    """Duplicate ability tag - creates copies of piece at offset positions"""
    location_offset: Optional[RangeMask8x8] = Field(default=None, description="Relative positions for duplicates", alias="duplicateOffset")
    limit: int = Field(default=1, ge=1, le=8, description="Maximum duplicates", alias="duplicateLimitAmount")
    limit_enabled: bool = Field(default=False, description="Whether limit is enabled", alias="duplicateLimit")
    
    @field_validator('location_offset')
    @classmethod
    def validate_location_offset(cls, v):
        """Validate location offset pattern"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v


class BuffPieceTag(BaseAdventureChessModel):
    """Buff piece ability tag - temporarily enhances target pieces"""
    buff_target_list: List[Dict[str, Any]] = Field(default_factory=list, description="Valid buff targets", alias="buffTargetList")
    buff_duration: Duration = Field(default=1, description="Effect duration", alias="buffDuration")
    buff_abilities: List[Dict[str, Any]] = Field(default_factory=list, description="Abilities to add", alias="buffAbilities")
    buff_movement_attack_pattern: Optional[Pattern8x8] = Field(default=None, description="Modified movement/attack pattern")
    
    @field_validator('buff_movement_attack_pattern')
    @classmethod
    def validate_buff_pattern(cls, v):
        """Validate buff movement pattern"""
        if v is not None:
            return validate_pattern_8x8(v)
        return v


class DebuffPieceTag(BaseAdventureChessModel):
    """Debuff piece ability tag - temporarily weakens target pieces"""
    debuff_target_list: List[Dict[str, Any]] = Field(default_factory=list, description="Valid debuff targets", alias="debuffTargetList")
    debuff_duration: Duration = Field(default=1, description="Effect duration", alias="debuffDuration")
    debuff_prevent_abilities: List[Dict[str, Any]] = Field(default_factory=list, description="Abilities to remove", alias="debuffPreventAbilities")
    debuff_prevent_los: bool = Field(default=False, description="Prevent line of sight abilities")
    debuff_movement_attack_pattern: Optional[Pattern8x8] = Field(default=None, description="Modified movement/attack pattern")
    
    @field_validator('debuff_movement_attack_pattern')
    @classmethod
    def validate_debuff_pattern(cls, v):
        """Validate debuff movement pattern"""
        if v is not None:
            return validate_pattern_8x8(v)
        return v


class AddObstacleTag(BaseAdventureChessModel):
    """Add obstacle ability tag - places obstacles on target squares"""
    obstacle_type: str = Field(default="wall", description="Type of obstacle to add", alias="obstacleType")

    @field_validator('obstacle_type')
    @classmethod
    def validate_obstacle_type(cls, v):
        """Validate obstacle type"""
        valid_types = ["wall", "spike", "crystal", "ice", "fire", "portal"]
        if v not in valid_types:
            raise ValueError(f"Invalid obstacle type: {v}. Must be one of {valid_types}")
        return v


class RemoveObstacleTag(BaseAdventureChessModel):
    """Remove obstacle ability tag - removes obstacles from target squares"""
    obstacle_type: str = Field(default="any", description="Type of obstacle to remove", alias="removeObstacleType")

    @field_validator('obstacle_type')
    @classmethod
    def validate_obstacle_type(cls, v):
        """Validate obstacle type"""
        valid_types = ["wall", "spike", "crystal", "ice", "fire", "portal", "any"]
        if v not in valid_types:
            raise ValueError(f"Invalid obstacle type: {v}. Must be one of {valid_types}")
        return v


class TrapTileTag(BaseAdventureChessModel):
    """Trap tile ability tag - creates hidden traps on target squares"""
    trap_capture: bool = Field(default=False, description="Trap destroys pieces", alias="trapCapture")
    trap_immobilize: bool = Field(default=False, description="Trap immobilizes pieces", alias="trapImmobilize")
    trap_immobilize_duration: Duration = Field(default=1, description="Immobilize duration", alias="trapImmobilizeAmount")
    trap_teleport: bool = Field(default=False, description="Trap teleports pieces", alias="trapTeleport")
    trap_teleport_range: Optional[RangeMask8x8] = Field(default=None, description="Teleport range pattern", alias="trapTeleportPattern")
    trap_add_ability: bool = Field(default=False, description="Trap adds ability to pieces", alias="trapAddAbility")
    trap_ability: Optional[Dict[str, Any]] = Field(default=None, description="Ability to add", alias="trapAbility")
    
    @field_validator('trap_teleport_range')
    @classmethod
    def validate_teleport_range(cls, v):
        """Validate teleport range pattern"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v


# ========== TARGETING ABILITIES ==========

class RangeTag(BaseAdventureChessModel):
    """Range ability tag - defines targeting area for abilities"""
    range_mask: RangeMask8x8 = Field(default_factory=create_default_range_mask, description="8x8 range pattern", alias="rangeMask")
    piece_position: List[int] = Field(default=[4, 4], description="Piece position [row, col]", alias="piecePosition")
    range_friendly_only: bool = Field(default=False, description="Range only affects friendly pieces", alias="rangeFriendlyOnly")
    range_enemy_only: bool = Field(default=False, description="Range only affects enemy pieces", alias="rangeEnemyOnly")
    range_include_start: bool = Field(default=False, description="Include starting square in range", alias="rangeIncludeStart")
    range_include_self: bool = Field(default=False, description="Include self square in range", alias="rangeIncludeSelf")

    @field_validator('range_mask')
    @classmethod
    def validate_range_mask(cls, v):
        """Validate range mask"""
        return validate_range_mask_8x8(v)

    @field_validator('piece_position')
    @classmethod
    def validate_piece_position(cls, v):
        """Validate piece position"""
        if not isinstance(v, list) or len(v) != 2:
            raise ValueError("Piece position must be a list of [row, col]")
        if not all(isinstance(x, int) and 0 <= x <= 7 for x in v):
            raise ValueError("Piece position coordinates must be integers between 0 and 7")
        return v


class AreaEffectTag(BaseAdventureChessModel):
    """Area effect ability tag - affects multiple squares around target"""
    area_shape: AreaEffectShape = Field(default=AreaEffectShape.CIRCLE, description="Shape of area effect", alias="areaShape")
    area_radius: Distance = Field(default=1, description="Size of effect area", alias="areaSize")
    area_effect_target: Optional[List[int]] = Field(default=None, description="Target position [row, col]")
    area_effect_center: Optional[List[int]] = Field(default=None, description="Center of effect [row, col]")
    custom_area_pattern: Optional[Pattern8x8] = Field(default=None, description="Custom area pattern")

    @field_validator('custom_area_pattern')
    @classmethod
    def validate_custom_pattern(cls, v):
        """Validate custom area pattern"""
        if v is not None:
            return validate_pattern_8x8(v)
        return v

    @field_validator('area_effect_target', 'area_effect_center')
    @classmethod
    def validate_coordinates(cls, v):
        """Validate coordinate fields"""
        if v is not None:
            if not isinstance(v, list) or len(v) != 2:
                raise ValueError("Coordinates must be a list of [row, col]")
            if not all(isinstance(x, int) and 0 <= x <= 7 for x in v):
                raise ValueError("Coordinates must be integers between 0 and 7")
        return v


# ========== CONDITION ABILITIES ==========

class AdjacencyRequiredTag(BaseAdventureChessModel):
    """Adjacency required ability tag - ability only works when adjacent to specific pieces"""
    adjacency_list: List[Dict[str, Any]] = Field(default_factory=list, description="Required adjacent pieces", alias="adjacencyList")
    adjacency_max_distance: int = Field(default=1, ge=0, le=5, description="Maximum distance for adjacency", alias="adjacencyDistance")


class LosRequiredTag(BaseAdventureChessModel):
    """Line of sight required ability tag - requires clear line of sight to target"""
    los_ignore_friendly: bool = Field(default=False, description="Ignore friendly pieces for LoS", alias="losIgnoreFriendly")
    los_ignore_enemy: bool = Field(default=False, description="Ignore enemy pieces for LoS", alias="losIgnoreEnemy")
    los_ignore_all: bool = Field(default=False, description="Ignore all pieces for LoS", alias="losIgnoreAll")
    prevent_los: bool = Field(default=False, description="Prevent line of sight abilities", alias="preventLos")


class NoTurnCostTag(BaseAdventureChessModel):
    """No turn cost ability tag - ability doesn't consume turn points"""
    no_turn_cost_limit: int = Field(default=0, ge=0, le=10, description="Uses per turn (0=unlimited)", alias="noTurnCostLimit")


# ========== SPECIAL ABILITIES ==========

class ShareSpaceTag(BaseAdventureChessModel):
    """Share space ability tag - multiple pieces can occupy same square"""
    share_space_max: int = Field(default=2, ge=2, le=8, description="Maximum pieces per square", alias="shareSpaceMax")
    share_space_same_type: bool = Field(default=False, description="Only same piece types can share", alias="shareSpaceSameType")
    share_space_friendly: bool = Field(default=True, description="Allow friendly pieces to share space", alias="shareSpaceFriendly")
    share_space_enemy: bool = Field(default=False, description="Allow enemy pieces to share space", alias="shareSpaceEnemy")
    share_space_any: bool = Field(default=False, description="Allow any pieces to share space", alias="shareSpaceAny")


class DelayTag(BaseAdventureChessModel):
    """Delay ability tag - ability effect occurs after specified turns"""
    delay_turns: Optional[int] = Field(default=None, ge=1, le=10, description="Number of turns to delay", alias="delayTurnAmount")
    delay_actions: Optional[int] = Field(default=None, ge=1, le=10, description="Number of actions to delay", alias="delayActionAmount")
    delay_turn_enabled: bool = Field(default=False, description="Whether turn delay is enabled", alias="delayTurn")
    delay_action_enabled: bool = Field(default=False, description="Whether action delay is enabled", alias="delayAction")
    delay_cancelable: bool = Field(default=False, description="Whether delay can be cancelled")

    @model_validator(mode='after')
    def validate_delay_exclusivity(self):
        """Ensure delay_turns and delay_actions are mutually exclusive"""
        if self.delay_turns is not None and self.delay_actions is not None:
            raise ValueError("delay_turns and delay_actions cannot both be set")
        return self


class PassThroughTag(BaseAdventureChessModel):
    """Pass through ability tag - can target through other pieces"""
    pass_through_list: List[Dict[str, Any]] = Field(default_factory=list, description="Pieces that can be passed through", alias="passThroughList")
    pass_through_range: Optional[RangeMask8x8] = Field(default=None, description="Pass through range pattern")
    pass_through_piece_position: Optional[List[int]] = Field(default=None, description="Piece position for pass through")
    pass_through_capture: str = Field(default="None", description="Pass through capture mode", alias="passThroughCapture")

    @field_validator('pass_through_range')
    @classmethod
    def validate_pass_through_range(cls, v):
        """Validate pass through range pattern"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v

    @field_validator('pass_through_capture')
    @classmethod
    def validate_capture_mode(cls, v):
        """Validate capture mode"""
        valid_modes = ["None", "Enemy", "Friendly", "Any"]
        if v not in valid_modes:
            raise ValueError(f"Invalid capture mode: {v}. Must be one of {valid_modes}")
        return v


class PulseEffectTag(BaseAdventureChessModel):
    """Pulse effect ability tag - repeating effect that triggers every N turns"""
    interval: Duration = Field(default=1, description="Turns between activations", alias="pulseInterval")
    enabled: bool = Field(default=True, description="Whether pulse effect is enabled", alias="pulseEnabled")


class FogOfWarTag(BaseAdventureChessModel):
    """Fog of war ability tag - reveals hidden areas of the board"""
    vision_type: VisionType = Field(default=VisionType.SIGHT, description="Type of vision", alias="fogVisionType")
    fog_radius: Distance = Field(default=1, description="Vision radius", alias="fogRadius")
    fog_duration: int = Field(default=1, ge=0, le=10, description="Duration in turns (0=permanent)", alias="fogDuration")
    fog_cost: int = Field(default=0, ge=0, description="Point cost per use", alias="fogCost")
    fog_custom_range_pattern: Optional[RangeMask8x8] = Field(default=None, description="Custom vision pattern", alias="fogCustomRange")

    @field_validator('fog_custom_range_pattern')
    @classmethod
    def validate_custom_range(cls, v):
        """Validate custom range pattern"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v


class InvisibleTag(BaseAdventureChessModel):
    """Invisible ability tag - makes piece undetectable under certain conditions"""
    reveal_on_move: int = Field(default=0, ge=0, description="Reveal after N moves", alias="invisibleRevealMoveAmount")
    reveal_on_capture: int = Field(default=0, ge=0, description="Reveal after N captures", alias="invisibleRevealCaptureAmount")
    reveal_on_action: int = Field(default=0, ge=0, description="Reveal after N actions", alias="invisibleRevealActionAmount")
    reveal_on_enemy_los: bool = Field(default=False, description="Visible to enemies with line of sight", alias="invisibleRevealLos")
    reveal_move_enabled: bool = Field(default=False, description="Whether move reveal is enabled", alias="invisibleRevealMove")
    reveal_capture_enabled: bool = Field(default=False, description="Whether capture reveal is enabled", alias="invisibleRevealCapture")
    reveal_action_enabled: bool = Field(default=False, description="Whether action reveal is enabled", alias="invisibleRevealAction")


class ReactionTag(BaseAdventureChessModel):
    """Reaction ability tag - triggers automatically in response to events"""
    reaction_targets: List[Dict[str, Any]] = Field(default_factory=list, description="Target pieces for reaction", alias="reactionTargets")
    reaction_event_type: ReactionEventType = Field(default=ReactionEventType.ON_ENEMY_MOVE, description="Trigger event", alias="reactionEvent")
    uses_action: bool = Field(default=False, description="Whether reaction consumes action points", alias="reactionUsesAction")


class RequiresStartingPositionTag(BaseAdventureChessModel):
    """Requires starting position ability tag - only works if piece hasn't moved"""
    # This tag has no additional configuration
    pass


# ========== TAG REGISTRY ==========

# Map tag names to their corresponding model classes
TAG_MODEL_REGISTRY = {
    # Action tags
    "move": MoveTag,
    "summon": SummonTag,
    "revival": RevivalTag,
    "capture": CaptureTag,
    "carryPiece": CarryPieceTag,
    "swapPlaces": SwapPlacesTag,
    "displacePiece": DisplacePieceTag,
    "immobilize": ImmobilizeTag,
    "convertPiece": ConvertPieceTag,
    "duplicate": DuplicateTag,
    "buffPiece": BuffPieceTag,
    "debuffPiece": DebuffPieceTag,
    "addObstacle": AddObstacleTag,
    "removeObstacle": RemoveObstacleTag,
    "trapTile": TrapTileTag,

    # Targeting tags
    "range": RangeTag,
    "areaEffect": AreaEffectTag,

    # Condition tags
    "adjacencyRequired": AdjacencyRequiredTag,
    "losRequired": LosRequiredTag,
    "noTurnCost": NoTurnCostTag,

    # Special tags
    "shareSpace": ShareSpaceTag,
    "delay": DelayTag,
    "passThrough": PassThroughTag,
    "pulseEffect": PulseEffectTag,
    "fogOfWar": FogOfWarTag,
    "invisible": InvisibleTag,
    "reaction": ReactionTag,
    "requiresStartingPosition": RequiresStartingPositionTag,
}


def get_tag_model(tag_name: str) -> Optional[BaseAdventureChessModel]:
    """Get the Pydantic model class for a specific tag"""
    return TAG_MODEL_REGISTRY.get(tag_name)


def validate_tag_data(tag_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate data for a specific tag using its model"""
    model_class = get_tag_model(tag_name)
    if model_class is None:
        raise ValueError(f"Unknown tag: {tag_name}")

    # Create model instance to validate data
    instance = model_class(**data)
    return instance.model_dump(exclude_unset=True)
