"""
Range Tag Configuration for Adventure Chess Creator

This module handles the UI creation and data management for the range ability tag.
Matches the old ability editor exactly with quick pattern buttons and custom range editor.

Fields handled:
- rangePattern (8x8 grid): Custom range pattern for targeting
- rangePiecePosition (coordinates): Position of the piece in the pattern
- rangeIncludeStartingSquare (bool): Whether to include the starting square
- rangeContinueOffBoard (bool): Whether range continues off the board
- rangeFriendlyOnly (bool): Range limited to friendly side
- rangeEnemyOnly (bool): Range limited to enemy side
"""

import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (QWidget, QCheckBox, QPushButton, QVBoxLayout, QHBoxLayout,
                             QFormLayout, QLabel, QSizePolicy, QSpinBox, QComboBox)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig

logger = logging.getLogger(__name__)


class RangeConfig(BaseTagConfig):
    """Configuration handler for the range ability tag."""

    def __init__(self, editor_instance):
        super().__init__(editor_instance, "range")

    def get_title(self) -> str:
        """Get the display title for range configuration."""
        return "🎯 Range Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for range configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting range UI creation")

            # Main layout
            layout = QVBoxLayout()

            # Range pattern controls
            pattern_layout = QHBoxLayout()
            pattern_layout.addWidget(QLabel("Quick Patterns:"))

            # Chess piece pattern buttons with styling
            range_buttons = [
                ("♜", "rook", "Orthogonal lines (like Rook)"),
                ("♝", "bishop", "Diagonal lines (like Bishop)"),
                ("♛", "queen", "All directions (like Queen)"),
                ("♞", "knight", "L-shaped moves (like Knight)"),
                ("♚", "king", "Adjacent squares (like King)"),
                ("🌐", "global", "Entire board range")
            ]

            # Store pattern buttons for selection tracking
            self.range_pattern_buttons = []

            for symbol, preset_type, tooltip in range_buttons:
                btn = QPushButton(symbol)
                btn.setFixedSize(35, 30)
                btn.setToolTip(f"{tooltip} - Click to select this pattern")
                btn.setCheckable(True)  # Make buttons selectable
                btn.setStyleSheet("""
                    QPushButton {
                        font-size: 16px;
                        font-weight: bold;
                        border: 2px solid #555;
                        border-radius: 6px;
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #3a3a3a, stop:1 #2a2a2a);
                        color: white;
                        padding: 2px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #4a4a4a, stop:1 #3a3a3a);
                        border-color: #66aaff;
                    }
                    QPushButton:checked {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #4488cc, stop:1 #3366aa);
                        border-color: #66aaff;
                        border-width: 3px;
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                                  stop:0 #1a1a1a, stop:1 #2a2a2a);
                        border-color: #4488cc;
                    }
                """)
                btn.clicked.connect(lambda checked, pt=preset_type, b=btn: self.select_range_preset(pt, b))
                pattern_layout.addWidget(btn)
                self.range_pattern_buttons.append((btn, preset_type))

            pattern_layout.addStretch()

            clear_range_btn = QPushButton("Clear")
            clear_range_btn.clicked.connect(self.clear_range_selection)
            self.store_widget("clear_range_btn", clear_range_btn)
            pattern_layout.addWidget(clear_range_btn)

            layout.addLayout(pattern_layout)

            # Custom Range Editor Button
            custom_range_layout = QHBoxLayout()
            custom_range_btn = QPushButton("Edit Custom Range Pattern")
            custom_range_btn.clicked.connect(self.edit_custom_range_pattern)
            custom_range_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            self.store_widget("custom_range_btn", custom_range_btn)
            custom_range_layout.addWidget(custom_range_btn)
            custom_range_layout.addStretch()
            layout.addLayout(custom_range_layout)

            # Initialize range data
            if not hasattr(self, 'range_pattern'):
                self.range_pattern = [[False for _ in range(8)] for _ in range(8)]

            # Range options
            options_layout = QFormLayout()

            range_friendly_only_check = QCheckBox("Friendly side only")
            range_friendly_only_check.setToolTip("This ability can only be used on the friendly side of the map")
            range_friendly_only_check.stateChanged.connect(lambda state: self.on_range_side_changed(state, "friendly"))
            self.store_widget("range_friendly_only_check", range_friendly_only_check)
            options_layout.addRow("Options:", range_friendly_only_check)

            range_enemy_only_check = QCheckBox("Enemy side only")
            range_enemy_only_check.setToolTip("This ability can only be used on the enemy side of the map")
            range_enemy_only_check.stateChanged.connect(lambda state: self.on_range_side_changed(state, "enemy"))
            self.store_widget("range_enemy_only_check", range_enemy_only_check)
            options_layout.addRow("", range_enemy_only_check)

            layout.addLayout(options_layout)

            # Add the main layout to the parent
            main_widget = QWidget()
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Initialize range data if needed
            if not hasattr(self, 'range_pattern'):
                self.range_pattern = [[False for _ in range(8)] for _ in range(8)]
            if not hasattr(self, 'range_piece_position'):
                self.range_piece_position = [4, 4]

            self.log_debug("Range UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating range UI: {e}")
            raise

    def select_range_preset(self, preset_type, button):
        """Select a range preset pattern"""
        # Clear all other button selections
        for btn, _ in self.range_pattern_buttons:
            if btn != button:
                btn.setChecked(False)

        # Set the selected pattern
        self.selected_range_preset = preset_type
        self.log_debug(f"Selected range preset: {preset_type}")

    def clear_range_selection(self):
        """Clear range pattern selection"""
        for btn, _ in self.range_pattern_buttons:
            btn.setChecked(False)
        self.selected_range_preset = None
        self.log_debug("Cleared range selection")

    def edit_custom_range_pattern(self):
        """Edit custom range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'range_pattern', None)
        initial_position = getattr(self, 'range_piece_position', [3, 3])
        checkbox_states = getattr(self, 'range_checkbox_states', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Range Pattern", self.editor, checkbox_states
        )

        if pattern is not None:
            self.range_pattern = pattern
            self.range_piece_position = piece_position
            self.range_checkbox_states = new_checkbox_states
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self.editor, "Pattern Saved", "Range pattern has been updated.")

    def on_range_side_changed(self, state, side_type):
        """Handle range side restriction changes (mutually exclusive)"""
        friendly_check = self.get_widget_by_name("range_friendly_only_check")
        enemy_check = self.get_widget_by_name("range_enemy_only_check")

        if side_type == "friendly" and friendly_check and friendly_check.isChecked():
            if enemy_check:
                enemy_check.setChecked(False)
        elif side_type == "enemy" and enemy_check and enemy_check.isChecked():
            if friendly_check:
                friendly_check.setChecked(False)

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting range data population with: {data}")

            # Store range side restrictions as instance attributes
            self.range_friendly_only = data.get("rangeFriendlyOnly", False)
            self.range_enemy_only = data.get("rangeEnemyOnly", False)

            # Populate range side restrictions in UI widgets if they exist
            friendly_check = self.get_widget_by_name("range_friendly_only_check")
            if friendly_check:
                friendly_check.setChecked(self.range_friendly_only)

            enemy_check = self.get_widget_by_name("range_enemy_only_check")
            if enemy_check:
                enemy_check.setChecked(self.range_enemy_only)

            # Handle legacy data format (old save files)
            # Legacy: rangeMask -> rangePattern
            if "rangeMask" in data:
                self.range_pattern = data["rangeMask"]
                self.log_debug("Loaded legacy rangeMask as rangePattern")
            elif "rangePattern" in data:
                self.range_pattern = data["rangePattern"]

            # Legacy: piecePosition -> rangePiecePosition
            if "piecePosition" in data:
                self.range_piece_position = data["piecePosition"]
                self.log_debug("Loaded legacy piecePosition as rangePiecePosition")
            elif "rangePiecePosition" in data:
                self.range_piece_position = data["rangePiecePosition"]

            # Handle other range data
            if "rangeCheckboxStates" in data:
                self.range_checkbox_states = data["rangeCheckboxStates"]

            # Load selected preset if available
            if "rangePreset" in data:
                self.selected_range_preset = data["rangePreset"]
                # Update button selection
                for btn, preset_type in self.range_pattern_buttons:
                    btn.setChecked(preset_type == self.selected_range_preset)

            self.log_debug("Range data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the range configuration data
        """
        try:
            data = {}

            # Collect range side restrictions (always include boolean values)
            friendly_check = self.get_widget_by_name("range_friendly_only_check")
            if friendly_check:
                data["rangeFriendlyOnly"] = friendly_check.isChecked()

            enemy_check = self.get_widget_by_name("range_enemy_only_check")
            if enemy_check:
                data["rangeEnemyOnly"] = enemy_check.isChecked()

            # Collect custom patterns if they exist
            if hasattr(self, 'range_pattern'):
                data["rangePattern"] = self.range_pattern
            if hasattr(self, 'range_piece_position'):
                data["rangePiecePosition"] = self.range_piece_position
            if hasattr(self, 'range_checkbox_states'):
                data["rangeCheckboxStates"] = self.range_checkbox_states

            # Collect selected preset if available
            if hasattr(self, 'selected_range_preset') and self.selected_range_preset:
                data["rangePreset"] = self.selected_range_preset

            self.log_debug(f"Collected range data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
