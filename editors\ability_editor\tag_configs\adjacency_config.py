"""
Adjacency tag configuration for ability editor.
Handles adjacency-based ability configurations with inline 3x3 tile selection grid.
"""

from PyQt6.QtWidgets import (
    QPushButton, QLabel, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout
)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from dialogs.range_editor_dialog import RangeEditorDialog


class AdjacencyConfig(BaseTagConfig):
    """Configuration for adjacency tag abilities using unified adjacency dialog."""

    def __init__(self, editor):
        super().__init__(editor, "adjacencyRequired")
        # Initialize adjacency configuration
        self.adjacency_config = {
            'pieces': [],
            'distance': 1,
            'pattern': []
        }
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for adjacency configuration using unified dialog.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting adjacency UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Only activates if adjacent to defined units.")
            description.setWordWrap(True)
            description.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
            layout.addWidget(description)

            # Adjacency configuration button
            config_layout = QHBoxLayout()
            config_button = QPushButton("Configure Adjacency Requirements")
            config_button.setToolTip("Open adjacency configuration dialog")
            config_button.clicked.connect(self.edit_adjacency_config)
            self.store_widget("adjacency_config_button", config_button)
            config_layout.addWidget(config_button)
            layout.addLayout(config_layout)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            self.log_debug("Adjacency UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def edit_adjacency_config(self):
        """Open the range editor dialog for adjacency pattern configuration."""
        try:
            # Convert adjacency pattern to integer pattern for dialog
            pattern = self.adjacency_config.get('pattern', [])
            if not pattern:
                # Default 3x3 adjacency pattern around center
                pattern = [[0 for _ in range(8)] for _ in range(8)]
                # Set adjacent squares around center (3,3)
                for r in range(2, 5):
                    for c in range(2, 5):
                        if r != 3 or c != 3:  # Don't include center
                            pattern[r][c] = 1

            dialog = RangeEditorDialog(
                parent=self.editor,
                initial_pattern=pattern,
                piece_pos=[3, 3],  # Center position
                include_starting_square=False,
                friendly_only=False
            )

            if dialog.exec() == dialog.DialogCode.Accepted:
                self.adjacency_config['pattern'] = dialog.get_pattern()
                self.log_debug("Adjacency pattern updated from range editor")

                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()

        except Exception as e:
            self.log_error(f"Error opening adjacency pattern editor: {e}")



    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting adjacency data population with: {data}")

            # Load adjacency configuration
            self.adjacency_config = {
                'pieces': data.get('adjacencyPieces', []),
                'distance': data.get('adjacencyDistance', 1),
                'pattern': data.get('adjacencyPattern', [])
            }

            # Handle legacy adjacency tile pattern (3x3 grid)
            if 'adjacencyTilePattern' in data:
                pattern = data['adjacencyTilePattern']
                if pattern and len(pattern) == 3 and len(pattern[0]) == 3:
                    # Convert 3x3 pattern to relative positions
                    legacy_pattern = []
                    for r in range(3):
                        for c in range(3):
                            if r != 1 or c != 1:  # Skip center (piece position)
                                if pattern[r][c]:
                                    legacy_pattern.append((r-1, c-1))  # Convert to relative positions
                    self.adjacency_config['pattern'] = legacy_pattern

            self.log_debug("Adjacency data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the adjacency configuration data
        """
        try:
            data = {}

            # Save adjacency configuration
            if self.adjacency_config['pieces']:
                data["adjacencyPieces"] = self.adjacency_config['pieces']

            if self.adjacency_config['distance'] != 1:
                data["adjacencyDistance"] = self.adjacency_config['distance']

            if self.adjacency_config['pattern']:
                data["adjacencyPattern"] = self.adjacency_config['pattern']

            self.log_debug(f"Collected adjacency data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
