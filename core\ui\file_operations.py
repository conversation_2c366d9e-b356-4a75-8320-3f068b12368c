"""
File Operations Components for Adventure Chess Creator

This module contains file operation UI components extracted from ui_shared_components.py:
- FileOperationsWidget: Reusable file operations widget with customizable button sets

This component provides common save/load/delete functionality used across
all editors in the application.
"""

from typing import Callable, Optional

from PyQt6.QtWidgets import QHBoxLayout, QPushButton, QWidget


class FileOperationsWidget(QWidget):
    """
    Reusable file operations widget
    Common save/load/delete functionality with customizable button sets
    """

    def __init__(self, button_set="full", parent=None):
        """
        Initialize file operations widget

        Args:
            button_set: "full" (all buttons), "basic" (new/load/save), "save_only" (save/save_as)
        """
        super().__init__(parent)
        self.button_set = button_set
        self.setup_ui()

    def setup_ui(self):
        """Setup the file operations UI"""
        layout = QHBoxLayout()

        # Create buttons based on button set
        if self.button_set in ["full", "basic"]:
            self.new_btn = QPushButton("📄 New")
            self.load_btn = QPushButton("📂 Load")

        if self.button_set in ["full", "basic", "save_only"]:
            self.save_btn = QPushButton("💾 Save")
            self.save_as_btn = QPushButton("💾 Save As...")

        if self.button_set == "full":
            self.delete_btn = QPushButton("🗑️ Delete")

        # Style the buttons with dark theme support
        button_style = """
            QPushButton {
                font-weight: bold;
                padding: 8px 12px;
                border-radius: 4px;
                border: 1px solid palette(mid);
                background-color: palette(button);
                color: palette(button-text);
            }
            QPushButton:hover {
                background-color: palette(highlight);
                border-color: palette(highlight);
                color: palette(highlighted-text);
            }
            QPushButton:pressed {
                background-color: palette(dark);
                color: palette(bright-text);
            }
        """

        # Add buttons to layout based on button set
        if self.button_set in ["full", "basic"]:
            self.new_btn.setStyleSheet(button_style)
            self.load_btn.setStyleSheet(button_style)
            layout.addWidget(self.new_btn)
            layout.addWidget(self.load_btn)

        if self.button_set in ["full", "basic", "save_only"]:
            self.save_btn.setStyleSheet(button_style)
            self.save_as_btn.setStyleSheet(button_style)
            layout.addWidget(self.save_btn)
            layout.addWidget(self.save_as_btn)

        if self.button_set == "full":
            self.delete_btn.setStyleSheet(button_style)
            layout.addWidget(self.delete_btn)

        layout.addStretch()
        self.setLayout(layout)

    def connect_signals(
        self,
        new_func: Optional[Callable] = None,
        load_func: Optional[Callable] = None,
        save_func: Optional[Callable] = None,
        save_as_func: Optional[Callable] = None,
        delete_func: Optional[Callable] = None,
    ):
        """Connect button signals to functions"""
        if new_func and hasattr(self, "new_btn"):
            self.new_btn.clicked.connect(new_func)
        if load_func and hasattr(self, "load_btn"):
            self.load_btn.clicked.connect(load_func)
        if save_func and hasattr(self, "save_btn"):
            self.save_btn.clicked.connect(save_func)
        if save_as_func and hasattr(self, "save_as_btn"):
            self.save_as_btn.clicked.connect(save_as_func)
        if delete_func and hasattr(self, "delete_btn"):
            self.delete_btn.clicked.connect(delete_func)

    def set_button_enabled(self, button_name: str, enabled: bool):
        """Enable/disable specific buttons"""
        button_map = {
            "new": "new_btn",
            "load": "load_btn",
            "save": "save_btn",
            "save_as": "save_as_btn",
            "delete": "delete_btn",
        }

        if button_name in button_map:
            btn_attr = button_map[button_name]
            if hasattr(self, btn_attr):
                getattr(self, btn_attr).setEnabled(enabled)
