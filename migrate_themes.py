#!/usr/bin/env python3
"""
Theme Migration Script for Adventure Chess Creator

This script helps migrate existing code to use the new standardized theming system.
It can automatically update files to replace manual styling with theme-based styling.
"""

import os
import re
import argparse
from pathlib import Path
from typing import List, Tuple, Dict


class ThemeMigrator:
    """Handles migration of existing code to use the new theme system"""
    
    # Patterns to detect and replace
    IMPORT_ADDITIONS = [
        "from core.ui import (",
        "    apply_theme_to_widget,",
        "    auto_theme_widget_children,",
        "    create_themed_button,",
        "    create_themed_label,",
        "    create_themed_line_edit,",
        "    create_themed_combo_box,",
        "    create_themed_group_box,",
        "    apply_adventure_chess_theme",
        ")"
    ]
    
    # Common button text patterns and their recommended themes
    BUTTON_PATTERNS = {
        r'\b(save|apply|ok|submit|confirm|accept)\b': 'primary_button',
        r'\b(new|add|create|insert|plus)\b': 'success_button',
        r'\b(delete|remove|clear|destroy|trash|cancel|abort)\b': 'danger_button',
        r'\b(load|browse|select|choose|refresh|reload)\b': 'secondary_button',
    }
    
    # CSS patterns to replace with theme calls
    CSS_REPLACEMENTS = [
        # Button styling patterns
        (
            r'(\w+)\.setStyleSheet\(\s*"""[^"]*QPushButton[^"]*"""\s*\)',
            r'apply_theme_to_widget(\1, "secondary_button")'
        ),
        # Label styling patterns
        (
            r'(\w+)\.setStyleSheet\(\s*"[^"]*font-weight:\s*bold[^"]*"\s*\)',
            r'apply_theme_to_widget(\1, "header_label")'
        ),
    ]
    
    def __init__(self, project_root: Path):
        """Initialize the migrator with the project root path"""
        self.project_root = Path(project_root)
        self.changes_made = []
    
    def find_python_files(self, exclude_dirs: List[str] = None) -> List[Path]:
        """Find all Python files in the project"""
        if exclude_dirs is None:
            exclude_dirs = ['__pycache__', '.git', 'venv', 'env', 'logs', 'data']
        
        python_files = []
        for file_path in self.project_root.rglob('*.py'):
            # Skip excluded directories
            if any(exclude_dir in str(file_path) for exclude_dir in exclude_dirs):
                continue
            python_files.append(file_path)
        
        return python_files
    
    def analyze_file(self, file_path: Path) -> Dict[str, List[str]]:
        """Analyze a file for theming opportunities"""
        analysis = {
            'manual_stylesheets': [],
            'button_creations': [],
            'label_creations': [],
            'input_creations': [],
            'potential_auto_theme_locations': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                # Find manual stylesheet calls
                if 'setStyleSheet' in line:
                    analysis['manual_stylesheets'].append(f"Line {i}: {line.strip()}")
                
                # Find button creations
                if 'QPushButton(' in line:
                    analysis['button_creations'].append(f"Line {i}: {line.strip()}")
                
                # Find label creations
                if 'QLabel(' in line:
                    analysis['label_creations'].append(f"Line {i}: {line.strip()}")
                
                # Find input creations
                if re.search(r'Q(LineEdit|TextEdit|ComboBox)\(', line):
                    analysis['input_creations'].append(f"Line {i}: {line.strip()}")
                
                # Find potential auto-theme locations (UI setup methods)
                if re.search(r'def (setup_ui|__init__|create_.*_ui)', line):
                    analysis['potential_auto_theme_locations'].append(f"Line {i}: {line.strip()}")
        
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
        
        return analysis
    
    def suggest_migrations(self, file_path: Path, analysis: Dict[str, List[str]]) -> List[str]:
        """Suggest migration strategies for a file"""
        suggestions = []
        
        if analysis['manual_stylesheets']:
            suggestions.append(
                "🎨 Manual stylesheets detected - Consider replacing with theme calls:\n"
                "   apply_theme_to_widget(widget, 'appropriate_theme')"
            )
        
        if analysis['button_creations']:
            suggestions.append(
                "🔘 Button creations detected - Consider using themed factories:\n"
                "   create_themed_button('Text', 'appropriate_theme')"
            )
        
        if analysis['potential_auto_theme_locations']:
            suggestions.append(
                "🔧 UI setup methods detected - Consider adding auto-theming:\n"
                "   auto_theme_widget_children(self)  # Add at end of UI setup"
            )
        
        if len(analysis['button_creations']) > 3 or len(analysis['label_creations']) > 3:
            suggestions.append(
                "🏭 Many widgets detected - Consider using themed factories or auto-theming"
            )
        
        return suggestions
    
    def create_migration_plan(self, file_path: Path) -> str:
        """Create a detailed migration plan for a specific file"""
        analysis = self.analyze_file(file_path)
        suggestions = self.suggest_migrations(file_path, analysis)
        
        plan = f"\n📄 Migration Plan for: {file_path.relative_to(self.project_root)}\n"
        plan += "=" * 60 + "\n"
        
        if not any(analysis.values()):
            plan += "✅ No theming opportunities detected in this file.\n"
            return plan
        
        # Add import suggestions
        plan += "1. 📦 Add Theme System Imports:\n"
        plan += "   Add these imports to the top of the file:\n"
        for import_line in self.IMPORT_ADDITIONS:
            plan += f"   {import_line}\n"
        plan += "\n"
        
        # Add specific suggestions
        plan += "2. 🔧 Specific Migration Suggestions:\n"
        for i, suggestion in enumerate(suggestions, 1):
            plan += f"   {suggestion}\n\n"
        
        # Add detailed findings
        if analysis['manual_stylesheets']:
            plan += "3. 🎨 Manual StyleSheets Found:\n"
            for stylesheet in analysis['manual_stylesheets'][:5]:  # Limit to first 5
                plan += f"   - {stylesheet}\n"
            if len(analysis['manual_stylesheets']) > 5:
                plan += f"   ... and {len(analysis['manual_stylesheets']) - 5} more\n"
            plan += "\n"
        
        if analysis['button_creations']:
            plan += "4. 🔘 Button Creations Found:\n"
            for button in analysis['button_creations'][:5]:
                plan += f"   - {button}\n"
            if len(analysis['button_creations']) > 5:
                plan += f"   ... and {len(analysis['button_creations']) - 5} more\n"
            plan += "\n"
        
        # Add example migration
        plan += "5. 📝 Example Migration:\n"
        plan += "   Before:\n"
        plan += "   button = QPushButton('Save')\n"
        plan += "   button.setStyleSheet('QPushButton { background: blue; }')\n\n"
        plan += "   After:\n"
        plan += "   button = create_themed_button('Save', 'primary_button')\n"
        plan += "   # OR\n"
        plan += "   button = QPushButton('Save')\n"
        plan += "   apply_theme_to_widget(button, 'primary_button')\n\n"
        
        return plan
    
    def generate_project_report(self) -> str:
        """Generate a comprehensive project-wide migration report"""
        python_files = self.find_python_files()
        
        report = "🎨 Adventure Chess Creator - Theme Migration Report\n"
        report += "=" * 60 + "\n\n"
        
        report += f"📊 Project Analysis:\n"
        report += f"   - Total Python files: {len(python_files)}\n"
        
        # Analyze all files
        total_stylesheets = 0
        total_buttons = 0
        total_labels = 0
        files_needing_migration = 0
        
        priority_files = []
        
        for file_path in python_files:
            analysis = self.analyze_file(file_path)
            
            file_stylesheets = len(analysis['manual_stylesheets'])
            file_buttons = len(analysis['button_creations'])
            file_labels = len(analysis['label_creations'])
            
            total_stylesheets += file_stylesheets
            total_buttons += file_buttons
            total_labels += file_labels
            
            if file_stylesheets > 0 or file_buttons > 2 or file_labels > 2:
                files_needing_migration += 1
                
                # Calculate priority score
                priority_score = file_stylesheets * 3 + file_buttons + file_labels
                priority_files.append((file_path, priority_score, analysis))
        
        # Sort by priority
        priority_files.sort(key=lambda x: x[1], reverse=True)
        
        report += f"   - Files with manual stylesheets: {total_stylesheets}\n"
        report += f"   - Files with button creations: {total_buttons}\n"
        report += f"   - Files needing migration: {files_needing_migration}\n\n"
        
        # Priority files
        report += "🚀 High Priority Files for Migration:\n"
        report += "-" * 40 + "\n"
        
        for i, (file_path, score, analysis) in enumerate(priority_files[:10], 1):
            rel_path = file_path.relative_to(self.project_root)
            report += f"{i:2d}. {rel_path} (Score: {score})\n"
            report += f"    - Manual stylesheets: {len(analysis['manual_stylesheets'])}\n"
            report += f"    - Button creations: {len(analysis['button_creations'])}\n"
            report += f"    - Label creations: {len(analysis['label_creations'])}\n\n"
        
        # Quick migration steps
        report += "🔧 Quick Migration Steps:\n"
        report += "-" * 30 + "\n"
        report += "1. Start with the highest priority files\n"
        report += "2. Add theme system imports\n"
        report += "3. Replace manual stylesheets with theme calls\n"
        report += "4. Use themed widget factories for new widgets\n"
        report += "5. Add auto_theme_widget_children() to UI setup methods\n"
        report += "6. Test and verify visual consistency\n\n"
        
        # Integration patterns
        report += "🔗 Recommended Integration Patterns:\n"
        report += "-" * 35 + "\n"
        report += "For dialogs: Add apply_adventure_chess_theme(self) at end of setup\n"
        report += "For editors: Add auto_theme_widget_children(self) after UI creation\n"
        report += "For new widgets: Use create_themed_* factories\n"
        report += "For existing widgets: Use apply_theme_to_widget()\n\n"
        
        return report
    
    def save_migration_plans(self, output_dir: Path = None):
        """Save individual migration plans for each file"""
        if output_dir is None:
            output_dir = self.project_root / "migration_plans"
        
        output_dir.mkdir(exist_ok=True)
        
        python_files = self.find_python_files()
        
        for file_path in python_files:
            analysis = self.analyze_file(file_path)
            
            # Skip files with no migration opportunities
            if not any(analysis.values()):
                continue
            
            plan = self.create_migration_plan(file_path)
            
            # Create output filename
            rel_path = file_path.relative_to(self.project_root)
            output_name = str(rel_path).replace('/', '_').replace('\\', '_') + "_migration.txt"
            output_path = output_dir / output_name
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(plan)
        
        print(f"Migration plans saved to: {output_dir}")


def main():
    """Main function for running the migration analysis"""
    parser = argparse.ArgumentParser(description="Adventure Chess Creator Theme Migration Tool")
    parser.add_argument("project_path", help="Path to the Adventure Chess Creator project")
    parser.add_argument("--report", action="store_true", help="Generate project-wide report")
    parser.add_argument("--plans", action="store_true", help="Generate individual migration plans")
    parser.add_argument("--file", help="Analyze specific file")
    
    args = parser.parse_args()
    
    project_path = Path(args.project_path)
    
    if not project_path.exists():
        print(f"Error: Project path {project_path} does not exist")
        return
    
    migrator = ThemeMigrator(project_path)
    
    if args.report:
        report = migrator.generate_project_report()
        print(report)
        
        # Save report to file
        report_file = project_path / "theme_migration_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\nReport saved to: {report_file}")
    
    if args.plans:
        migrator.save_migration_plans()
    
    if args.file:
        file_path = Path(args.file)
        if file_path.exists():
            plan = migrator.create_migration_plan(file_path)
            print(plan)
        else:
            print(f"Error: File {file_path} does not exist")


if __name__ == "__main__":
    # If running directly, analyze current directory
    if len(sys.argv) == 1:
        import sys
        current_dir = Path.cwd()
        migrator = ThemeMigrator(current_dir)
        report = migrator.generate_project_report()
        print(report)
    else:
        main()
