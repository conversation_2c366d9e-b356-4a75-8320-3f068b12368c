{"version": "0.2.0", "configurations": [{"name": "Adventure Chess - Main App", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}, {"name": "Adventure Chess - Piece Editor", "type": "python", "request": "launch", "program": "${workspaceFolder}/piece_editor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}, {"name": "Adventure Chess - Ability Editor", "type": "python", "request": "launch", "program": "${workspaceFolder}/ability_editor.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}, {"name": "Adventure Chess - Run Tests", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_tests.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}]}