"""
Pydantic schema for Adventure Chess pieces
Defines the complete structure and validation for piece data
"""

from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator, model_validator

from .base import (
    BaseAdventureChessModel,
    MovementType,
    RechargeType,
    PieceRole,
    Coordinate,
    Pattern8x8,
    Points,
    Distance,
    create_default_pattern,
    validate_pattern_8x8
)


class Movement(BaseAdventureChessModel):
    """Movement configuration for a piece"""
    type: MovementType = Field(default=MovementType.ORTHOGONAL, description="Type of movement")
    distance: Optional[Distance] = Field(default=None, description="Movement distance for standard types (legacy field)")
    pattern: Optional[Pattern8x8] = Field(default=None, description="Custom movement pattern (8x8 grid)")
    piece_position: Optional[List[int]] = Field(default=None, description="Piece position for custom pattern [row, col]")
    
    @field_validator('pattern')
    @classmethod
    def validate_pattern(cls, v):
        """Validate custom movement pattern"""
        if v is not None:
            return validate_pattern_8x8(v)
        return v

    @field_validator('piece_position')
    @classmethod
    def validate_piece_position(cls, v):
        """Validate piece position coordinates"""
        if v is not None:
            if not isinstance(v, list) or len(v) != 2:
                raise ValueError("Piece position must be a list of [row, col]")
            if not all(isinstance(x, int) and 0 <= x <= 7 for x in v):
                raise ValueError("Piece position coordinates must be integers between 0 and 7")
        return v

    @field_validator('distance')
    @classmethod
    def validate_distance_for_type(cls, v, info):
        """Validate distance - PERMISSIVE MODE"""
        # Allow any distance value for maximum flexibility
        return v


class Piece(BaseAdventureChessModel):
    """Complete piece definition for Adventure Chess"""
    
    # Basic Information
    name: str = Field(..., min_length=1, max_length=50, description="Piece name")
    description: str = Field(default="", max_length=500, description="Piece description")
    role: PieceRole = Field(default=PieceRole.COMMANDER, description="Piece role")
    
    # Special Properties
    can_castle: bool = Field(default=False, description="Can participate in castling")
    track_starting_position: bool = Field(default=False, description="Track starting position for abilities")
    color_directional: bool = Field(default=False, description="Behavior differs by color")
    can_capture: bool = Field(default=True, description="Can capture other pieces")
    
    # Icons
    black_icon: Optional[str] = Field(default=None, description="Black piece icon filename")
    white_icon: Optional[str] = Field(default=None, description="White piece icon filename")
    
    # Movement
    movement: Movement = Field(default_factory=Movement, description="Movement configuration")
    
    # Point System
    enable_recharge: bool = Field(default=False, description="Enable recharge system")
    max_points: Points = Field(default=0, description="Maximum action points")
    starting_points: Points = Field(default=0, description="Starting action points")
    recharge_type: RechargeType = Field(default=RechargeType.TURN_RECHARGE, description="How points recharge")
    turn_points: Points = Field(default=1, description="Points gained per turn")
    capture_points: Points = Field(default=0, description="Points gained per capture")
    move_points: Points = Field(default=0, description="Points gained per move")
    adjacency_recharge_config: Optional[Dict[str, Any]] = Field(default=None, description="Adjacency recharge configuration")
    committed_recharge_turns: Points = Field(default=1, description="Turns for committed recharge")
    
    # Abilities and Promotions
    abilities: List[str] = Field(default_factory=list, description="List of ability filenames")
    promotions: List[str] = Field(default_factory=list, description="Primary promotion options")
    secondary_promotions: List[str] = Field(default_factory=list, description="Secondary promotion options")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate piece name"""
        if not v or not v.strip():
            raise ValueError("Piece name cannot be empty")
        return v.strip()

    @field_validator('abilities')
    @classmethod
    def validate_abilities(cls, v):
        """Validate ability filenames"""
        if not isinstance(v, list):
            raise ValueError("Abilities must be a list")

        # Remove duplicates while preserving order
        seen = set()
        unique_abilities = []
        for ability in v:
            if ability not in seen:
                seen.add(ability)
                unique_abilities.append(ability)

        return unique_abilities

    @field_validator('promotions', 'secondary_promotions')
    @classmethod
    def validate_promotions(cls, v):
        """Validate promotion lists"""
        if not isinstance(v, list):
            raise ValueError("Promotions must be a list")

        # Remove duplicates while preserving order
        seen = set()
        unique_promotions = []
        for promotion in v:
            if promotion not in seen:
                seen.add(promotion)
                unique_promotions.append(promotion)

        return unique_promotions

    @model_validator(mode='after')
    def validate_starting_points(self):
        """Validate starting points - PERMISSIVE MODE"""
        # Skip validation for maximum flexibility
        return self
    
    def to_legacy_dict(self) -> Dict[str, Any]:
        """Convert to legacy dictionary format for backward compatibility"""
        data = self.model_dump(exclude_unset=True, by_alias=True)
        
        # Handle movement conversion
        if 'movement' in data:
            movement = data['movement']
            # Keep full movement structure for proper round-trip compatibility
            movement_data = {
                'type': movement['type']
            }

            # Include distance only if present (for backward compatibility)
            if 'distance' in movement:
                movement_data['distance'] = movement['distance']

            # Include pattern and piece_position in movement structure
            if movement.get('pattern'):
                movement_data['pattern'] = movement['pattern']
            if movement.get('piece_position'):
                movement_data['piecePosition'] = movement['piece_position']
            elif movement.get('piecePosition'):  # Handle legacy field name
                movement_data['piecePosition'] = movement['piecePosition']

            data['movement'] = movement_data

            # Also add legacy fields for backward compatibility
            if movement.get('pattern'):
                data['customPattern'] = movement['pattern']
            if movement.get('piece_position'):
                data['customPatternPiecePos'] = movement['piece_position']
        
        # Convert icon fields to legacy names
        if 'black_icon' in data:
            data['blackIcon'] = data.pop('black_icon')
        if 'white_icon' in data:
            data['whiteIcon'] = data.pop('white_icon')
        
        # Convert snake_case to camelCase for legacy compatibility
        legacy_mappings = {
            'can_castle': 'canCastle',
            'track_starting_position': 'trackStartingPosition',
            'color_directional': 'colorDirectional',
            'can_capture': 'canCapture',
            'enable_recharge': 'enableRecharge',
            'max_points': 'maxPoints',
            'starting_points': 'startingPoints',
            'recharge_type': 'rechargeType',
            'turn_points': 'turnPoints',
            'capture_points': 'capturePoints',
            'move_points': 'movePoints',
            'adjacency_recharge_config': 'adjacencyRechargeConfig',
            'committed_recharge_turns': 'committedRechargeTurns',
            'secondary_promotions': 'secondaryPromotions'
        }
        
        for new_key, legacy_key in legacy_mappings.items():
            if new_key in data:
                data[legacy_key] = data.pop(new_key)
        
        return data
    
    @classmethod
    def from_legacy_dict(cls, data: Dict[str, Any]) -> 'Piece':
        """Create piece from legacy dictionary format"""
        # Create a copy to avoid modifying original
        normalized_data = data.copy()
        
        # Convert legacy field names to new schema
        legacy_mappings = {
            'canCastle': 'can_castle',
            'trackStartingPosition': 'track_starting_position',
            'colorDirectional': 'color_directional',
            'canCapture': 'can_capture',
            'enableRecharge': 'enable_recharge',
            'maxPoints': 'max_points',
            'startingPoints': 'starting_points',
            'rechargeType': 'recharge_type',
            'turnPoints': 'turn_points',
            'capturePoints': 'capture_points',
            'movePoints': 'move_points',
            'adjacencyRechargeConfig': 'adjacency_recharge_config',
            'committedRechargeTurns': 'committed_recharge_turns',
            'secondaryPromotions': 'secondary_promotions',
            'blackIcon': 'black_icon',
            'whiteIcon': 'white_icon'
        }
        
        for legacy_key, new_key in legacy_mappings.items():
            if legacy_key in normalized_data:
                normalized_data[new_key] = normalized_data.pop(legacy_key)
        
        # Handle movement conversion
        if 'movement' in normalized_data:
            movement_data = normalized_data['movement'].copy()

            # Add custom pattern if present
            if 'customPattern' in normalized_data:
                movement_data['pattern'] = normalized_data.pop('customPattern')
            if 'customPatternPiecePos' in normalized_data:
                movement_data['piece_position'] = normalized_data.pop('customPatternPiecePos')

            # Handle piecePosition field name conversion
            if 'piecePosition' in movement_data:
                movement_data['piece_position'] = movement_data.pop('piecePosition')

            normalized_data['movement'] = movement_data
        
        return cls(**normalized_data)
