"""
Base Classes for Adventure Chess Creator

This module contains all the foundational base classes that provide
common functionality and patterns used throughout the application.

Classes:
- BaseWidget: Foundation for all custom UI widgets
- BaseFormWidget: Base for form-style widgets with labels and inputs
- BaseTabWidget: Base for tab-based interfaces
- BaseDataWidget: Base for widgets that handle data collection/population
- BaseManager: Abstract foundation for all manager components
- BaseUIManager: Base for UI component managers
- BaseDataManager: Base for data management classes
- BaseConfigurationManager: Base for configuration managers
- BaseDataHandler: Unified base for all data handlers
- BaseEditor: Base class for all Adventure Chess editors
"""

from .base_data_handler import BaseDataHandler
from .base_editor import BaseEditor
from .base_manager import (
    BaseConfigurationManager,
    BaseDataManager,
    BaseManager,
    BaseUIManager,
)
from .base_widgets import BaseDataWidget, BaseFormWidget, BaseTabWidget, BaseWidget

__all__ = [
    "BaseWidget",
    "BaseFormWidget",
    "BaseTabWidget",
    "BaseDataWidget",
    "BaseManager",
    "BaseUIManager",
    "BaseDataManager",
    "BaseConfigurationManager",
    "BaseDataHandler",
    "BaseEditor",
]
