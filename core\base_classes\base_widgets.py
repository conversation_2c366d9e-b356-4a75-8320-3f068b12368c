"""
Base Widget Classes for Adventure Chess Creator

This module provides base classes for common UI widget patterns used throughout
the application. These base classes consolidate duplicate functionality and
provide consistent behavior across all editors.

Base Classes:
- BaseWidget: Foundation for all custom widgets
- BaseFormWidget: Base for form-style widgets with labels and inputs
- BaseTabWidget: Base for tab-based interfaces
- BaseDataWidget: Base for widgets that handle data collection/population
- BaseGridWidget: Base for grid-based interactive widgets
- BaseDialogWidget: Base for dialog-style widgets
"""

import logging
from typing import Any, Callable, Dict, List, Optional

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFormLayout,
    QGridLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QSizePolicy,
    QSpinBox,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from config import LAYOUT_MARGIN, LAYOUT_SPACING
from core.ui import ResponsiveWidget, make_widget_responsive

logger = logging.getLogger(__name__)


class BaseWidget(ResponsiveWidget):
    """
    Foundation base class for all custom widgets in Adventure Chess Creator.

    Provides:
    - Consistent styling and responsive behavior
    - Standard widget management methods
    - Change tracking capabilities
    - Error handling and logging
    """

    # Signals for common widget events
    data_changed = pyqtSignal()
    validation_changed = pyqtSignal(bool)  # True if valid, False if invalid

    def __init__(
        self, parent=None, title: str = "", enable_change_tracking: bool = True
    ):
        super().__init__(parent)

        self.title = title
        self.enable_change_tracking = enable_change_tracking
        self.is_valid = True
        self.validation_errors = []
        self.stored_widgets = {}  # For widget storage and retrieval

        # Initialize the widget
        self.init_base_widget()

    def init_base_widget(self):
        """Initialize base widget properties and styling"""
        # Set responsive behavior
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Apply base styling
        self.setStyleSheet(self.get_base_style())

        # Setup change tracking if enabled
        if self.enable_change_tracking:
            self.setup_change_tracking()

    def get_base_style(self) -> str:
        """Get base stylesheet for consistent widget appearance"""
        return """
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 9pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """

    def setup_change_tracking(self):
        """Setup automatic change tracking for child widgets"""
        # Override in subclasses to connect specific widgets

    def store_widget(self, name: str, widget: QWidget):
        """Store a widget for later retrieval"""
        self.stored_widgets[name] = widget

        # Connect change tracking if enabled
        if self.enable_change_tracking:
            self.connect_widget_change_tracking(widget)

    def get_widget_by_name(self, name: str) -> Optional[QWidget]:
        """Retrieve a stored widget by name"""
        return self.stored_widgets.get(name)

    def connect_widget_change_tracking(self, widget: QWidget):
        """Connect change tracking to a specific widget"""
        try:
            if isinstance(widget, (QLineEdit, QTextEdit)):
                widget.textChanged.connect(self.on_data_changed)
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.valueChanged.connect(self.on_data_changed)
            elif isinstance(widget, QComboBox):
                widget.currentTextChanged.connect(self.on_data_changed)
            elif isinstance(widget, QCheckBox):
                widget.toggled.connect(self.on_data_changed)
        except Exception as e:
            logger.warning(
                f"Could not connect change tracking for {type(widget).__name__}: {e}"
            )

    def on_data_changed(self):
        """Handle data change events"""
        if self.enable_change_tracking:
            self.data_changed.emit()

    def validate_data(self) -> tuple[bool, List[str]]:
        """
        Validate widget data. Override in subclasses.

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        return True, []

    def update_validation_state(self):
        """Update validation state and emit signal"""
        is_valid, errors = self.validate_data()
        self.is_valid = is_valid
        self.validation_errors = errors
        self.validation_changed.emit(is_valid)

    def set_enabled_state(self, enabled: bool):
        """Set enabled state with visual feedback"""
        self.setEnabled(enabled)

        # Apply visual styling for disabled state
        if not enabled:
            self.setStyleSheet(
                self.get_base_style()
                + """
                QWidget { color: #888888; }
                QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    background-color: #f5f5f5;
                }
            """
            )
        else:
            self.setStyleSheet(self.get_base_style())


class BaseFormWidget(BaseWidget):
    """
    Base class for form-style widgets with labels and input controls.

    Provides:
    - Automatic form layout management
    - Label and input pairing
    - Consistent form styling
    - Field validation support
    """

    def __init__(self, parent=None, title: str = "", layout_type: str = "form"):
        super().__init__(parent, title)

        self.layout_type = layout_type  # 'form', 'grid', 'vbox', 'hbox'
        self.form_fields = {}  # Maps field names to (label, widget) tuples

        # Create main layout
        self.main_layout = self.create_main_layout()
        self.setLayout(self.main_layout)

        # Create title if provided
        if title:
            self.add_title(title)

    def create_main_layout(self):
        """Create the main layout based on layout_type"""
        if self.layout_type == "form":
            layout = QFormLayout()
            layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        elif self.layout_type == "grid":
            layout = QGridLayout()
        elif self.layout_type == "vbox":
            layout = QVBoxLayout()
        elif self.layout_type == "hbox":
            layout = QHBoxLayout()
        else:
            layout = QVBoxLayout()  # Default fallback

        layout.setContentsMargins(
            LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN
        )
        layout.setSpacing(LAYOUT_SPACING)

        return layout

    def add_title(self, title: str):
        """Add a title label to the form"""
        title_label = QLabel(title)
        title_label.setStyleSheet(
            """
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """
        )

        if isinstance(self.main_layout, QFormLayout):
            self.main_layout.addRow(title_label)
        else:
            self.main_layout.addWidget(title_label)

    def add_field(
        self,
        field_name: str,
        label_text: str,
        widget: QWidget,
        tooltip: str = "",
        required: bool = False,
    ) -> QWidget:
        """
        Add a field to the form with label and widget.

        Args:
            field_name: Internal name for the field
            label_text: Display text for the label
            widget: The input widget
            tooltip: Optional tooltip text
            required: Whether the field is required

        Returns:
            The widget that was added
        """
        # Create label
        label = QLabel(label_text)
        if required:
            label.setText(f"{label_text} *")
            label.setStyleSheet("QLabel { color: #c0392b; }")

        # Set tooltip if provided
        if tooltip:
            label.setToolTip(tooltip)
            widget.setToolTip(tooltip)

        # Store the field
        self.form_fields[field_name] = (label, widget)
        self.store_widget(field_name, widget)

        # Add to layout
        if isinstance(self.main_layout, QFormLayout):
            self.main_layout.addRow(label, widget)
        elif isinstance(self.main_layout, QGridLayout):
            row = self.main_layout.rowCount()
            self.main_layout.addWidget(label, row, 0)
            self.main_layout.addWidget(widget, row, 1)
        else:
            # For VBox/HBox layouts, create a horizontal layout for label+widget
            field_layout = QHBoxLayout()
            field_layout.addWidget(label)
            field_layout.addWidget(widget)
            self.main_layout.addLayout(field_layout)

        return widget

    def get_field_value(self, field_name: str) -> Any:
        """Get the value from a form field"""
        widget = self.get_widget_by_name(field_name)
        if not widget:
            return None

        if isinstance(widget, QLineEdit):
            return widget.text()
        elif isinstance(widget, QTextEdit):
            return widget.toPlainText()
        elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
            return widget.value()
        elif isinstance(widget, QComboBox):
            return widget.currentText()
        elif isinstance(widget, QCheckBox):
            return widget.isChecked()
        else:
            logger.warning(
                f"Unknown widget type for field {field_name}: {type(widget)}"
            )
            return None

    def set_field_value(self, field_name: str, value: Any):
        """Set the value for a form field"""
        widget = self.get_widget_by_name(field_name)
        if not widget:
            return

        try:
            if isinstance(widget, QLineEdit):
                widget.setText(str(value) if value is not None else "")
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value) if value is not None else "")
            elif isinstance(widget, QSpinBox):
                widget.setValue(int(value) if value is not None else 0)
            elif isinstance(widget, QDoubleSpinBox):
                widget.setValue(float(value) if value is not None else 0.0)
            elif isinstance(widget, QComboBox):
                index = widget.findText(str(value))
                if index >= 0:
                    widget.setCurrentIndex(index)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(bool(value) if value is not None else False)
        except Exception as e:
            logger.warning(f"Could not set value for field {field_name}: {e}")

    def collect_form_data(self) -> Dict[str, Any]:
        """Collect all form data into a dictionary"""
        data = {}
        for field_name in self.form_fields.keys():
            data[field_name] = self.get_field_value(field_name)
        return data

    def populate_form_data(self, data: Dict[str, Any]):
        """Populate form fields from a data dictionary"""
        for field_name, value in data.items():
            self.set_field_value(field_name, value)


class BaseTabWidget(BaseWidget):
    """
    Base class for tab-based interfaces.

    Provides:
    - Standardized tab creation and management
    - Tab validation and error indication
    - Responsive tab behavior
    - Data collection across tabs
    """

    def __init__(self, parent=None, title: str = ""):
        super().__init__(parent, title)

        self.tabs = {}  # Maps tab names to tab widgets
        self.tab_validators = {}  # Maps tab names to validation functions

        # Create main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.setup_tab_widget()

        main_layout.addWidget(self.tab_widget)
        self.setLayout(main_layout)

    def setup_tab_widget(self):
        """Setup the tab widget with responsive behavior"""
        from core.ui import TabWidgetResponsive

        TabWidgetResponsive.setup_tab_widget(self.tab_widget)
        make_widget_responsive(self.tab_widget)

    def add_tab(
        self,
        tab_name: str,
        tab_widget: QWidget,
        tab_title: str,
        validator: Optional[Callable] = None,
    ) -> QWidget:
        """
        Add a tab to the tab widget.

        Args:
            tab_name: Internal name for the tab
            tab_widget: The widget to display in the tab
            tab_title: Display title for the tab
            validator: Optional validation function for the tab

        Returns:
            The tab widget that was added
        """
        # Store tab reference
        self.tabs[tab_name] = tab_widget

        # Store validator if provided
        if validator:
            self.tab_validators[tab_name] = validator

        # Add to tab widget
        self.tab_widget.addTab(tab_widget, tab_title)

        # Connect change tracking if the tab widget supports it
        if hasattr(tab_widget, "data_changed"):
            tab_widget.data_changed.connect(self.on_data_changed)

        return tab_widget

    def get_tab(self, tab_name: str) -> Optional[QWidget]:
        """Get a tab widget by name"""
        return self.tabs.get(tab_name)

    def set_tab_enabled(self, tab_name: str, enabled: bool):
        """Enable or disable a specific tab"""
        tab_widget = self.get_tab(tab_name)
        if tab_widget:
            index = self.tab_widget.indexOf(tab_widget)
            if index >= 0:
                self.tab_widget.setTabEnabled(index, enabled)

    def validate_all_tabs(self) -> tuple[bool, Dict[str, List[str]]]:
        """
        Validate all tabs that have validators.

        Returns:
            Tuple of (all_valid, dict_of_tab_errors)
        """
        all_valid = True
        tab_errors = {}

        for tab_name, validator in self.tab_validators.items():
            try:
                is_valid, errors = validator()
                if not is_valid:
                    all_valid = False
                    tab_errors[tab_name] = errors
            except Exception as e:
                all_valid = False
                tab_errors[tab_name] = [f"Validation error: {str(e)}"]
                logger.error(f"Error validating tab {tab_name}: {e}")

        return all_valid, tab_errors

    def collect_tab_data(self) -> Dict[str, Any]:
        """Collect data from all tabs that support data collection"""
        data = {}

        for tab_name, tab_widget in self.tabs.items():
            try:
                if hasattr(tab_widget, "collect_form_data"):
                    data[tab_name] = tab_widget.collect_form_data()
                elif hasattr(tab_widget, "collect_data"):
                    data[tab_name] = tab_widget.collect_data()
            except Exception as e:
                logger.warning(f"Could not collect data from tab {tab_name}: {e}")

        return data

    def populate_tab_data(self, data: Dict[str, Any]):
        """Populate data to all tabs that support data population"""
        for tab_name, tab_data in data.items():
            tab_widget = self.get_tab(tab_name)
            if not tab_widget:
                continue

            try:
                if hasattr(tab_widget, "populate_form_data"):
                    tab_widget.populate_form_data(tab_data)
                elif hasattr(tab_widget, "populate_data"):
                    tab_widget.populate_data(tab_data)
            except Exception as e:
                logger.warning(f"Could not populate data to tab {tab_name}: {e}")


class BaseDataWidget(BaseWidget):
    """
    Base class for widgets that handle data collection and population.

    Provides:
    - Standardized data collection interface
    - Data validation and error handling
    - Change tracking for data modifications
    - Integration with EditorDataInterface patterns
    """

    def __init__(self, parent=None, title: str = "", data_type: str = ""):
        super().__init__(parent, title)

        self.data_type = data_type
        self.current_data = {}
        self.default_data = {}

    def set_default_data(self, default_data: Dict[str, Any]):
        """Set default data values for the widget"""
        self.default_data = default_data.copy()

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the widget. Override in subclasses.

        Returns:
            Dictionary containing collected data
        """
        return {}

    def populate_data(self, data: Dict[str, Any]):
        """
        Populate widget with data. Override in subclasses.

        Args:
            data: Dictionary containing data to populate
        """

    def reset_to_defaults(self):
        """Reset widget to default values"""
        self.populate_data(self.default_data)
        self.current_data = self.default_data.copy()

    def has_changes(self) -> bool:
        """Check if current data differs from stored data"""
        current = self.collect_data()
        return current != self.current_data

    def save_current_state(self):
        """Save current widget state as the baseline"""
        self.current_data = self.collect_data()

    def restore_saved_state(self):
        """Restore widget to last saved state"""
        self.populate_data(self.current_data)
