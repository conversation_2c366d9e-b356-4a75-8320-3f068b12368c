"""
Example Standardized Tag Configuration for Adventure Chess Creator

This file demonstrates the standardized UI patterns and styling that all
tag configurations should follow using the enhanced BaseTagConfig class.
"""

from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class ExampleStandardizedConfig(BaseTagConfig):
    """
    Example configuration demonstrating standardized UI patterns.
    
    This serves as a template for creating new tag configurations
    with consistent styling and layout.
    """
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "exampleStandardized")
    
    def get_title(self) -> str:
        return "🎯 Example Standardized Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the UI using standardized patterns."""
        try:
            # Create main layout with description
            main_layout = self.create_standard_layout(
                parent_layout, 
                "This is an example configuration demonstrating standardized UI patterns."
            )
            
            # Basic Settings Section
            basic_form = self.create_form_section(main_layout, "Basic Settings")
            
            # Standard spinner example
            duration_spinner = self.create_standard_spinner(
                1, 10, 3, " turns", 
                "Duration of the effect in turns"
            )
            self.add_widget_with_signals("duration", duration_spinner)
            basic_form.addRow("Duration:", duration_spinner)
            
            # Standard combo box example
            effect_type_combo = self.create_standard_combo([
                "Immediate",
                "Delayed", 
                "Continuous",
                "Triggered"
            ], "Type of effect timing")
            self.add_widget_with_signals("effect_type", effect_type_combo)
            basic_form.addRow("Effect Type:", effect_type_combo)
            
            # Advanced Options Section
            advanced_layout = self.create_vertical_section(main_layout, "Advanced Options")
            
            # Standard checkbox examples
            persistent_check = self.create_standard_checkbox(
                "Persistent effect",
                "Effect continues even after piece moves"
            )
            self.add_widget_with_signals("persistent", persistent_check)
            advanced_layout.addWidget(persistent_check)
            
            stackable_check = self.create_standard_checkbox(
                "Stackable with other effects",
                "Multiple instances can affect the same target"
            )
            self.add_widget_with_signals("stackable", stackable_check)
            advanced_layout.addWidget(stackable_check)
            
            # Target Selection Section
            target_layout = self.create_vertical_section(main_layout, "Target Selection")
            
            # Inline piece selector example
            target_selector = self.create_inline_piece_selector(
                "Target Pieces", allow_costs=True
            )
            if target_selector:
                self.add_widget_with_signals("target_selector", target_selector, auto_connect=False)
                target_layout.addWidget(target_selector)
            
            # Pattern Configuration Section
            pattern_layout = self.create_horizontal_section(main_layout, "Pattern Configuration")
            
            # Range editor button example
            pattern_button = self.create_range_editor_button(
                "Edit Effect Pattern...", "edit_effect_pattern"
            )
            pattern_layout.addWidget(pattern_button)
            
            # Add separator
            main_layout.addWidget(self.create_separator())
            
            # Action Buttons Section
            button_layout = self.create_horizontal_section(main_layout)
            
            # Standard button examples
            reset_button = self.create_standard_button(
                "Reset to Defaults", "Reset all settings to default values", "secondary"
            )
            reset_button.clicked.connect(self.reset_to_defaults)
            button_layout.addWidget(reset_button)
            
            button_layout.addStretch()  # Push buttons to sides
            
            test_button = self.create_standard_button(
                "Test Configuration", "Test the current configuration", "primary"
            )
            test_button.clicked.connect(self.test_configuration)
            button_layout.addWidget(test_button)
            
            self.log_debug("Standardized UI created successfully")
            
        except Exception as e:
            self.log_error(f"Error creating standardized UI: {e}")
    
    def edit_effect_pattern(self):
        """Example method for opening range editor dialog."""
        try:
            from dialogs.range_editor_dialog import RangeEditorDialog
            
            dialog = RangeEditorDialog(
                parent=self.editor,
                initial_pattern=[[0 for _ in range(8)] for _ in range(8)],
                piece_pos=[3, 3],
                include_starting_square=False,
                friendly_only=False
            )
            
            if dialog.exec() == dialog.DialogCode.Accepted:
                pattern = dialog.get_pattern()
                self.log_debug(f"Effect pattern updated: {pattern}")
                
                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()
                    
        except Exception as e:
            self.log_error(f"Error opening effect pattern editor: {e}")
    
    def reset_to_defaults(self):
        """Reset all settings to default values."""
        try:
            # Reset spinner
            duration_spinner = self.get_widget_by_name("duration")
            if duration_spinner:
                duration_spinner.setValue(3)
            
            # Reset combo
            effect_type_combo = self.get_widget_by_name("effect_type")
            if effect_type_combo:
                effect_type_combo.setCurrentIndex(0)
            
            # Reset checkboxes
            persistent_check = self.get_widget_by_name("persistent")
            if persistent_check:
                persistent_check.setChecked(False)
                
            stackable_check = self.get_widget_by_name("stackable")
            if stackable_check:
                stackable_check.setChecked(False)
            
            self.log_debug("Settings reset to defaults")
            
            # Mark as changed
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()
                
        except Exception as e:
            self.log_error(f"Error resetting to defaults: {e}")
    
    def test_configuration(self):
        """Test the current configuration."""
        try:
            data = self.collect_data()
            self.log_debug(f"Current configuration: {data}")
            
            # Here you could add validation logic
            # For now, just show a simple message
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self.editor,
                "Configuration Test",
                f"Configuration is valid!\n\nCurrent settings:\n{data}"
            )
            
        except Exception as e:
            self.log_error(f"Error testing configuration: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI widgets with data."""
        try:
            self.log_debug(f"Populating data: {data}")
            
            # Populate duration
            duration_spinner = self.get_widget_by_name("duration")
            if duration_spinner:
                duration_spinner.setValue(data.get("duration", 3))
            
            # Populate effect type
            effect_type_combo = self.get_widget_by_name("effect_type")
            if effect_type_combo:
                effect_type = data.get("effectType", "Immediate")
                index = effect_type_combo.findText(effect_type)
                if index >= 0:
                    effect_type_combo.setCurrentIndex(index)
            
            # Populate checkboxes
            persistent_check = self.get_widget_by_name("persistent")
            if persistent_check:
                persistent_check.setChecked(data.get("persistent", False))
                
            stackable_check = self.get_widget_by_name("stackable")
            if stackable_check:
                stackable_check.setChecked(data.get("stackable", False))
            
            # Populate target selector
            target_selector = self.get_widget_by_name("target_selector")
            if target_selector and hasattr(target_selector, 'populate_data'):
                target_selector.populate_data(data.get("targets", []))
            
            self.log_debug("Data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}
            
            # Collect duration
            duration_spinner = self.get_widget_by_name("duration")
            if duration_spinner:
                data["duration"] = duration_spinner.value()
            
            # Collect effect type
            effect_type_combo = self.get_widget_by_name("effect_type")
            if effect_type_combo:
                data["effectType"] = effect_type_combo.currentText()
            
            # Collect checkboxes
            persistent_check = self.get_widget_by_name("persistent")
            if persistent_check:
                data["persistent"] = persistent_check.isChecked()
                
            stackable_check = self.get_widget_by_name("stackable")
            if stackable_check:
                data["stackable"] = stackable_check.isChecked()
            
            # Collect target selector data
            target_selector = self.get_widget_by_name("target_selector")
            if target_selector and hasattr(target_selector, 'collect_data'):
                data["targets"] = target_selector.collect_data()
            
            self.log_debug(f"Data collected: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
