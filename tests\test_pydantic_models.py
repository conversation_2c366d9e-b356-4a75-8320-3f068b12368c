#!/usr/bin/env python3
"""
Comprehensive tests for Adventure Chess Pydantic models
Tests all schemas for validation, serialization, and edge cases
"""

import pytest
import json
from typing import Dict, Any
from pydantic import ValidationError

# Import all Pydantic models
from schemas.piece_schema import Piece, Movement
from schemas.ability_schema import Ability
from schemas.base import (
    MovementType, RechargeType, PieceRole, ActivationMode,
    create_default_pattern, create_default_range_mask
)


class TestPieceSchema:
    """Test suite for Piece Pydantic model"""
    
    def test_piece_creation_minimal(self):
        """Test creating a piece with minimal required fields"""
        piece_data = {
            "name": "Test Piece",
            "role": "Commander"
        }
        
        piece = Piece(**piece_data)
        assert piece.name == "Test Piece"
        assert piece.role == PieceRole.COMMANDER
        assert piece.version == "1.0.0"  # Default version
        assert piece.can_capture is True  # Default value
        assert piece.abilities == []  # Default empty list
    
    def test_piece_creation_complete(self):
        """Test creating a piece with all fields"""
        piece_data = {
            "version": "1.0.0",
            "name": "Complete Test Piece",
            "description": "A fully configured test piece",
            "role": "Commander",  # Use valid enum value
            "movement": {
                "type": "custom",
                "pattern": create_default_pattern(),
                "piece_position": [3, 3]
            },
            "can_capture": True,
            "abilities": ["test_ability_1", "test_ability_2"],
            "max_points": 10,
            "starting_points": 5,
            "recharge_type": "turnRecharge"
        }

        piece = Piece(**piece_data)
        assert piece.name == "Complete Test Piece"
        assert piece.role == PieceRole.COMMANDER
        assert piece.movement.type == MovementType.CUSTOM
        assert piece.max_points == 10
        assert piece.starting_points == 5
        assert piece.recharge_type == RechargeType.TURN_RECHARGE
        assert len(piece.abilities) == 2
    
    def test_piece_validation_errors(self):
        """Test piece validation errors"""
        # Missing required name
        with pytest.raises(ValidationError) as exc_info:
            Piece(role="Commander")
        assert "name" in str(exc_info.value)
        
        # Invalid role
        with pytest.raises(ValidationError) as exc_info:
            Piece(name="Test", role="InvalidRole")
        assert "role" in str(exc_info.value)
        
        # Invalid points (negative)
        with pytest.raises(ValidationError) as exc_info:
            Piece(name="Test", role="Commander", max_points=-1)
        assert "max_points" in str(exc_info.value)
    
    def test_piece_serialization(self):
        """Test piece serialization to/from JSON"""
        piece_data = {
            "name": "Serialization Test",
            "role": "Commander",
            "max_points": 5
        }
        
        # Create piece
        piece = Piece(**piece_data)
        
        # Serialize to dict
        piece_dict = piece.model_dump()
        assert piece_dict["name"] == "Serialization Test"
        assert piece_dict["role"] == "Commander"
        
        # Serialize to JSON
        piece_json = piece.model_dump_json()
        assert isinstance(piece_json, str)
        
        # Deserialize from JSON
        piece_from_json = Piece.model_validate_json(piece_json)
        assert piece_from_json.name == piece.name
        assert piece_from_json.role == piece.role


class TestMovementSchema:
    """Test suite for Movement Pydantic model"""
    
    def test_movement_creation_default(self):
        """Test creating movement with defaults"""
        movement = Movement()
        assert movement.type == MovementType.ORTHOGONAL
        assert movement.pattern is None
        assert movement.piece_position is None
    
    def test_movement_custom_pattern(self):
        """Test movement with custom pattern"""
        pattern = create_default_pattern()
        movement_data = {
            "type": "custom",
            "pattern": pattern,
            "piece_position": [3, 3]
        }
        
        movement = Movement(**movement_data)
        assert movement.type == MovementType.CUSTOM
        assert movement.pattern is not None
        assert movement.piece_position == [3, 3]
    
    def test_movement_validation_errors(self):
        """Test movement validation errors"""
        # Invalid piece position
        with pytest.raises(ValidationError) as exc_info:
            Movement(piece_position=[8, 8])  # Out of bounds
        assert "piece_position" in str(exc_info.value)
        
        # Invalid piece position format
        with pytest.raises(ValidationError) as exc_info:
            Movement(piece_position=[3])  # Wrong length
        assert "piece_position" in str(exc_info.value)


class TestAbilitySchema:
    """Test suite for Ability Pydantic model"""
    
    def test_ability_creation_minimal(self):
        """Test creating ability with minimal required fields"""
        ability_data = {
            "name": "Test Ability"
        }
        
        ability = Ability(**ability_data)
        assert ability.name == "Test Ability"
        assert ability.description == ""  # Default
        assert ability.cost == 0  # Default
        assert ability.activation_mode == ActivationMode.AUTO
        assert ability.tags == []  # Default empty list
    
    def test_ability_creation_complete(self):
        """Test creating ability with all common fields"""
        ability_data = {
            "name": "Complete Test Ability",
            "description": "A fully configured test ability",
            "cost": 3,
            "activation_mode": "click",  # Use valid enum value
            "tags": ["movement", "attack"],
            "range_mask": create_default_range_mask(),
            "piece_position": [3, 3],
            "range_friendly_only": True,
            "area_size": 2,
            "move_distance": 3,
            "summon_max": 2
        }

        ability = Ability(**ability_data)
        assert ability.name == "Complete Test Ability"
        assert ability.cost == 3
        assert ability.activation_mode == ActivationMode.CLICK
        assert len(ability.tags) == 2
        assert ability.range_friendly_only is True
        assert ability.area_size == 2
        assert ability.move_distance == 3
        assert ability.summon_max == 2
    
    def test_ability_validation_errors(self):
        """Test ability validation errors"""
        # Missing required name
        with pytest.raises(ValidationError) as exc_info:
            Ability()
        assert "name" in str(exc_info.value)
        
        # Name too long
        with pytest.raises(ValidationError) as exc_info:
            Ability(name="x" * 51)  # Exceeds max length
        assert "name" in str(exc_info.value)
        
        # Negative cost
        with pytest.raises(ValidationError) as exc_info:
            Ability(name="Test", cost=-1)
        assert "cost" in str(exc_info.value)
    
    def test_ability_serialization(self):
        """Test ability serialization to/from JSON"""
        ability_data = {
            "name": "Serialization Test Ability",
            "cost": 2,
            "tags": ["test_tag"]
        }
        
        # Create ability
        ability = Ability(**ability_data)
        
        # Serialize to dict
        ability_dict = ability.model_dump()
        assert ability_dict["name"] == "Serialization Test Ability"
        assert ability_dict["cost"] == 2
        
        # Serialize to JSON
        ability_json = ability.model_dump_json()
        assert isinstance(ability_json, str)
        
        # Deserialize from JSON
        ability_from_json = Ability.model_validate_json(ability_json)
        assert ability_from_json.name == ability.name
        assert ability_from_json.cost == ability.cost


class TestModelIntegration:
    """Test integration between different models"""
    
    def test_piece_with_abilities_references(self):
        """Test piece referencing abilities"""
        piece_data = {
            "name": "Integration Test Piece",
            "role": "Commander",
            "abilities": ["ability_1", "ability_2", "ability_3"]
        }
        
        piece = Piece(**piece_data)
        assert len(piece.abilities) == 3
        assert "ability_1" in piece.abilities
    
    def test_complex_movement_pattern(self):
        """Test complex movement patterns"""
        # Create a custom 8x8 pattern
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        # Set some movement squares
        pattern[2][3] = 1  # Can move
        pattern[4][3] = 1  # Can move
        pattern[3][2] = 1  # Can move
        pattern[3][4] = 1  # Can move
        
        movement_data = {
            "type": "custom",
            "pattern": pattern,
            "piece_position": [3, 3]
        }
        
        movement = Movement(**movement_data)
        assert movement.pattern[2][3] == 1
        assert movement.pattern[4][3] == 1
        assert movement.pattern[0][0] == 0  # No movement
    
    def test_model_version_consistency(self):
        """Test that all models have consistent versioning"""
        piece = Piece(name="Version Test", role="Commander")
        ability = Ability(name="Version Test Ability")
        
        # Both should have version fields
        assert hasattr(piece, 'version')
        assert hasattr(ability, 'version')
        
        # Versions should be consistent
        assert piece.version == ability.version


class TestEdgeCases:
    """Test edge cases and boundary conditions"""
    
    def test_empty_strings_and_lists(self):
        """Test handling of empty strings and lists"""
        piece = Piece(
            name="Edge Case Test",
            role="Commander",
            description="",  # Empty description
            abilities=[]  # Empty abilities list
        )
        
        assert piece.description == ""
        assert piece.abilities == []
    
    def test_unicode_and_special_characters(self):
        """Test handling of unicode and special characters"""
        piece_data = {
            "name": "Tëst Piëcé 🏰",
            "role": "Commander",
            "description": "Spëcial chäractërs: àáâãäåæçèéêë"
        }
        
        piece = Piece(**piece_data)
        assert "🏰" in piece.name
        assert "àáâãäåæçèéêë" in piece.description
    
    def test_large_numbers(self):
        """Test handling of large numbers within valid ranges"""
        piece = Piece(
            name="Large Numbers Test",
            role="Commander",
            max_points=99,  # Use maximum allowed value
            starting_points=99  # Use maximum allowed value
        )

        assert piece.max_points == 99
        assert piece.starting_points == 99


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
