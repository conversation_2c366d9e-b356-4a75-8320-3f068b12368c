"""
Piece Editor for Adventure Chess Creator

This file provides backward compatibility by importing the refactored
PieceEditorWindow from the piece_editor package.

The original monolithic piece_editor.py (1,882 lines) has been refactored
into a modular package with the following components:
- piece_editor_main.py: Main coordinator class
- piece_data_handlers.py: Data operations and validation
- piece_ui_components.py: UI widget creation and layout
- piece_movement_manager.py: Movement pattern management
- piece_promotion_manager.py: Promotion system management
- piece_icon_manager.py: Icon and visual management

Original file archived at: archive/piece_editor_refactoring/piece_editor_original.py
"""

# Import the refactored PieceEditorWindow
from .piece_editor import PieceEditorWindow

# Re-export for backward compatibility
__all__ = ["PieceEditorWindow"]

# Version info
__version__ = "1.1.0"
__refactored__ = "2025-06-25"
