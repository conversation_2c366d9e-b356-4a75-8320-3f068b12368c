{"tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_piece_save_load_cycle": true, "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_ability_save_load_cycle": true, "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_piece_ui_workflow_simulation": true, "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_ability_ui_workflow_simulation": true, "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_direct_to_pydantic_compatibility": true, "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_large_data_save_load": true, "tests/test_application_stress.py::TestApplicationStress::test_concurrent_data_access": true, "tests/test_application_stress.py::TestApplicationStress::test_data_manager_stress": true}