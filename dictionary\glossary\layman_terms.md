# Layman's Terms Glossary

This glossary explains technical concepts from Adventure Chess Creator in simple, accessible language for non-programmers, new contributors, and anyone wanting to understand the system without deep technical knowledge.

## Core Concepts

### Single Source of Truth
**Simple Explanation**: Like having one master copy of important information instead of multiple copies that might get out of sync.

**Real-World Analogy**: Imagine you have a family calendar. Instead of everyone keeping their own copy (which might have different events), you have one calendar on the refrigerator that everyone checks and updates. That's your "single source of truth" for family schedules.

**In Adventure Chess**: Instead of storing piece information in multiple places where it might become inconsistent, we keep one master record of each piece's configuration that everyone refers to.

### Data Ownership
**Simple Explanation**: Deciding which part of the program is responsible for taking care of specific information.

**Real-World Analogy**: In a library, the librarian owns the book catalog, the security guard owns the visitor log, and the janitor owns the cleaning schedule. Each person is responsible for their specific information.

**In Adventure Chess**: The Piece Editor owns all information about pieces, the Ability Editor owns all information about abilities, and so on.

### Base Classes
**Simple Explanation**: Templates that provide common features to similar things.

**Real-World Analogy**: Like a cookie cutter that gives all cookies the same basic shape, but you can still decorate each cookie differently.

**In Adventure Chess**: All editors (Piece Editor, Ability Editor) use the same base template that provides common features like saving and loading files, but each editor adds its own specific features.

### Properties (Programming)
**Simple Explanation**: A way to make old names for things still work even when you've reorganized where the information is stored.

**Real-World Analogy**: Like forwarding mail from your old address to your new address. People can still send mail to your old address, but it automatically gets redirected to where you actually live now.

**In Adventure Chess**: Old code can still ask for `current_custom_pattern`, but it automatically gets redirected to the new location where movement patterns are stored.

### Data Interface
**Simple Explanation**: A standardized way for different parts of the program to exchange information.

**Real-World Analogy**: Like having a standard electrical outlet. Any device with the right plug can connect and get power, regardless of what's behind the wall.

**In Adventure Chess**: All editors can save and load data using the same standard methods, even though they handle different types of information.

## User Interface Concepts

### Widget
**Simple Explanation**: Any interactive element you can see and use in the program's window.

**Examples**: Buttons you click, text boxes you type in, dropdown menus you select from, checkboxes you check or uncheck.

**In Adventure Chess**: The name field where you type a piece's name, the dropdown where you select movement type, the grid where you design movement patterns.

### Editor
**Simple Explanation**: A window or section of the program where you create and modify specific types of content.

**Real-World Analogy**: Like different tools in a workshop - you use a saw for cutting wood, a drill for making holes, and sandpaper for smoothing. Each tool is designed for specific tasks.

**In Adventure Chess**: The Piece Editor for creating chess pieces, the Ability Editor for creating special abilities.

### Dialog
**Simple Explanation**: A small window that pops up to ask you questions or let you configure specific settings.

**Examples**: The "Save As" dialog when you save a file, a settings dialog where you adjust preferences.

**In Adventure Chess**: Windows that pop up to configure complex ability settings or movement patterns.

## Data Concepts

### Configuration
**Simple Explanation**: All the settings and options that define how something behaves.

**Real-World Analogy**: Like the settings on your car - seat position, mirror angles, radio presets. These configurations make the car work the way you want it to.

**In Adventure Chess**: All the settings that define a piece - its name, how it moves, what abilities it has, how many action points it gets.

### Schema
**Simple Explanation**: The rules about what information must be included and how it should be organized.

**Real-World Analogy**: Like a form you fill out - it tells you which fields are required, which are optional, and what format to use (like phone numbers needing 10 digits).

**In Adventure Chess**: The rules that say a piece must have a name and movement type, but description is optional.

### Validation
**Simple Explanation**: Checking that information follows the rules and makes sense.

**Real-World Analogy**: Like a spell-checker that underlines misspelled words, or a form that won't submit if you forgot to fill in required fields.

**In Adventure Chess**: Making sure piece names aren't empty, movement patterns are valid, and ability costs are reasonable numbers.

### Migration
**Simple Explanation**: Converting old information to work with new systems.

**Real-World Analogy**: Like converting old VHS tapes to digital files so you can watch them on modern devices.

**In Adventure Chess**: Converting save files from older versions of the program to work with the current version.

## File and Data Management

### Save File
**Simple Explanation**: A file that contains all your work so you can come back to it later.

**Real-World Analogy**: Like saving a document in Microsoft Word - all your work gets stored in a file that you can open later.

**In Adventure Chess**: Files that contain your custom pieces and abilities so you can use them again or share them with others.

### Serialization
**Simple Explanation**: Converting information from the program's memory into a format that can be saved to a file.

**Real-World Analogy**: Like packing a suitcase - you take all your clothes (information) and organize them into a suitcase (file) so you can transport them.

**In Adventure Chess**: Taking all the piece configuration information and organizing it into a save file.

### Legacy Support
**Simple Explanation**: Making sure old files and old ways of doing things still work even after you've improved the program.

**Real-World Analogy**: Like how modern cars still have keys even though many now use push-button start - they support both old and new ways.

**In Adventure Chess**: Making sure pieces created in older versions of the program still work in the new version.

## Programming Patterns

### Inheritance
**Simple Explanation**: When one thing gets features from another thing, like how children inherit traits from their parents.

**Real-World Analogy**: All vehicles have wheels and engines, but cars add doors and seats while motorcycles add handlebars. Cars and motorcycles inherit basic vehicle features but add their own specific features.

**In Adventure Chess**: All editors inherit basic features like saving and loading, but each adds its own specific features for pieces or abilities.

### Interface
**Simple Explanation**: A contract that says "if you want to work with this system, you must provide these specific capabilities."

**Real-World Analogy**: Like a driver's license - it doesn't matter what kind of car you drive, but if you want to drive on public roads, you must demonstrate certain standard capabilities.

**In Adventure Chess**: All data handlers must provide standard capabilities like collecting data and populating UI, but they can implement these capabilities in their own way.

### Registry
**Simple Explanation**: A central list that keeps track of what exists and where to find it.

**Real-World Analogy**: Like a phone book that lists everyone's name and how to contact them, or a library catalog that tells you where to find each book.

**In Adventure Chess**: A central list that keeps track of which part of the program is responsible for each type of information.

## Common Operations

### Populate
**Simple Explanation**: Filling in forms or displays with information from storage.

**Real-World Analogy**: Like auto-filling a form with your saved information, or loading your preferences when you open an app.

**In Adventure Chess**: Loading a saved piece and filling in all the editor fields with that piece's information.

### Collect
**Simple Explanation**: Gathering information from forms or displays to save it.

**Real-World Analogy**: Like collecting all the information from a survey form to store in a database.

**In Adventure Chess**: Gathering all the information you've entered in the editor to save as a piece file.

### Validate
**Simple Explanation**: Checking that everything is correct and complete before proceeding.

**Real-World Analogy**: Like proofreading an essay before submitting it, or checking that you have your passport before going to the airport.

**In Adventure Chess**: Making sure all required fields are filled in and all values make sense before saving a piece.

## Getting Help

If you encounter terms not explained here:
1. Check the [Technical Terms Glossary](technical_terms.md) for more detailed explanations
2. Look in the relevant documentation sections for context
3. Ask questions - understanding the system helps everyone contribute better

Remember: Every expert was once a beginner. These concepts become natural with practice!
