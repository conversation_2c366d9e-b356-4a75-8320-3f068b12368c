#!/usr/bin/env python3
"""
Theme Utilities for Adventure Chess Creator

This module provides utility functions and patterns for manual theme application
throughout the application. Manual theming provides better control and consistency
than automatic detection.

Recommended Usage Patterns:
    # Manual theme application (recommended)
    apply_theme_to_widget(save_button, 'primary_button')
    apply_theme_to_widget(delete_button, 'danger_button')
    
    # Themed widget factories (best for new code)
    save_btn = create_themed_button("Save", 'primary_button')
    delete_btn = create_themed_button("Delete", 'danger_button')
    
    # Bulk manual theming
    apply_manual_themes_to_container(my_dialog)
"""

import functools
import re
from typing import Callable, Dict, List, Optional, Any, Union
from contextlib import contextmanager
from PyQt6.QtWidgets import QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox, QGroupBox, QTabWidget
from PyQt6.QtCore import QObject

from .theme_manager import ThemeManager, apply_theme_to_widget, get_color


class ManualThemeUtils:
    """Utility class for manual theming operations - provides better control than automatic theming"""
    
    # Recommended manual theme assignments by widget purpose
    MANUAL_THEME_ASSIGNMENTS = {
        # Action buttons
        'save_buttons': 'primary_button',
        'apply_buttons': 'primary_button', 
        'ok_buttons': 'primary_button',
        'submit_buttons': 'primary_button',
        'confirm_buttons': 'primary_button',
        
        # Creation buttons
        'new_buttons': 'success_button',
        'add_buttons': 'success_button',
        'create_buttons': 'success_button',
        'insert_buttons': 'success_button',
        
        # Destructive buttons
        'delete_buttons': 'danger_button',
        'remove_buttons': 'danger_button',
        'clear_buttons': 'danger_button',
        'cancel_buttons': 'danger_button',
        
        # Secondary actions
        'load_buttons': 'secondary_button',
        'browse_buttons': 'secondary_button',
        'select_buttons': 'secondary_button',
        'refresh_buttons': 'secondary_button',
        'close_buttons': 'secondary_button',
        
        # Input fields
        'text_inputs': 'line_edit',
        'text_areas': 'text_edit',
        'dropdowns': 'combo_box',
        
        # Labels
        'titles': 'header_label',
        'section_headers': 'subheader_label',
        'descriptions': 'body_label',
        'hints': 'muted_label',
        
        # Containers
        'form_groups': 'group_box',
        'tab_containers': 'tab_widget',
    }
    
    @classmethod
    def apply_manual_themes_to_container(cls, container: QWidget, theme_map: Optional[Dict[str, str]] = None) -> None:
        """
        Apply manual themes to widgets in a container using explicit mapping
        
        This provides better control than automatic detection and ensures
        consistent theming across the application.
        
        Args:
            container: Container widget to theme
            theme_map: Optional custom theme mapping (widget_name -> theme_name)
        """
        if theme_map is None:
            theme_map = {}
        
        # Apply themes to buttons based on their text and purpose
        buttons = container.findChildren(QPushButton)
        for button in buttons:
            button_text = button.text().lower()
            
            # Check custom mapping first
            widget_name = button.objectName()
            if widget_name and widget_name in theme_map:
                apply_theme_to_widget(button, theme_map[widget_name])
                continue
            
            # Apply manual theme based on button text/purpose
            theme_applied = False
            
            # Primary actions
            if any(word in button_text for word in ['save', 'apply', 'ok', 'submit', 'confirm', 'accept']):
                apply_theme_to_widget(button, 'primary_button')
                theme_applied = True
            # Creation actions  
            elif any(word in button_text for word in ['new', 'add', 'create', 'insert']):
                apply_theme_to_widget(button, 'success_button')
                theme_applied = True
            # Destructive actions
            elif any(word in button_text for word in ['delete', 'remove', 'clear', 'cancel', 'abort']):
                apply_theme_to_widget(button, 'danger_button')
                theme_applied = True
            # Secondary actions
            elif any(word in button_text for word in ['load', 'browse', 'select', 'refresh', 'close']):
                apply_theme_to_widget(button, 'secondary_button')
                theme_applied = True
            
            # Default to secondary if no specific match
            if not theme_applied:
                apply_theme_to_widget(button, 'secondary_button')
        
        # Apply themes to input widgets
        line_edits = container.findChildren(QLineEdit)
        for edit in line_edits:
            apply_theme_to_widget(edit, 'line_edit')
            
        text_edits = container.findChildren(QTextEdit)
        for edit in text_edits:
            apply_theme_to_widget(edit, 'text_edit')
            
        combo_boxes = container.findChildren(QComboBox)
        for combo in combo_boxes:
            apply_theme_to_widget(combo, 'combo_box')
        
        # Apply themes to containers
        group_boxes = container.findChildren(QGroupBox)
        for group in group_boxes:
            apply_theme_to_widget(group, 'group_box')
            
        tab_widgets = container.findChildren(QTabWidget)
        for tab in tab_widgets:
            apply_theme_to_widget(tab, 'tab_widget')
    
    @classmethod
    def get_recommended_button_theme(cls, button_text: str, context: str = '') -> str:
        """
        Get recommended theme for a button based on its text and context
        
        Args:
            button_text: Text of the button
            context: Optional context (e.g., 'dialog', 'toolbar', 'form')
            
        Returns:
            Recommended theme name
        """
        text = button_text.lower()
        
        # Primary actions (most important)
        if any(word in text for word in ['save', 'apply', 'ok', 'submit', 'confirm', 'accept']):
            return 'primary_button'
        
        # Creation actions (positive)
        if any(word in text for word in ['new', 'add', 'create', 'insert', 'plus']):
            return 'success_button'
        
        # Destructive actions (dangerous)
        if any(word in text for word in ['delete', 'remove', 'clear', 'cancel', 'abort', 'trash']):
            return 'danger_button'
        
        # Secondary actions (neutral)
        return 'secondary_button'


def apply_manual_themes(container: QWidget, theme_assignments: Optional[Dict[str, str]] = None) -> None:
    """
    Apply manual themes to a container - recommended approach for consistent theming
    
    Args:
        container: Container widget to theme
        theme_assignments: Optional custom theme assignments (widget_name -> theme_name)
    """
    ManualThemeUtils.apply_manual_themes_to_container(container, theme_assignments)


def create_themed_dialog_buttons(button_configs: List[Dict[str, Any]]) -> List[QPushButton]:
    """
    Create a set of dialog buttons with manual theme assignment
    
    Args:
        button_configs: List of button configurations
            Each config should have: 'text', 'theme', optionally 'callback'
    
    Returns:
        List of themed buttons
        
    Usage:
        buttons = create_themed_dialog_buttons([
            {'text': 'Save', 'theme': 'primary_button', 'callback': self.save},
            {'text': 'Cancel', 'theme': 'danger_button', 'callback': self.reject},
        ])
    """
    buttons = []
    
    for config in button_configs:
        button = QPushButton(config['text'])
        
        # Apply callback if provided
        if 'callback' in config:
            button.clicked.connect(config['callback'])
        
        # Apply specified theme (manual assignment)
        theme = config.get('theme', 'secondary_button')
        apply_theme_to_widget(button, theme)
        
        buttons.append(button)
    
    return buttons


def create_manual_themed_form(form_config: Dict[str, Any]) -> QWidget:
    """
    Create a form with manual theme assignments
    
    Args:
        form_config: Form configuration with explicit theme assignments
        
    Returns:
        Themed form widget
    """
    from PyQt6.QtWidgets import QFormLayout, QWidget
    
    form_widget = QWidget()
    form_layout = QFormLayout()
    
    for field_name, field_config in form_config.items():
        field_type = field_config.get('type', 'line_edit')
        field_theme = field_config.get('theme', field_type)
        label_text = field_config.get('label', field_name.title())
        
        # Create label with manual theme
        label = QLabel(label_text)
        apply_theme_to_widget(label, 'subheader_label')
        
        # Create field with manual theme
        if field_type == 'line_edit':
            field = QLineEdit()
            if 'placeholder' in field_config:
                field.setPlaceholderText(field_config['placeholder'])
        elif field_type == 'text_edit':
            field = QTextEdit()
            if 'max_height' in field_config:
                field.setMaximumHeight(field_config['max_height'])
        elif field_type == 'combo_box':
            field = QComboBox()
            if 'items' in field_config:
                field.addItems(field_config['items'])
        else:
            field = QLineEdit()  # Default fallback
        
        apply_theme_to_widget(field, field_theme)
        form_layout.addRow(label, field)
    
    form_widget.setLayout(form_layout)
    apply_theme_to_widget(form_widget, 'group_box')
    
    return form_widget


# Legacy automatic theming functions (kept for backward compatibility but not recommended)
def auto_theme_widget(widget: QWidget, force_retheme: bool = False) -> None:
    """
    DEPRECATED: Use manual theming instead for better control
    
    Automatically apply appropriate theme to a widget based on its type and content
    """
    import warnings
    warnings.warn(
        "auto_theme_widget is deprecated. Use apply_manual_themes() or explicit theme assignment instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    # Skip if already themed (unless forcing)
    if not force_retheme and widget.styleSheet():
        return
    
    # Special handling for buttons
    if isinstance(widget, QPushButton):
        style = ManualThemeUtils.get_recommended_button_theme(widget.text())
        apply_theme_to_widget(widget, style)
        return
    
    # Use default style for widget type
    widget_type = widget.__class__.__name__
    default_styles = {
        'QLineEdit': 'line_edit',
        'QTextEdit': 'text_edit', 
        'QComboBox': 'combo_box',
        'QLabel': 'body_label',
        'QGroupBox': 'group_box',
        'QTabWidget': 'tab_widget',
    }
    
    default_style = default_styles.get(widget_type)
    if default_style:
        apply_theme_to_widget(widget, default_style)


def auto_theme_widget_children(parent: QWidget, recursive: bool = True, force_retheme: bool = False) -> None:
    """
    DEPRECATED: Use apply_manual_themes() instead for better control
    
    Automatically apply themes to all child widgets
    """
    import warnings
    warnings.warn(
        "auto_theme_widget_children is deprecated. Use apply_manual_themes() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    # For backward compatibility, delegate to manual theming
    apply_manual_themes(parent)


def themed_widget(style_name: str, **style_kwargs):
    """
    Decorator to apply manual theme to widget creation functions
    
    Args:
        style_name: Name of the style to apply
        **style_kwargs: Additional style parameters
        
    Usage:
        @themed_widget('primary_button')
        def create_save_button():
            return QPushButton("Save")
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            widget = func(*args, **kwargs)
            if isinstance(widget, QWidget):
                apply_theme_to_widget(widget, style_name, **style_kwargs)
            return widget
        return wrapper
    return decorator


@contextmanager
def manual_theme_context(parent_widget: QWidget, theme_assignments: Optional[Dict[str, str]] = None):
    """
    Context manager for manual theming operations
    
    Args:
        parent_widget: Widget that will be manually themed when context exits
        theme_assignments: Optional theme assignments for specific widgets
        
    Usage:
        with manual_theme_context(my_dialog, {'save_btn': 'primary_button'}):
            # Create widgets here
            button = QPushButton("Save")
            button.setObjectName('save_btn')
            # They will be manually themed when the context exits
    """
    try:
        yield parent_widget
    finally:
        apply_manual_themes(parent_widget, theme_assignments)


def apply_adventure_chess_theme(widget: QWidget) -> None:
    """
    Apply the Adventure Chess specific theme enhancements with manual theming
    
    This function applies manual theming with additional styling specific to the 
    Adventure Chess application.
    """
    # Apply manual theme to all children first
    apply_manual_themes(widget)
    
    # Adventure Chess specific enhancements
    colors = ThemeManager.get_colors()
    
    # Apply chess-themed colors to specific widget types
    chess_widgets = widget.findChildren(QWidget)
    for child in chess_widgets:
        obj_name = child.objectName().lower()
        
        # Enhance chess board related widgets
        if 'board' in obj_name or 'chess' in obj_name or 'range' in obj_name or 'pattern' in obj_name:
            if hasattr(child, 'setStyleSheet'):
                enhanced_style = f"""
                    {child.styleSheet()}
                    /* Adventure Chess enhancements */
                    border: 2px solid {colors['chess_border']};
                    background: {colors['chess_frame']};
                """
                child.setStyleSheet(enhanced_style)


def get_responsive_spacing(base_size: str = 'base', scale_factor: float = 1.0) -> str:
    """
    Get responsive spacing value
    
    Args:
        base_size: Base spacing size from theme
        scale_factor: Scale factor for responsive design
        
    Returns:
        CSS spacing value
    """
    base_value = ThemeManager.SPACING.get(base_size, '8px')
    # Extract numeric value and unit
    import re
    match = re.match(r'(\d+)(\w+)', base_value)
    if match:
        value, unit = match.groups()
        new_value = int(int(value) * scale_factor)
        return f"{new_value}{unit}"
    
    return base_value


def migrate_existing_styles(widget: QWidget, style_mapping: Optional[Dict[str, str]] = None) -> None:
    """
    Migrate existing inline styles to manual theme-based styles
    
    Args:
        widget: Widget to migrate
        style_mapping: Optional mapping of old style patterns to new theme names
    """
    if not style_mapping:
        style_mapping = {
            'color.*red': 'danger_button',
            'color.*green': 'success_button',
            'color.*blue': 'primary_button',
            'background.*gradient': 'secondary_button',
        }
    
    current_style = widget.styleSheet()
    if not current_style:
        return
    
    # Try to match existing style to a theme
    for pattern, theme_name in style_mapping.items():
        if re.search(pattern, current_style, re.IGNORECASE):
            apply_theme_to_widget(widget, theme_name)
            return
    
    # If no match found, apply manual theme based on widget type
    if isinstance(widget, QPushButton):
        theme = ManualThemeUtils.get_recommended_button_theme(widget.text())
        apply_theme_to_widget(widget, theme)
    elif isinstance(widget, QLineEdit):
        apply_theme_to_widget(widget, 'line_edit')
    elif isinstance(widget, QTextEdit):
        apply_theme_to_widget(widget, 'text_edit')
    elif isinstance(widget, QComboBox):
        apply_theme_to_widget(widget, 'combo_box')
    elif isinstance(widget, QGroupBox):
        apply_theme_to_widget(widget, 'group_box')


class ThemeAwareWidget:
    """
    Mixin class for widgets that want to be theme-aware with manual theming
    
    Usage:
        class MyWidget(QWidget, ThemeAwareWidget):
            def __init__(self):
                super().__init__()
                self.setup_theme()
    """
    
    def setup_theme(self, style_name: Optional[str] = None) -> None:
        """Setup manual theme for this widget"""
        if style_name:
            apply_theme_to_widget(self, style_name)
        else:
            apply_manual_themes(self)
    
    def update_theme(self) -> None:
        """Update theme with manual theming"""
        apply_manual_themes(self)


# Legacy context manager (kept for backward compatibility)
@contextmanager
def theme_context(parent_widget: QWidget):
    """
    DEPRECATED: Use manual_theme_context() instead
    
    Context manager for bulk theming operations
    """
    import warnings
    warnings.warn(
        "theme_context is deprecated. Use manual_theme_context() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    try:
        yield parent_widget
    finally:
        apply_manual_themes(parent_widget)


# Export commonly used functions for easy importing - prioritizing manual theming
__all__ = [
    # Manual theming (recommended)
    'ManualThemeUtils',
    'apply_manual_themes',
    'create_themed_dialog_buttons',
    'create_manual_themed_form',
    'manual_theme_context',
    'apply_adventure_chess_theme',
    
    # Utility functions
    'themed_widget',
    'ThemeAwareWidget',
    'get_responsive_spacing',
    'migrate_existing_styles',
    
    # Deprecated automatic functions (for backward compatibility)
    'auto_theme_widget',
    'auto_theme_widget_children', 
    'theme_context',
]
