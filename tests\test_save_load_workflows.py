#!/usr/bin/env python3
"""
Comprehensive tests for save/load workflows in Adventure Chess Creator
Tests complete data persistence and retrieval workflows
"""

import pytest
import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any
from unittest.mock import Mock, patch

# Import data managers and schemas
from utils.simple_bridge import simple_bridge
from schemas.data_manager import DirectDataManager, PydanticDataManager
from schemas.piece_schema import Piece
from schemas.ability_schema import Ability


class TestSaveLoadWorkflows:
    """Test complete save/load workflows"""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create temporary data directory for testing"""
        temp_dir = tempfile.mkdtemp()
        pieces_dir = Path(temp_dir) / "pieces"
        abilities_dir = Path(temp_dir) / "abilities"
        pieces_dir.mkdir()
        abilities_dir.mkdir()
        
        yield temp_dir
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_piece_data(self):
        """Sample piece data for testing"""
        return {
            "version": "1.0.0",
            "name": "Test Workflow Piece",
            "description": "A piece for testing save/load workflows",
            "role": "Commander",
            "movement": {
                "type": "orthogonal",
                "piece_position": [3, 3]
            },
            "can_capture": True,
            "abilities": ["test_ability"],
            "max_points": 10,
            "starting_points": 5,
            "recharge_type": "turnRecharge"
        }
    
    @pytest.fixture
    def sample_ability_data(self):
        """Sample ability data for testing"""
        return {
            "version": "1.0.0",
            "name": "Test Workflow Ability",
            "description": "An ability for testing save/load workflows",
            "cost": 3,
            "activation_mode": "click",  # Use valid enum value
            "tags": ["movement", "attack"],
            "range_friendly_only": False,
            "area_size": 1,
            "move_distance": 2
        }


class TestDirectDataManagerWorkflow(TestSaveLoadWorkflows):
    """Test DirectDataManager save/load workflow"""
    
    def test_piece_save_load_cycle(self, temp_data_dir, sample_piece_data):
        """Test complete piece save/load cycle with DirectDataManager"""
        pieces_dir = str(Path(temp_data_dir) / "pieces")
        Path(pieces_dir).mkdir(parents=True, exist_ok=True)

        with patch('config.PIECES_DIR', pieces_dir):
            # Save piece
            success, error = DirectDataManager.save_piece(sample_piece_data, "test_piece")
            if not success:
                print(f"Save failed with error: {error}")
            assert success is True, f"Save failed with error: {error}"
            assert error is None

            # Verify file exists
            piece_file = Path(pieces_dir) / "test_piece.json"
            assert piece_file.exists()

            # Load piece
            loaded_data, error = DirectDataManager.load_piece("test_piece")
            assert loaded_data is not None
            assert error is None

            # Verify data integrity
            assert loaded_data["name"] == sample_piece_data["name"]
            assert loaded_data["role"] == sample_piece_data["role"]
            assert loaded_data["max_points"] == sample_piece_data["max_points"]
    
    def test_ability_save_load_cycle(self, temp_data_dir, sample_ability_data):
        """Test complete ability save/load cycle with DirectDataManager"""
        abilities_dir = str(Path(temp_data_dir) / "abilities")
        Path(abilities_dir).mkdir(parents=True, exist_ok=True)

        with patch('config.ABILITIES_DIR', abilities_dir):
            # Save ability
            success, error = DirectDataManager.save_ability(sample_ability_data, "test_ability")
            assert success is True
            assert error is None

            # Verify file exists
            ability_file = Path(abilities_dir) / "test_ability.json"
            assert ability_file.exists()

            # Load ability
            loaded_data, error = DirectDataManager.load_ability("test_ability")
            assert loaded_data is not None
            assert error is None

            # Verify data integrity
            assert loaded_data["name"] == sample_ability_data["name"]
            assert loaded_data["cost"] == sample_ability_data["cost"]
            assert loaded_data["activation_mode"] == sample_ability_data["activation_mode"]
    
    def test_file_corruption_handling(self, temp_data_dir):
        """Test handling of corrupted files"""
        # Setup paths
        original_pieces_dir = os.environ.get('PIECES_DIR', 'data/pieces')
        os.environ['PIECES_DIR'] = str(Path(temp_data_dir) / "pieces")
        
        try:
            # Create corrupted file
            corrupted_file = Path(temp_data_dir) / "pieces" / "corrupted.json"
            with open(corrupted_file, 'w') as f:
                f.write("{ invalid json content")
            
            # Try to load corrupted file
            loaded_data, error = DirectDataManager.load_piece("corrupted")
            assert loaded_data is None
            assert error is not None
            assert "json" in error.lower() or "parse" in error.lower()
            
        finally:
            os.environ['PIECES_DIR'] = original_pieces_dir


class TestPydanticDataManagerWorkflow(TestSaveLoadWorkflows):
    """Test PydanticDataManager save/load workflow"""
    
    def test_piece_validation_workflow(self, temp_data_dir, sample_piece_data):
        """Test piece validation during save/load with PydanticDataManager"""
        # Setup paths
        original_pieces_dir = os.environ.get('PIECES_DIR', 'data/pieces')
        os.environ['PIECES_DIR'] = str(Path(temp_data_dir) / "pieces")
        
        try:
            manager = PydanticDataManager()
            
            # Create and validate piece
            piece = Piece(**sample_piece_data)
            
            # Save piece
            success, error = manager.save_piece(piece, "validated_piece")
            assert success is True
            assert error is None
            
            # Load piece
            loaded_piece, error = manager.load_piece("validated_piece.json")
            assert loaded_piece is not None
            assert error is None
            assert isinstance(loaded_piece, Piece)
            
            # Verify data integrity
            assert loaded_piece.name == piece.name
            assert loaded_piece.role == piece.role
            assert loaded_piece.max_points == piece.max_points
            
        finally:
            os.environ['PIECES_DIR'] = original_pieces_dir
    
    def test_ability_validation_workflow(self, temp_data_dir, sample_ability_data):
        """Test ability validation during save/load with PydanticDataManager"""
        # Setup paths
        original_abilities_dir = os.environ.get('ABILITIES_DIR', 'data/abilities')
        os.environ['ABILITIES_DIR'] = str(Path(temp_data_dir) / "abilities")
        
        try:
            manager = PydanticDataManager()
            
            # Create and validate ability
            ability = Ability(**sample_ability_data)
            
            # Save ability
            success, error = manager.save_ability(ability, "validated_ability")
            assert success is True
            assert error is None
            
            # Load ability
            loaded_ability, error = manager.load_ability("validated_ability.json")
            assert loaded_ability is not None
            assert error is None
            assert isinstance(loaded_ability, Ability)
            
            # Verify data integrity
            assert loaded_ability.name == ability.name
            assert loaded_ability.cost == ability.cost
            assert loaded_ability.activation_mode == ability.activation_mode
            
        finally:
            os.environ['ABILITIES_DIR'] = original_abilities_dir
    
    def test_validation_error_handling(self, temp_data_dir):
        """Test handling of validation errors"""
        # Setup paths
        original_pieces_dir = os.environ.get('PIECES_DIR', 'data/pieces')
        os.environ['PIECES_DIR'] = str(Path(temp_data_dir) / "pieces")
        
        try:
            manager = PydanticDataManager()
            
            # Create invalid piece data
            invalid_data = {
                "name": "",  # Invalid: empty name
                "role": "InvalidRole",  # Invalid: not a valid role
                "max_points": -1  # Invalid: negative points
            }
            
            # Try to create piece (should fail validation)
            with pytest.raises(Exception):  # Pydantic ValidationError
                Piece(**invalid_data)
            
        finally:
            os.environ['PIECES_DIR'] = original_pieces_dir


class TestSimpleBridgeWorkflow(TestSaveLoadWorkflows):
    """Test SimpleBridge save/load workflow (UI integration)"""
    
    def test_piece_ui_workflow_simulation(self, temp_data_dir, sample_piece_data):
        """Test piece workflow as if coming from UI"""
        pieces_dir = str(Path(temp_data_dir) / "pieces")
        Path(pieces_dir).mkdir(parents=True, exist_ok=True)

        with patch('config.PIECES_DIR', pieces_dir):
            # Create mock editor that returns our sample data
            mock_editor = Mock()

            # Mock the EditorDataInterface.collect_data_from_ui to return our sample data
            with patch('utils.simple_bridge.EditorDataInterface.collect_data_from_ui', return_value=sample_piece_data):
                # Save using SimpleBridge
                success, error = simple_bridge.save_piece_from_ui(mock_editor, "ui_test_piece")
                assert success is True
                assert error is None

                # Load using SimpleBridge
                loaded_data, error = simple_bridge.load_piece_for_ui("ui_test_piece")
                assert loaded_data is not None
                assert error is None

                # Verify data integrity
                assert loaded_data["name"] == sample_piece_data["name"]
                assert loaded_data["role"] == sample_piece_data["role"]
    
    def test_ability_ui_workflow_simulation(self, temp_data_dir, sample_ability_data):
        """Test ability UI workflow simulation using SimpleBridge"""
        abilities_dir = str(Path(temp_data_dir) / "abilities")

        with patch('config.ABILITIES_DIR', abilities_dir):
            # Create mock editor that returns our sample data
            mock_editor = Mock()

            # Mock the EditorDataInterface.collect_data_from_ui to return our sample data
            with patch('utils.simple_bridge.EditorDataInterface.collect_data_from_ui', return_value=sample_ability_data):
                # Save using SimpleBridge
                success, error = simple_bridge.save_ability_from_ui(mock_editor, "ui_test_ability")
                assert success is True
                assert error is None

                # Load using SimpleBridge
                loaded_data, error = simple_bridge.load_ability_for_ui("ui_test_ability")
                assert loaded_data is not None
                assert error is None

                # Verify data integrity
                assert loaded_data["name"] == sample_ability_data["name"]
                assert loaded_data["cost"] == sample_ability_data["cost"]


class TestCrossManagerCompatibility(TestSaveLoadWorkflows):
    """Test compatibility between different data managers"""

    def test_direct_to_pydantic_compatibility(self, temp_data_dir, sample_piece_data):
        """Test that files saved with DirectDataManager can be loaded with PydanticDataManager"""
        pieces_dir = str(Path(temp_data_dir) / "pieces")

        with patch('config.PIECES_DIR', pieces_dir):
            # Save with DirectDataManager
            success, error = DirectDataManager.save_piece(sample_piece_data, "compatibility_test")
            assert success is True

            # Load with PydanticDataManager
            manager = PydanticDataManager()
            loaded_piece, error = manager.load_piece("compatibility_test.json")
            assert loaded_piece is not None
            assert error is None
            assert isinstance(loaded_piece, Piece)

            # Verify data integrity
            assert loaded_piece.name == sample_piece_data["name"]
    
    def test_pydantic_to_direct_compatibility(self, temp_data_dir, sample_ability_data):
        """Test that files saved with PydanticDataManager can be loaded with DirectDataManager"""
        # Setup paths
        original_abilities_dir = os.environ.get('ABILITIES_DIR', 'data/abilities')
        os.environ['ABILITIES_DIR'] = str(Path(temp_data_dir) / "abilities")
        
        try:
            # Save with PydanticDataManager
            manager = PydanticDataManager()
            ability = Ability(**sample_ability_data)
            success, error = manager.save_ability(ability, "compatibility_test")
            assert success is True
            
            # Load with DirectDataManager
            loaded_data, error = DirectDataManager.load_ability("compatibility_test")
            assert loaded_data is not None
            assert error is None
            
            # Verify data integrity
            assert loaded_data["name"] == sample_ability_data["name"]
            
        finally:
            os.environ['ABILITIES_DIR'] = original_abilities_dir


class TestPerformanceWorkflows(TestSaveLoadWorkflows):
    """Test performance aspects of save/load workflows"""

    def test_large_data_save_load(self, temp_data_dir):
        """Test save/load performance with large datasets"""
        pieces_dir = str(Path(temp_data_dir) / "pieces")
        Path(pieces_dir).mkdir(parents=True, exist_ok=True)

        with patch('config.PIECES_DIR', pieces_dir):
            # Create large piece data
            large_piece_data = {
                "version": "1.0.0",
                "name": "Large Test Piece",
                "description": "x" * 1000,  # Large description
                "role": "Commander",
                "abilities": [f"ability_{i}" for i in range(100)],  # Many abilities
                "max_points": 99  # Use valid range
            }

            # Test save performance
            import time
            start_time = time.time()
            success, error = DirectDataManager.save_piece(large_piece_data, "large_piece")
            save_time = time.time() - start_time

            assert success is True
            assert save_time < 1.0  # Should save within 1 second

            # Test load performance
            start_time = time.time()
            loaded_data, error = DirectDataManager.load_piece("large_piece")
            load_time = time.time() - start_time

            assert loaded_data is not None
            assert load_time < 1.0  # Should load within 1 second

            # Verify data integrity
            assert loaded_data["name"] == large_piece_data["name"]
            assert len(loaded_data["abilities"]) == 100

    def test_concurrent_save_load(self, temp_data_dir, sample_piece_data):
        """Test concurrent save/load operations"""
        import threading
        import time

        # Setup paths
        original_pieces_dir = os.environ.get('PIECES_DIR', 'data/pieces')
        os.environ['PIECES_DIR'] = str(Path(temp_data_dir) / "pieces")

        results = []

        def save_piece(piece_id):
            data = sample_piece_data.copy()
            data["name"] = f"Concurrent Piece {piece_id}"
            success, error = DirectDataManager.save_piece(data, f"concurrent_{piece_id}")
            results.append((piece_id, success, error))

        try:
            # Create multiple threads for concurrent saves
            threads = []
            for i in range(5):
                thread = threading.Thread(target=save_piece, args=(i,))
                threads.append(thread)
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Verify all saves succeeded
            assert len(results) == 5
            for piece_id, success, error in results:
                assert success is True
                assert error is None

        finally:
            os.environ['PIECES_DIR'] = original_pieces_dir


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
