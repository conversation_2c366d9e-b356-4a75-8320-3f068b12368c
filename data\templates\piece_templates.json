{"Basic Pawn": {"version": "1.0.0", "name": "", "description": "A basic pawn piece", "role": "Soldier", "can_castle": false, "track_starting_position": true, "color_directional": true, "can_capture": true, "movement": {"type": "Custom", "pattern": [[false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false]], "piece_position": [4, 4]}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Basic Rook": {"version": "1.0.0", "name": "", "description": "A basic rook piece", "role": "Commander", "can_castle": true, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "orthogonal", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Basic Knight": {"version": "1.0.0", "name": "", "description": "A basic knight piece", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "L-shape", "distance": 1}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Basic Bishop": {"version": "1.0.0", "name": "", "description": "A basic bishop piece", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "diagonal", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Basic Queen": {"version": "1.0.0", "name": "", "description": "A basic queen piece", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "orthogonal_diagonal", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Basic King": {"version": "1.0.0", "name": "", "description": "A basic king piece", "role": "King", "can_castle": true, "track_starting_position": true, "color_directional": false, "can_capture": true, "movement": {"type": "orthogonal_diagonal", "distance": 1}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}, "Magical Archer": {"version": "1.0.0", "name": "", "description": "An archer with ranged magical abilities", "role": "Soldier", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "orthogonal_diagonal", "distance": 2}, "recharge": {"type": "turn<PERSON><PERSON><PERSON><PERSON>", "turns": 2}, "abilities": ["Magic Bolt", "Long Range Shot"], "promotions": []}, "Teleporting Mage": {"version": "1.0.0", "name": "", "description": "A mage with teleportation abilities", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "orthogonal_diagonal", "distance": 1}, "recharge": {"type": "turn<PERSON><PERSON><PERSON><PERSON>", "turns": 3}, "abilities": ["Teleport", "Magic Shield"], "promotions": []}}