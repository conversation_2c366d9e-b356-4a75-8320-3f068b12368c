"""
FogOfWar tag configuration for ability editor.
Handles fog of war ability configurations with vision types and range settings.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QSpinBox, QHBoxLayout,
                            QPushButton, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from config import VISION_TYPES


class FogOfWarConfig(BaseTagConfig):
    """Configuration for fogOfWar tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "fogOfWar")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for fog of war configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting fog of war UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Reveals tiles on the board around the source piece.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Vision type
            fog_vision_combo = QComboBox()
            fog_vision_combo.addItems([f"{code} - {desc}" for code, desc in VISION_TYPES])
            fog_vision_combo.setToolTip("Type of vision system")
            self.store_widget("fog_vision_combo", fog_vision_combo)
            form_layout.addRow("Vision Type:", fog_vision_combo)

            # Lantern Duration (only for lantern type)
            fog_duration_spin = QSpinBox()
            fog_duration_spin.setRange(0, 20)
            fog_duration_spin.setValue(0)
            fog_duration_spin.setSuffix(" turns")
            fog_duration_spin.setSpecialValueText("Permanent")
            fog_duration_spin.setToolTip("Duration of lantern effect (0 for permanent)")
            fog_duration_spin.setVisible(False)
            self.store_widget("fog_duration_spin", fog_duration_spin)
            form_layout.addRow("Lantern Duration:", fog_duration_spin)

            # Connect vision type change
            fog_vision_combo.currentTextChanged.connect(self._on_fog_vision_changed)

            layout.addLayout(form_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Fog of war UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def _on_fog_vision_changed(self):
        """Handle vision type change to show/hide relevant options."""
        try:
            fog_vision_combo = self.get_widget_by_name("fog_vision_combo")
            fog_duration_spin = self.get_widget_by_name("fog_duration_spin")

            if fog_vision_combo and fog_duration_spin:
                current_text = fog_vision_combo.currentText()
                is_lantern = "lantern" in current_text.lower()

                # Only show duration for lantern type
                fog_duration_spin.setVisible(is_lantern)

        except Exception as e:
            self.log_error(f"Error handling fog vision change: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating fog of war data")

            # Populate vision type
            fog_vision_combo = self.get_widget_by_name("fog_vision_combo")
            if fog_vision_combo and "fogVisionType" in data:
                vision_type = data["fogVisionType"]
                for i in range(fog_vision_combo.count()):
                    if vision_type in fog_vision_combo.itemText(i):
                        fog_vision_combo.setCurrentIndex(i)
                        break

            # Populate lantern duration (only for lantern type)
            fog_duration_spin = self.get_widget_by_name("fog_duration_spin")
            if fog_duration_spin and "fogLanternDuration" in data:
                fog_duration_spin.setValue(data["fogLanternDuration"])

            # Update visibility based on vision type
            self._on_fog_vision_changed()

            self.log_debug("Fog of war data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting fog of war data")
            data = {}

            # Collect vision type
            fog_vision_combo = self.get_widget_by_name("fog_vision_combo")
            if fog_vision_combo:
                vision_text = fog_vision_combo.currentText()
                if vision_text:
                    vision_type = vision_text.split(" - ")[0]
                    data["fogVisionType"] = vision_type

            # Collect lantern duration (only if visible for lantern type)
            fog_duration_spin = self.get_widget_by_name("fog_duration_spin")
            if fog_duration_spin and fog_duration_spin.isVisible():
                data["fogLanternDuration"] = fog_duration_spin.value()

            self.log_debug("Fog of war data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
