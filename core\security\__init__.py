"""
Core Security Module for Adventure Chess Creator

This module contains security enhancements and validation systems.
Consolidated from the enhancements/security/ folder for better organization.
"""

from .security_system import (
    CrashRecoveryManager,
    SecureDataManager,
    SecureDirectDataManager,
    SecurePydanticDataManager,
    SecurityValidator,
    crash_recovery_manager,
    get_recovery_options,
    secure_data_manager,
    secure_direct_data_manager,
    secure_pydantic_data_manager,
    security_validator,
)

# Alias for backward compatibility
SecurityEnhancementManager = SecureDataManager

__all__ = [
    "SecurityValidator",
    "CrashRecoveryManager",
    "SecureDataManager",
    "SecureDirectDataManager",
    "SecurePydanticDataManager",
    "SecurityEnhancementManager",  # Alias for SecureDataManager
    "security_validator",
    "crash_recovery_manager",
    "secure_data_manager",
    "secure_direct_data_manager",
    "secure_pydantic_data_manager",
    "get_recovery_options",
]
