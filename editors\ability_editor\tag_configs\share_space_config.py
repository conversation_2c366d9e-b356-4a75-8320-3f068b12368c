"""
Share Space tag configuration for the Adventure Chess Creator ability editor.
Allows multiple pieces to occupy the same square with various restrictions.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import Q<PERSON>pinBox, QComboBox

from .base_tag_config import BaseTagConfig


class ShareSpaceConfig(BaseTagConfig):
    """Configuration for shareSpace tag - multiple pieces per square."""
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "shareSpace")
    
    def get_title(self) -> str:
        return "📦 Share Space Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the share space configuration UI."""
        try:
            self.log_debug("Starting share space UI creation")

            # Create form layout
            form_layout = self.create_form_layout()

            # Target type dropdown
            target_type_combo = QComboBox()
            target_type_combo.addItems(["friendly", "enemy", "any"])
            target_type_combo.setCurrentText("friendly")
            target_type_combo.setToolTip("Which pieces can share space")
            self.store_widget("share_space_target_combo", target_type_combo)
            self.connect_change_signals(target_type_combo)
            form_layout.addRow("Target Type:", target_type_combo)
            self.log_debug("Added target type dropdown")

            # Maximum pieces per square
            max_pieces_spin = QSpinBox()
            max_pieces_spin.setRange(2, 8)
            max_pieces_spin.setValue(2)
            max_pieces_spin.setToolTip("Maximum pieces per square")
            self.store_widget("share_space_max_spin", max_pieces_spin)
            self.connect_change_signals(max_pieces_spin)
            form_layout.addRow("Max Pieces:", max_pieces_spin)
            self.log_debug("Added max pieces spinner")

            # Add to parent layout
            parent_layout.addLayout(form_layout)

            self.log_debug("Share space configuration UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating share space UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating share space data: {data}")

            # Populate target type
            target_type_combo = self.get_widget_by_name("share_space_target_combo")
            if target_type_combo:
                target_type = data.get("shareSpaceTargetType", "friendly")
                self.log_debug(f"Setting target type to: {target_type}")
                target_type_combo.setCurrentText(target_type)

            # Populate max pieces
            max_pieces_spin = self.get_widget_by_name("share_space_max_spin")
            if max_pieces_spin:
                max_pieces = data.get("shareSpaceMax", 2)
                self.log_debug(f"Setting max pieces to: {max_pieces}")
                max_pieces_spin.setValue(max_pieces)

            self.log_debug("Share space data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating share space data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            self.log_debug("Collecting share space data")
            data = {}

            # Collect target type
            target_type_combo = self.get_widget_by_name("share_space_target_combo")
            if target_type_combo:
                data["shareSpaceTargetType"] = target_type_combo.currentText()

            # Collect max pieces
            max_pieces_spin = self.get_widget_by_name("share_space_max_spin")
            if max_pieces_spin:
                data["shareSpaceMax"] = max_pieces_spin.value()

            self.log_debug(f"Collected share space data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting share space data: {e}")
            return {}
