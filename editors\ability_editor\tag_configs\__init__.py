"""
Tag Configuration Modules for Adventure Chess Creator

This package contains individual configuration modules for each ability tag.
Each tag has its own file with a standardized interface for creating UI widgets
and handling data population.

Structure:
- Each tag config file exports a TagConfig class
- TagConfig classes implement create_ui() and populate_data() methods
- The tag manager dynamically imports and uses these configurations

This modular approach makes it easy to:
- Add new tags without modifying existing code
- Maintain and debug individual tag configurations
- Share tag configurations between different editors
- Test tag configurations in isolation
"""

from typing import Dict, Type, TYPE_CHECKING

if TYPE_CHECKING:
    from .base_tag_config import BaseTagConfig

# Registry of available tag configurations
TAG_CONFIG_REGISTRY: Dict[str, Type["BaseTagConfig"]] = {}

def register_tag_config(tag_name: str, config_class: Type["BaseTagConfig"]) -> None:
    """Register a tag configuration class."""
    TAG_CONFIG_REGISTRY[tag_name] = config_class

def get_tag_config(tag_name: str) -> Type["BaseTagConfig"]:
    """Get a tag configuration class by name."""
    return TAG_CONFIG_REGISTRY.get(tag_name)

def get_available_tags() -> list[str]:
    """Get list of available tag configurations."""
    return list(TAG_CONFIG_REGISTRY.keys())

# Import all tag configurations to register them
try:
    from .carry_piece_config import CarryPieceConfig
    register_tag_config("carryPiece", CarryPieceConfig)
except ImportError:
    pass

# Import other tag configurations
try:
    from .range_config import RangeConfig
    register_tag_config("range", RangeConfig)
except ImportError:
    pass

try:
    from .area_effect_config import AreaEffectConfig
    register_tag_config("areaEffect", AreaEffectConfig)
except ImportError:
    pass

try:
    from .move_config import MoveConfig
    register_tag_config("move", MoveConfig)
except ImportError:
    pass

try:
    from .summon_config import SummonConfig
    register_tag_config("summon", SummonConfig)
except ImportError:
    pass

try:
    from .revival_config import RevivalConfig
    register_tag_config("revival", RevivalConfig)
except ImportError:
    pass

try:
    from .capture_config import CaptureConfig
    register_tag_config("capture", CaptureConfig)
except ImportError:
    pass

# Advanced configurations with custom dialogs
try:
    from .adjacency_config import AdjacencyConfig
    register_tag_config("adjacencyRequired", AdjacencyConfig)
except ImportError:
    pass

try:
    from .displacement_config import DisplacementConfig
    register_tag_config("displacePiece", DisplacementConfig)
except ImportError:
    pass

try:
    from .swap_places_config import SwapPlacesConfig
    register_tag_config("swapPlaces", SwapPlacesConfig)
except ImportError:
    pass

try:
    from .no_turn_cost_config import NoTurnCostConfig
    register_tag_config("noTurnCost", NoTurnCostConfig)
except ImportError:
    pass

# New advanced tag configurations
try:
    from .pass_through_config import PassThroughConfig
    register_tag_config("passThrough", PassThroughConfig)
except ImportError:
    pass

try:
    from .los_required_config import LosRequiredConfig
    register_tag_config("losRequired", LosRequiredConfig)
except ImportError:
    pass

try:
    from .delay_config import DelayConfig
    register_tag_config("delay", DelayConfig)
except ImportError:
    pass

try:
    from .immobilize_config import ImmobilizeConfig
    register_tag_config("immobilize", ImmobilizeConfig)
except ImportError:
    pass

try:
    from .pulse_effect_config import PulseEffectConfig
    register_tag_config("pulseEffect", PulseEffectConfig)
except ImportError:
    pass

try:
    from .fog_of_war_config import FogOfWarConfig
    register_tag_config("fogOfWar", FogOfWarConfig)
except ImportError:
    pass

try:
    from .add_obstacle_config import AddObstacleConfig
    register_tag_config("addObstacle", AddObstacleConfig)
except ImportError:
    pass

try:
    from .remove_obstacle_config import RemoveObstacleConfig
    register_tag_config("removeObstacle", RemoveObstacleConfig)
except ImportError:
    pass

try:
    from .duplicate_config import DuplicateConfig
    register_tag_config("duplicate", DuplicateConfig)
except ImportError:
    pass

try:
    from .convert_piece_config import ConvertPieceConfig
    register_tag_config("convertPiece", ConvertPieceConfig)
except ImportError:
    pass

try:
    from .reaction_config import ReactionConfig
    register_tag_config("reaction", ReactionConfig)
except ImportError:
    pass

# Missing tag configurations - newly implemented
try:
    from .buff_piece_config import BuffPieceConfig
    register_tag_config("buffPiece", BuffPieceConfig)
except ImportError:
    pass

try:
    from .debuff_piece_config import DebuffPieceConfig
    register_tag_config("debuffPiece", DebuffPieceConfig)
except ImportError:
    pass

try:
    from .invisible_config import InvisibleConfig
    register_tag_config("invisible", InvisibleConfig)
except ImportError:
    pass

try:
    from .share_space_config import ShareSpaceConfig
    register_tag_config("shareSpace", ShareSpaceConfig)
except ImportError:
    pass

try:
    from .trap_tile_config import TrapTileConfig
    register_tag_config("trapTile", TrapTileConfig)
except ImportError:
    pass

try:
    from .requires_starting_position_config import RequiresStartingPositionConfig
    register_tag_config("requiresStartingPosition", RequiresStartingPositionConfig)
except ImportError:
    pass
