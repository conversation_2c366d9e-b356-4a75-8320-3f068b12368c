"""
Keyboard Shortcuts System for Adventure Chess Creator

This module provides enhanced keyboard shortcuts for editors with
comprehensive shortcut management and customizable key bindings.
"""

import logging

from PyQt6.QtGui import QKeySequence, QShortcut

logger = logging.getLogger(__name__)


class KeyboardShortcutManager:
    """Manages enhanced keyboard shortcuts for editors"""

    def __init__(self, editor):
        self.editor = editor
        self.shortcuts = []
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """Setup enhanced keyboard shortcuts"""
        shortcuts_config = [
            # File operations
            ("Ctrl+N", self.new_item, "New"),
            ("Ctrl+O", self.open_item, "Open"),
            ("Ctrl+S", self.save_item, "Save"),
            ("Ctrl+Shift+S", self.save_as_item, "Save As"),
            ("Ctrl+D", self.duplicate_item, "Duplicate"),
            ("Delete", self.delete_item, "Delete"),
            # Edit operations
            ("Ctrl+Z", self.undo, "Undo"),
            ("Ctrl+Y", self.redo, "Redo"),
            ("Ctrl+Shift+Z", self.redo, "Redo (Alt)"),
            # Navigation
            ("Ctrl+Tab", self.next_tab, "Next Tab"),
            ("Ctrl+Shift+Tab", self.prev_tab, "Previous Tab"),
            ("F5", self.refresh, "Refresh"),
            # Quick actions
            ("Ctrl+R", self.reset_form, "Reset Form"),
            ("Ctrl+T", self.show_templates, "Show Templates"),
            ("Ctrl+P", self.preview, "Preview"),
            ("Escape", self.cancel_operation, "Cancel"),
            # Field navigation
            ("Tab", self.next_field, "Next Field"),
            ("Shift+Tab", self.prev_field, "Previous Field"),
        ]

        for key_sequence, handler, description in shortcuts_config:
            try:
                shortcut = QShortcut(QKeySequence(key_sequence), self.editor)
                shortcut.activated.connect(handler)
                self.shortcuts.append((shortcut, description))
                logger.debug(f"Registered shortcut: {key_sequence} - {description}")

            except Exception as e:
                logger.error(f"Error registering shortcut {key_sequence}: {e}")

    # Shortcut handlers
    def new_item(self):
        """Create new item"""
        if hasattr(self.editor, "new_data"):
            self.editor.new_data()
        elif hasattr(self.editor, "reset_form"):
            self.editor.reset_form()

    def open_item(self):
        """Open item"""
        if hasattr(self.editor, "open_data"):
            self.editor.open_data()
        elif hasattr(self.editor, "load_data"):
            self.editor.load_data()

    def save_item(self):
        """Save item"""
        if hasattr(self.editor, "save_data"):
            self.editor.save_data()

    def save_as_item(self):
        """Save as item"""
        if hasattr(self.editor, "save_as_data"):
            self.editor.save_as_data()

    def duplicate_item(self):
        """Duplicate current item"""
        if hasattr(self.editor, "duplicate_current"):
            self.editor.duplicate_current()

    def delete_item(self):
        """Delete current item"""
        if hasattr(self.editor, "delete_current"):
            self.editor.delete_current()

    def undo(self):
        """Undo last action"""
        if hasattr(self.editor, "undo_manager"):
            self.editor.undo_manager.undo_stack.undo()

    def redo(self):
        """Redo last action"""
        if hasattr(self.editor, "undo_manager"):
            self.editor.undo_manager.undo_stack.redo()

    def next_tab(self):
        """Switch to next tab"""
        if hasattr(self.editor, "tab_widget"):
            current = self.editor.tab_widget.currentIndex()
            count = self.editor.tab_widget.count()
            self.editor.tab_widget.setCurrentIndex((current + 1) % count)

    def prev_tab(self):
        """Switch to previous tab"""
        if hasattr(self.editor, "tab_widget"):
            current = self.editor.tab_widget.currentIndex()
            count = self.editor.tab_widget.count()
            self.editor.tab_widget.setCurrentIndex((current - 1) % count)

    def refresh(self):
        """Refresh editor"""
        if hasattr(self.editor, "refresh_data"):
            self.editor.refresh_data()

    def reset_form(self):
        """Reset form"""
        if hasattr(self.editor, "reset_form"):
            self.editor.reset_form()

    def show_templates(self):
        """Show template dialog"""
        if hasattr(self.editor, "template_manager"):
            self.editor.template_manager.show_template_dialog()

    def preview(self):
        """Show preview"""
        if hasattr(self.editor, "show_preview"):
            self.editor.show_preview()

    def cancel_operation(self):
        """Cancel current operation"""
        # Close any open dialogs or reset current operation

    def next_field(self):
        """Navigate to next field"""
        # Let Qt handle default tab behavior

    def prev_field(self):
        """Navigate to previous field"""
        # Let Qt handle default shift+tab behavior

    def add_custom_shortcut(self, key_sequence: str, handler, description: str):
        """Add a custom shortcut"""
        try:
            shortcut = QShortcut(QKeySequence(key_sequence), self.editor)
            shortcut.activated.connect(handler)
            self.shortcuts.append((shortcut, description))
            logger.info(f"Added custom shortcut: {key_sequence} - {description}")

        except Exception as e:
            logger.error(f"Error adding custom shortcut {key_sequence}: {e}")

    def remove_shortcut(self, key_sequence: str):
        """Remove a shortcut by key sequence"""
        for i, (shortcut, description) in enumerate(self.shortcuts):
            if shortcut.key().toString() == key_sequence:
                shortcut.setParent(None)
                del self.shortcuts[i]
                logger.info(f"Removed shortcut: {key_sequence}")
                break

    def get_shortcuts_list(self):
        """Get list of all registered shortcuts"""
        return [
            (shortcut.key().toString(), description)
            for shortcut, description in self.shortcuts
        ]

    def cleanup(self):
        """Cleanup all shortcuts"""
        for shortcut, _ in self.shortcuts:
            shortcut.setParent(None)
        self.shortcuts.clear()
        logger.info("Keyboard shortcuts cleaned up")
