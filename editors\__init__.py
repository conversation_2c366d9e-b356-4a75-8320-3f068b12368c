"""
Editor modules for Adventure Chess

Location: editors/

Contains main editor windows and interfaces:

/piece_editor/     - Complete piece creation and editing system
  - piece_editor_main.py: Main coordinator window
  - piece_data_handlers.py: Data management and validation
  - piece_ui_components.py: UI layout and widget creation
  - piece_movement_manager.py: Movement pattern handling
  - piece_promotion_manager.py: Promotion configuration
  - piece_icon_manager.py: Icon selection and management

/ability_editor/   - Complete ability creation and editing system
  - ability_editor_main.py: Main coordinator window
  - ability_data_handlers.py: Data management and validation
  - ability_ui_components.py: UI layout and widget creation
  - ability_tag_managers.py: Tag selection and configuration
  - tag_configs/: Individual tag configuration modules

Entry points: piece_editor.py and ability_editor.py provide window factories
"""
