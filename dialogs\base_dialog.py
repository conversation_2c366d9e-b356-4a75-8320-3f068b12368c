"""
Base Dialog Class for Adventure Chess Creator

This module provides the base class that all dialogs should inherit from.
It defines standard functionality, styling, and layout patterns.
"""

import logging
from abc import ABCMeta, abstractmethod
from typing import Dict, Any, Optional, Tuple
from PyQt6.QtWidgets import (
    QDialog, Q<PERSON>oxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QPushButton, QFrame, QWidget, QDialogButtonBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

# Import theme management for consistent styling
from core.ui import (
    ThemeManager,
    apply_theme_to_widget,
    apply_manual_themes,
    create_themed_label,
    create_themed_group_box,
    create_themed_button
)

logger = logging.getLogger(__name__)


class DialogMeta(type(QDialog), ABCMeta):
    """Metaclass to resolve conflict between QDialog and ABC metaclasses."""
    pass


class BaseDialog(QDialog, metaclass=DialogMeta):
    """
    Base class for all Adventure Chess Creator dialogs.
    
    Provides standardized functionality including:
    - Consistent styling and layout patterns
    - Standard button handling
    - Error handling and logging
    - Window management
    - Data validation patterns
    """
    
    # Signals for dialog events
    data_changed = pyqtSignal()
    validation_failed = pyqtSignal(str)  # Error message
    
    def __init__(self, parent: Optional[QWidget] = None, title: str = "Dialog", 
                 size: Tuple[int, int] = (600, 400)):
        """
        Initialize the base dialog.
        
        Args:
            parent: Parent widget
            title: Dialog window title
            size: Dialog size as (width, height)
        """
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setMinimumSize(*size)
        self.setModal(True)
        
        # Apply standard dialog styling
        # Apply theme styling instead of hardcoded stylesheet
        self._apply_theme()
        
        # Initialize data tracking
        self._data_changed = False
        self._validation_errors = []
        
        # Setup UI
        self._setup_base_ui()
        self.setup_ui()
        
        # Connect signals
        self.data_changed.connect(self._on_data_changed)
        self.validation_failed.connect(self._on_validation_failed)
        
        logger.debug(f"Initialized {self.__class__.__name__} dialog")
    
    def _setup_base_ui(self):
        """Setup the base UI structure."""
        # Main layout
        self.main_layout = QVBoxLayout()
        self.setLayout(self.main_layout)
        
        # Content area (to be populated by subclasses)
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout()
        self.content_widget.setLayout(self.content_layout)
        self.main_layout.addWidget(self.content_widget)
        
        # Button box
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self._on_accept)
        self.button_box.rejected.connect(self.reject)
        self.main_layout.addWidget(self.button_box)
        
        # Get button references
        self.ok_button = self.button_box.button(QDialogButtonBox.StandardButton.Ok)
        self.cancel_button = self.button_box.button(QDialogButtonBox.StandardButton.Cancel)
        
        # Apply button styling
        self._style_buttons()
    
    @abstractmethod
    def setup_ui(self):
        """
        Setup the dialog-specific UI.
        
        Subclasses should implement this method to create their specific UI.
        Use self.content_layout to add widgets.
        """
        pass
    
    @abstractmethod
    def validate_data(self) -> bool:
        """
        Validate the current dialog data.
        
        Returns:
            True if data is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the dialog.
        
        Returns:
            Dictionary containing the dialog's data
        """
        pass
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the dialog with data.
        
        Args:
            data: Dictionary containing data to populate
        """
        # Default implementation - subclasses can override
        pass
    
    # ========== STANDARDIZED UI CREATION METHODS ==========
    
    def create_title_label(self, text: str, size: int = 14) -> QLabel:
        """
        Create a standardized title label.
        
        Args:
            text: Title text
            size: Font size (ignored, uses theme-defined size)
            
        Returns:
            Configured QLabel
        """
        label = create_themed_label(text, 'header_label')
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        return label
    
    def create_description_label(self, text: str) -> QLabel:
        """
        Create a standardized description label.
        
        Args:
            text: Description text
            
        Returns:
            Configured QLabel
        """
        label = create_themed_label(text, 'muted_label')
        label.setWordWrap(True)
        return label
    
    def create_section_group(self, title: str) -> QGroupBox:
        """
        Create a standardized section group box.
        
        Args:
            title: Section title
            
        Returns:
            Configured QGroupBox
        """
        group = create_themed_group_box(title)
        return group
    
    def create_separator(self) -> QFrame:
        """
        Create a standardized separator line.
        
        Returns:
            Configured QFrame as separator
        """
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        # Apply theme styling instead of inline CSS
        apply_theme_to_widget(separator, 'separator')
        return separator
    
    def create_left_right_layout(self) -> Tuple[QHBoxLayout, QVBoxLayout, QVBoxLayout]:
        """
        Create a standardized left-right layout.
        
        Returns:
            Tuple of (main_layout, left_layout, right_layout)
        """
        main_layout = QHBoxLayout()
        left_layout = QVBoxLayout()
        right_layout = QVBoxLayout()
        
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)
        
        return main_layout, left_layout, right_layout
    
    # ========== BUTTON MANAGEMENT ==========
    
    def add_custom_button(self, text: str, role: QDialogButtonBox.ButtonRole) -> QPushButton:
        """
        Add a custom button to the button box.
        
        Args:
            text: Button text
            role: Button role
            
        Returns:
            The created button
        """
        button = QPushButton(text)
        self.button_box.addButton(button, role)
        self._style_button(button)
        return button
    
    def set_ok_button_text(self, text: str):
        """Set the OK button text."""
        if self.ok_button:
            self.ok_button.setText(text)
    
    def set_cancel_button_text(self, text: str):
        """Set the Cancel button text."""
        if self.cancel_button:
            self.cancel_button.setText(text)
    
    # ========== DATA MANAGEMENT ==========
    
    def mark_data_changed(self):
        """Mark that data has been changed."""
        if not self._data_changed:
            self._data_changed = True
            self.data_changed.emit()
    
    def is_data_changed(self) -> bool:
        """Check if data has been changed."""
        return self._data_changed
    
    def add_validation_error(self, error: str):
        """Add a validation error."""
        self._validation_errors.append(error)
        self.validation_failed.emit(error)
    
    def clear_validation_errors(self):
        """Clear all validation errors."""
        self._validation_errors.clear()
    
    def get_validation_errors(self) -> list:
        """Get all validation errors."""
        return self._validation_errors.copy()
    
    # ========== EVENT HANDLERS ==========
    
    def _on_accept(self):
        """Handle OK button click."""
        self.clear_validation_errors()
        
        if self.validate_data():
            self.accept()
        else:
            # Show validation errors
            if self._validation_errors:
                error_msg = "\n".join(self._validation_errors)
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Validation Error", error_msg)
    
    def _on_data_changed(self):
        """Handle data changed signal."""
        # Update window title to indicate unsaved changes
        title = self.windowTitle()
        if not title.endswith("*"):
            self.setWindowTitle(title + "*")
    
    def _on_validation_failed(self, error: str):
        """Handle validation failed signal."""
        logger.warning(f"Validation failed in {self.__class__.__name__}: {error}")
    
    # ========== STYLING ==========
    
    def _apply_theme(self):
        """Apply theme styling to the dialog."""
        # Apply the dialog theme to the entire window
        ThemeManager.apply_window_theme(self)
        
        # Apply manual theming to all child widgets
        apply_manual_themes(self)
    
    def _style_buttons(self):
        """Apply styling to dialog buttons."""
        if self.ok_button:
            apply_theme_to_widget(self.ok_button, 'primary_button')
        if self.cancel_button:
            apply_theme_to_widget(self.cancel_button, 'secondary_button')
    
    def _style_button(self, button: QPushButton, style: str = "secondary"):
        """Apply styling to a button using the theme manager."""
        if style == "primary":
            apply_theme_to_widget(button, 'primary_button')
        elif style == "secondary":
            apply_theme_to_widget(button, 'secondary_button')
        elif style == "danger":
            apply_theme_to_widget(button, 'danger_button')
        elif style == "success":
            apply_theme_to_widget(button, 'success_button')
        else:
            apply_theme_to_widget(button, 'secondary_button')
    
    # ========== UTILITY METHODS ==========
    
    def log_debug(self, message: str):
        """Log a debug message with dialog context."""
        logger.debug(f"[{self.__class__.__name__}] {message}")
    
    def log_error(self, message: str):
        """Log an error message with dialog context."""
        logger.error(f"[{self.__class__.__name__}] {message}")
    
    def show_error_message(self, title: str, message: str):
        """Show an error message dialog."""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.critical(self, title, message)
    
    def show_info_message(self, title: str, message: str):
        """Show an information message dialog."""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, title, message)


class BaseChessBoardDialog(BaseDialog):
    """
    Base class for dialogs that include a chess board.

    Provides standardized chess board integration with left-right layout.
    """

    def __init__(self, parent: Optional[QWidget] = None, title: str = "Chess Board Dialog",
                 size: Tuple[int, int] = (800, 600), board_mode: str = "pattern"):
        """
        Initialize the chess board dialog.

        Args:
            parent: Parent widget
            title: Dialog window title
            size: Dialog size as (width, height)
            board_mode: Chess board mode ('pattern', 'range', 'area', 'adjacency', 'selection', 'preview')
        """
        self.board_mode = board_mode
        self.chess_board = None

        super().__init__(parent, title, size)

    def setup_ui(self):
        """Setup the chess board dialog UI with left-right layout."""
        # Create left-right layout
        main_layout, self.left_layout, self.right_layout = self.create_left_right_layout()
        self.content_layout.addLayout(main_layout)

        # Setup left side (controls)
        self.setup_controls()

        # Setup right side (chess board)
        self.setup_chess_board()

    @abstractmethod
    def setup_controls(self):
        """
        Setup the control widgets on the left side.

        Subclasses should implement this to add their specific controls
        to self.left_layout.
        """
        pass

    def setup_chess_board(self):
        """Setup the chess board on the right side."""
        try:
            from core.ui.centralized_board import EnhancedChessBoardWidget

            # Create chess board
            self.chess_board = EnhancedChessBoardWidget(
                mode=self.board_mode,
                tile_size=50
            )

            # Add to right layout
            board_group = self.create_section_group("Chess Board")
            board_layout = QVBoxLayout()
            board_layout.addWidget(self.chess_board)
            board_group.setLayout(board_layout)
            self.right_layout.addWidget(board_group)

            # Connect board signals
            if hasattr(self.chess_board, 'pattern_changed'):
                self.chess_board.pattern_changed.connect(self.mark_data_changed)

            self.log_debug(f"Chess board setup complete in {self.board_mode} mode")

        except Exception as e:
            self.log_error(f"Error setting up chess board: {e}")

    def get_board_pattern(self) -> list:
        """Get the current board pattern."""
        if self.chess_board and hasattr(self.chess_board, 'get_pattern'):
            return self.chess_board.get_pattern()
        return [[0 for _ in range(8)] for _ in range(8)]

    def set_board_pattern(self, pattern: list):
        """Set the board pattern."""
        if self.chess_board and hasattr(self.chess_board, 'set_pattern'):
            self.chess_board.set_pattern(pattern)

    def get_piece_position(self) -> list:
        """Get the current piece position."""
        if self.chess_board and hasattr(self.chess_board, 'get_piece_position'):
            return self.chess_board.get_piece_position()
        return [3, 3]  # Default center position

    def set_piece_position(self, position: list):
        """Set the piece position."""
        if self.chess_board and hasattr(self.chess_board, 'set_piece_position'):
            self.chess_board.set_piece_position(position)
