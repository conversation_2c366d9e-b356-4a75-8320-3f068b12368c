"""
Piece Promotion Manager for Adventure Chess Creator

This module handles all promotion functionality for the piece editor:
- Primary and secondary promotion management
- Promotion selector dialog handling
- Promotion data processing and validation
- Promotion UI updates and display
- Promotion piece selection and filtering

Extracted from piece_editor.py to improve maintainability and make
promotion management more modular and easier to test.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QScrollArea,
    QWidget, QCheckBox, QLineEdit, QMessageBox
)
from PyQt6.QtCore import Qt

from config import PIECES_DIR
from utils.simple_bridge import simple_bridge

logger = logging.getLogger(__name__)


class PromotionSelectorDialog(QDialog):
    """Dialog for selecting promotion pieces with checkboxes"""
    
    def __init__(self, parent=None, title="Select Promotion Pieces", selected_pieces=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumSize(400, 500)

        # Apply dark theme styling to dialog
        self.setStyleSheet("""
            QDialog {
                background-color: #1a202c;
                color: #e2e8f0;
            }
            QLabel {
                color: #e2e8f0;
            }
            QLineEdit {
                background-color: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 5px;
                color: #e2e8f0;
            }
            QScrollArea {
                background-color: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 4px;
            }
            QPushButton {
                background-color: #4a5568;
                color: #e2e8f0;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #718096;
            }
            QPushButton:pressed {
                background-color: #2d3748;
            }
        """)

        self.selected_pieces = selected_pieces.copy() if selected_pieces else []
        self.piece_checkboxes = {}

        self.setup_ui()
        self.refresh_piece_list()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel(f"""
        <b>{self.windowTitle()}</b><br>
        Select pieces that this piece can promote to. Check the boxes for pieces you want to include.
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("padding: 10px; background-color: #2d3748; color: #a0aec0; border: 1px solid #4a5568; border-radius: 4px;")
        layout.addWidget(instructions)
        
        # Search bar
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("🔍 Search:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Type to filter pieces...")
        self.search_edit.textChanged.connect(self.filter_pieces)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        # Scroll area for checkboxes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        
        self.checkboxes_widget = QWidget()
        self.checkboxes_layout = QVBoxLayout()
        self.checkboxes_widget.setLayout(self.checkboxes_layout)
        scroll_area.setWidget(self.checkboxes_widget)
        
        layout.addWidget(scroll_area)
        
        # Selection info
        self.selection_info = QLabel("0 pieces selected")
        self.selection_info.setStyleSheet("font-weight: bold; color: #a0aec0;")
        layout.addWidget(self.selection_info)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(select_all_btn)
        
        clear_all_btn = QPushButton("Clear All")
        clear_all_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_all_btn)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def refresh_piece_list(self):
        """Refresh the list of available pieces"""
        # Clear existing checkboxes
        for checkbox in self.piece_checkboxes.values():
            checkbox.setParent(None)
        self.piece_checkboxes.clear()
        
        # Get list of piece files
        if not os.path.exists(PIECES_DIR):
            return
        
        piece_files = [f for f in os.listdir(PIECES_DIR) if f.endswith('.json')]
        
        for piece_file in sorted(piece_files):
            try:
                base_filename = piece_file.replace('.json', '')
                piece_data, error = simple_bridge.load_piece_for_ui(base_filename)

                if piece_data:
                    piece_name = piece_data.get('name', base_filename)

                    # Show error status if there was an error, but still allow selection
                    if error:
                        display_text = f"{piece_name} ({base_filename}) - Warning: {error}"
                        logger.warning(f"Piece {base_filename} loaded with warning: {error}")
                    else:
                        display_text = f"{piece_name} ({base_filename})"

                    checkbox = QCheckBox(display_text)
                    checkbox.setChecked(base_filename in self.selected_pieces)
                    checkbox.stateChanged.connect(self.update_selection_info)

                    # Apply dark theme styling to checkbox
                    checkbox.setStyleSheet("""
                        QCheckBox {
                            color: #e2e8f0;
                            spacing: 5px;
                        }
                        QCheckBox::indicator {
                            width: 16px;
                            height: 16px;
                        }
                        QCheckBox::indicator:unchecked {
                            background-color: #4a5568;
                            border: 1px solid #718096;
                            border-radius: 3px;
                        }
                        QCheckBox::indicator:checked {
                            background-color: #68d391;
                            border: 1px solid #48bb78;
                            border-radius: 3px;
                        }
                    """)

                    self.piece_checkboxes[base_filename] = checkbox
                    self.checkboxes_layout.addWidget(checkbox)
                else:
                    logger.error(f"Failed to load piece {base_filename}: {error}")

            except Exception as e:
                logger.error(f"Error loading piece {piece_file}: {e}")
        
        self.update_selection_info()
    
    def filter_pieces(self, text):
        """Filter pieces based on search text"""
        text = text.lower()
        for filename, checkbox in self.piece_checkboxes.items():
            checkbox_text = checkbox.text().lower()
            checkbox.setVisible(text in checkbox_text)
    
    def select_all(self):
        """Select all visible pieces"""
        for checkbox in self.piece_checkboxes.values():
            if checkbox.isVisible():
                checkbox.setChecked(True)
    
    def clear_all(self):
        """Clear all selections"""
        for checkbox in self.piece_checkboxes.values():
            checkbox.setChecked(False)
    
    def update_selection_info(self):
        """Update the selection info label"""
        selected_count = sum(1 for cb in self.piece_checkboxes.values() if cb.isChecked())
        self.selection_info.setText(f"{selected_count} pieces selected")
    
    def get_selected_pieces(self):
        """Get list of selected piece filenames"""
        return [filename for filename, checkbox in self.piece_checkboxes.items() if checkbox.isChecked()]


class PiecePromotionManager:
    """
    Handles all promotion functionality for the piece editor.
    
    This class manages:
    - Primary and secondary promotion management
    - Promotion selector dialog handling
    - Promotion data processing and validation
    - Promotion UI updates and display
    - Promotion piece selection and filtering
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the promotion manager.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
        
        # Initialize promotion lists
        self.editor.primary_promotions = []
        self.editor.secondary_promotions = []
    
    def open_primary_promotion_selector(self) -> None:
        """Open the primary promotion selector dialog."""
        try:
            dialog = PromotionSelectorDialog(
                self.editor, 
                "Select Primary Promotion Pieces", 
                self.editor.primary_promotions
            )
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.editor.primary_promotions = dialog.get_selected_pieces()
                self.update_promotion_displays()
                self.editor.mark_unsaved_changes()
                
                logger.info(f"Primary promotions updated: {len(self.editor.primary_promotions)} pieces selected")
                
        except Exception as e:
            logger.error(f"Error opening primary promotion selector: {e}")
            QMessageBox.critical(self.editor, "Error", f"Failed to open promotion selector: {str(e)}")
    
    def open_secondary_promotion_selector(self) -> None:
        """Open the secondary promotion selector dialog."""
        try:
            dialog = PromotionSelectorDialog(
                self.editor, 
                "Select Secondary Promotion Pieces", 
                self.editor.secondary_promotions
            )
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.editor.secondary_promotions = dialog.get_selected_pieces()
                self.update_promotion_displays()
                self.editor.mark_unsaved_changes()
                
                logger.info(f"Secondary promotions updated: {len(self.editor.secondary_promotions)} pieces selected")
                
        except Exception as e:
            logger.error(f"Error opening secondary promotion selector: {e}")
            QMessageBox.critical(self.editor, "Error", f"Failed to open promotion selector: {str(e)}")
    
    def update_promotion_displays(self) -> None:
        """Update the promotion display widgets."""
        try:
            # Use the new refresh method from the main editor
            if hasattr(self.editor, 'refresh_promotion_display'):
                self.editor.refresh_promotion_display()

            logger.debug(f"Updated promotion displays: {len(self.editor.primary_promotions)} primary, {len(self.editor.secondary_promotions)} secondary")

        except Exception as e:
            logger.error(f"Error updating promotion displays: {e}")
    
    def get_promotion_data(self) -> Dict[str, List[str]]:
        """
        Get the current promotion data.
        
        Returns:
            Dictionary containing promotion data
        """
        return {
            "primaryPromotions": self.editor.primary_promotions.copy(),
            "secondaryPromotions": self.editor.secondary_promotions.copy()
        }
    
    def set_promotion_data(self, promotion_data: Dict[str, Any]) -> None:
        """
        Set promotion data from external source.
        
        Args:
            promotion_data: Dictionary containing promotion data
        """
        try:
            # Set primary promotions
            primary_promotions = promotion_data.get('primaryPromotions', [])
            if isinstance(primary_promotions, list):
                self.editor.primary_promotions = primary_promotions.copy()
            else:
                self.editor.primary_promotions = []
                logger.warning("Primary promotions data is not a list, defaulting to empty")
            
            # Set secondary promotions
            secondary_promotions = promotion_data.get('secondaryPromotions', [])
            if isinstance(secondary_promotions, list):
                self.editor.secondary_promotions = secondary_promotions.copy()
            else:
                self.editor.secondary_promotions = []
                logger.warning("Secondary promotions data is not a list, defaulting to empty")
            
            # Update displays
            self.update_promotion_displays()
            
            logger.info(f"Promotion data set: {len(self.editor.primary_promotions)} primary, {len(self.editor.secondary_promotions)} secondary")
            
        except Exception as e:
            logger.error(f"Error setting promotion data: {e}")
            raise
    
    def reset_promotions(self) -> None:
        """Reset promotions to default (empty) state."""
        try:
            self.editor.primary_promotions = []
            self.editor.secondary_promotions = []
            self.update_promotion_displays()
            
            logger.info("Promotions reset to default state")
            
        except Exception as e:
            logger.error(f"Error resetting promotions: {e}")
    
    def validate_promotion_data(self) -> List[str]:
        """
        Validate promotion data for consistency.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        try:
            # Check if promotion pieces exist
            for piece_name in self.editor.primary_promotions:
                if not self._piece_exists(piece_name):
                    errors.append(f"Primary promotion piece '{piece_name}' does not exist")
            
            for piece_name in self.editor.secondary_promotions:
                if not self._piece_exists(piece_name):
                    errors.append(f"Secondary promotion piece '{piece_name}' does not exist")
            
            # Check for duplicates within each list
            if len(self.editor.primary_promotions) != len(set(self.editor.primary_promotions)):
                errors.append("Primary promotions contain duplicates")
            
            if len(self.editor.secondary_promotions) != len(set(self.editor.secondary_promotions)):
                errors.append("Secondary promotions contain duplicates")
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def _piece_exists(self, piece_name: str) -> bool:
        """
        Check if a piece file exists.
        
        Args:
            piece_name: Name of the piece to check
            
        Returns:
            True if piece exists, False otherwise
        """
        try:
            piece_path = os.path.join(PIECES_DIR, f"{piece_name}.json")
            return os.path.exists(piece_path)
        except Exception:
            return False
