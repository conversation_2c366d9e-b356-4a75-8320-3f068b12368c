# Adventure Chess Creator - Technical Documentation Dictionary

This directory contains comprehensive technical documentation for the Adventure Chess Creator codebase, organized as a searchable dictionary of terms, concepts, and architectural patterns.

## Directory Structure

```
dictionary/
├── README.md                    # This file - overview and navigation
├── architecture/               # System architecture documentation
│   ├── base_classes.md         # Base class hierarchy and patterns
│   ├── data_flow.md            # Data flow patterns and ownership
│   ├── single_source_truth.md  # Single source of truth implementation
│   └── module_organization.md  # Module and directory organization
├── data_structures/            # Data structure definitions
│   ├── piece_data.md           # Piece data structure and fields
│   ├── ability_data.md         # Ability data structure and fields
│   ├── movement_data.md        # Movement data structure and fields
│   └── ui_state_data.md        # UI state data structures
├── interfaces/                 # Interface and API documentation
│   ├── editor_interfaces.md    # Editor interface patterns
│   ├── data_interfaces.md      # Data access interfaces
│   └── manager_interfaces.md   # Manager class interfaces
├── patterns/                   # Design patterns and conventions
│   ├── inheritance_patterns.md # Inheritance and base class patterns
│   ├── data_ownership.md       # Data ownership patterns
│   ├── ui_patterns.md          # UI component patterns
│   └── error_handling.md       # Error handling patterns
├── field_definitions/          # Detailed field definitions
│   ├── piece_fields.md         # All piece-related fields
│   ├── ability_fields.md       # All ability-related fields
│   ├── movement_fields.md      # All movement-related fields
│   └── ui_fields.md            # All UI-related fields
├── cross_references/           # Cross-reference documentation
│   ├── component_relationships.md  # How components relate to each other
│   ├── data_dependencies.md    # Data dependency mappings
│   └── legacy_mappings.md      # Legacy field to new field mappings
└── glossary/                   # Glossary and terminology
    ├── technical_terms.md      # Technical terminology
    ├── layman_terms.md         # Layman's explanations
    └── acronyms.md             # Acronyms and abbreviations
```

## How to Use This Documentation

### For Developers
- Start with `architecture/` to understand the overall system design
- Refer to `data_structures/` for specific data format information
- Use `field_definitions/` for detailed field documentation
- Check `cross_references/` to understand component relationships

### For AI Assistants
- Use `patterns/` to understand coding conventions and design patterns
- Refer to `interfaces/` for API and interface documentation
- Check `cross_references/data_dependencies.md` for data flow understanding
- Use `glossary/` for terminology clarification

### For New Contributors
- Begin with `glossary/layman_terms.md` for accessible explanations
- Read `architecture/module_organization.md` for codebase navigation
- Study `patterns/` to understand established conventions
- Use `field_definitions/` as a reference while working with data

## Quick Reference

### Key Architectural Concepts
- **Single Source of Truth**: All data has one authoritative location
- **Base Class Hierarchy**: Standardized inheritance patterns
- **Data Ownership Registry**: Central registry of data ownership rules
- **Unified Interfaces**: Consistent APIs across all components

### Core Data Types
- **Piece Data**: Configuration for chess pieces (movement, abilities, etc.)
- **Ability Data**: Configuration for piece abilities and tags
- **Movement Data**: Movement patterns and piece positioning
- **UI State Data**: User interface state and preferences

### Important Patterns
- **Property-Based Access**: Legacy fields redirect to canonical sources
- **Manager-Mediated Access**: Complex data accessed through managers
- **Interface-Mediated Access**: Standardized data access through interfaces
- **Direct Property Access**: Simple data accessed directly

## Navigation Tips

1. **Search by Topic**: Use your editor's search function across all `.md` files
2. **Follow Cross-References**: Links between documents show relationships
3. **Check Multiple Perspectives**: Same concept may be documented in multiple files
4. **Use Glossary**: When encountering unfamiliar terms, check the glossary

## Maintenance

This documentation is maintained alongside the codebase and should be updated when:
- New architectural patterns are introduced
- Data structures are modified
- New interfaces are created
- Legacy compatibility changes

## Contributing

When adding new documentation:
1. Follow the established directory structure
2. Use clear, descriptive headings
3. Include cross-references to related concepts
4. Provide both technical and layman explanations where appropriate
5. Update this README if adding new categories

---

*This documentation was created as part of the Adventure Chess Creator refactoring project to establish professional-grade organization and maintainability.*
