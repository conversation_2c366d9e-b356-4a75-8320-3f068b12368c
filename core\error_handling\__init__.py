"""
Core Error Handling Module for Adventure Chess Creator

This module contains comprehensive error handling functionality
moved from enhancements/ui/error_handling_system.py for better organization.
"""

# Import all error handling components
from .error_handling_system import (  # Core error handling classes; Data manager integration - DirectDataManager moved to schemas.data_manager; User-friendly error system; Convenience functions; Global error handler
    <PERSON>rror<PERSON>ate<PERSON><PERSON>,
    ErrorContext,
    ErrorHandler,
    ErrorMessageTranslator,
    ErrorSeverity,
    UserFriendlyError,
    UserFriendlyErrorDialog,
    error_handler,
    safe_file_load,
    safe_file_save,
    show_user_friendly_error,
)

# Import standardized error decorators
from .standardized_error_decorators import (  # Core decorators; Convenience decorators
    data_delete_handler,
    data_load_handler,
    data_operation_error_handler,
    data_save_handler,
    file_handler,
    file_operation_error_handler,
    standardized_error_handler,
    ui_handler,
    ui_operation_error_handler,
    validation_error_handler,
    validation_handler,
)

# Re-export all components
__all__ = [
    # Core error handling classes
    "ErrorSeverity",
    "ErrorCategory",
    "<PERSON>rror<PERSON>ontex<PERSON>",
    "<PERSON>rrorHandler",
    # Data manager integration - DirectDataManager moved to schemas.data_manager
    # User-friendly error system
    "UserFriendlyError",
    "ErrorMessageTranslator",
    "UserFriendlyErrorDialog",
    # Convenience functions
    "safe_file_load",
    "safe_file_save",
    "show_user_friendly_error",
    # Global error handler
    "error_handler",
    # Standardized error decorators
    "standardized_error_handler",
    "data_operation_error_handler",
    "ui_operation_error_handler",
    "file_operation_error_handler",
    "validation_error_handler",
    # Convenience decorators
    "data_load_handler",
    "data_save_handler",
    "data_delete_handler",
    "ui_handler",
    "file_handler",
    "validation_handler",
]
