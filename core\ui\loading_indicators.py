"""
Enhanced Loading Indicators for Adventure Chess Creator

This module provides loading indicators with animations and status feedback.
"""

import logging

from PyQt6.QtCore import QEasingCurve, QPropertyAnimation, Qt, QTimer
from PyQt6.QtWidgets import (
    QGraphicsOpacityEffect,
    QLabel,
    QProgressBar,
    QVBoxLayout,
    QWidget,
)

from .color_schemes import ColorSchemes

logger = logging.getLogger(__name__)


class EnhancedLoadingIndicator(QWidget):
    """Enhanced loading indicator with animations and status"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_loading = False
        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """Setup the loading indicator UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(
            f"""
            QLabel {{
                color: {ColorSchemes.INFO};
                font-weight: bold;
                font-size: 12px;
                padding: 5px;
            }}
        """
        )

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(
            f"""
            QProgressBar {{
                border: 2px solid {ColorSchemes.LOADING};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: {ColorSchemes.LIGHT_BG};
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {ColorSchemes.LOADING};
                border-radius: 6px;
                margin: 1px;
            }}
        """
        )

        # Details label (optional)
        self.details_label = QLabel("")
        self.details_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.details_label.setStyleSheet(
            """
            QLabel {
                color: #6c757d;
                font-size: 10px;
                padding: 2px;
            }
        """
        )

        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.details_label)
        self.setLayout(layout)

        # Initially hidden
        self.hide()

    def setup_animations(self):
        """Setup fade animations"""
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)

        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

    def show_loading(self, message: str, details: str = ""):
        """Show loading state with message"""
        self.is_loading = True
        self.status_label.setText(message)
        self.details_label.setText(details)
        self.progress_bar.setValue(0)

        # Fade in
        self.show()
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()

        logger.debug(f"Loading indicator shown: {message}")

    def update_progress(self, value: int, message: str = "", details: str = ""):
        """Update progress and optionally message"""
        if self.is_loading:
            self.progress_bar.setValue(value)
            if message:
                self.status_label.setText(message)
            if details:
                self.details_label.setText(details)

    def hide_loading(self, success_message: str = ""):
        """Hide loading state with optional success message"""
        if success_message:
            self.status_label.setText(success_message)
            self.status_label.setStyleSheet(
                f"""
                QLabel {{
                    color: {ColorSchemes.SUCCESS};
                    font-weight: bold;
                    font-size: 12px;
                    padding: 5px;
                }}
            """
            )
            self.progress_bar.setValue(100)

            # Show success briefly then fade out
            QTimer.singleShot(1500, self._fade_out)
        else:
            self._fade_out()

    def _fade_out(self):
        """Fade out the loading indicator"""
        self.is_loading = False
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self.hide)
        self.fade_animation.start()


class OperationFeedbackManager:
    """Manager for operation feedback and loading states"""

    def __init__(self, parent=None):
        self.parent = parent
        self.active_operations = {}
        self.feedback_widgets = {}

    def start_operation(self, operation_id: str, message: str, details: str = ""):
        """Start tracking an operation"""
        self.active_operations[operation_id] = {
            "message": message,
            "details": details,
            "start_time": QTimer(),
        }

        # Show feedback if widget is registered
        if operation_id in self.feedback_widgets:
            widget = self.feedback_widgets[operation_id]
            widget.show_loading(message, details)

        logger.debug(f"Operation started: {operation_id} - {message}")

    def update_operation(
        self, operation_id: str, progress: int, message: str = "", details: str = ""
    ):
        """Update operation progress"""
        if operation_id in self.active_operations:
            self.active_operations[operation_id].update(
                {
                    "progress": progress,
                    "message": message
                    or self.active_operations[operation_id]["message"],
                    "details": details
                    or self.active_operations[operation_id]["details"],
                }
            )

            # Update feedback widget
            if operation_id in self.feedback_widgets:
                widget = self.feedback_widgets[operation_id]
                widget.update_progress(progress, message, details)

    def complete_operation(self, operation_id: str, success_message: str = ""):
        """Complete an operation"""
        if operation_id in self.active_operations:
            del self.active_operations[operation_id]

            # Hide feedback widget
            if operation_id in self.feedback_widgets:
                widget = self.feedback_widgets[operation_id]
                widget.hide_loading(success_message)

            logger.debug(f"Operation completed: {operation_id}")

    def register_feedback_widget(
        self, operation_id: str, widget: EnhancedLoadingIndicator
    ):
        """Register a feedback widget for an operation type"""
        self.feedback_widgets[operation_id] = widget

    def get_active_operations(self) -> list:
        """Get list of active operation IDs"""
        return list(self.active_operations.keys())
