{"version": "1.0.0", "name": "Pawn", "description": "Moves forward one square, captures diagonally, with special first move", "role": "Supporter", "can_castle": false, "color_directional": false, "can_capture": true, "black_icon": "black-pawn.png", "white_icon": "black-pawn.png", "movement": {"type": "custom", "pattern": [[0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 2, 1, 2, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "piece_position": [3, 3]}, "enable_recharge": true, "max_points": 1, "starting_points": 1, "recharge_type": "turn<PERSON><PERSON><PERSON><PERSON>", "abilities": ["pawn_movement_2", "en_passant"], "promotions": ["Queen", "Rook", "<PERSON>", "<PERSON>"], "secondary_promotions": [], "turn_recharge_points": 1, "primary_promotions": ["Queen", "Rook", "<PERSON>", "<PERSON>"]}