["tests/test_application_stress.py::TestApplicationStress::test_concurrent_data_access", "tests/test_application_stress.py::TestApplicationStress::test_core_module_imports_stress", "tests/test_application_stress.py::TestApplicationStress::test_data_manager_stress", "tests/test_application_stress.py::TestApplicationStress::test_json_formatter_stress", "tests/test_application_stress.py::TestApplicationStress::test_memory_usage_patterns", "tests/test_application_stress.py::TestApplicationStress::test_performance_timing", "tests/test_dialog_integration.py::TestAreaEffectMaskDialog::test_area_dialog_initialization", "tests/test_dialog_integration.py::TestDialogDataFlow::test_ability_editor_to_range_dialog_flow", "tests/test_dialog_integration.py::TestDialogDataFlow::test_piece_editor_to_pattern_dialog_flow", "tests/test_dialog_integration.py::TestDialogValidation::test_dialog_cancel_handling", "tests/test_dialog_integration.py::TestDialogValidation::test_pattern_dialog_validation", "tests/test_dialog_integration.py::TestPatternEditorDialog::test_pattern_data_handling", "tests/test_dialog_integration.py::TestPatternEditorDialog::test_pattern_dialog_initialization", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_ability_selection", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_dialog_initialization", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_dialog_integration_with_piece_editor", "tests/test_dialog_integration.py::TestRangeEditorDialog::test_range_dialog_initialization", "tests/test_dialog_integration.py::TestRangeEditorDialog::test_range_mask_handling", "tests/test_enhanced_error_handling.py::test_basic_error_handling", "tests/test_enhanced_error_handling.py::test_enhanced_data_manager", "tests/test_enhanced_error_handling.py::test_error_recovery_suggestions", "tests/test_enhanced_error_handling.py::test_error_summary", "tests/test_enhanced_error_handling.py::test_file_operation_error_handling", "tests/test_enhanced_error_handling.py::test_filename_cleaning", "tests/test_enhanced_error_handling.py::test_safe_operations", "tests/test_main_app_integration.py::TestMainAppIntegration::test_cache_integration", "tests/test_main_app_integration.py::TestMainAppIntegration::test_data_manager_file_operations", "tests/test_main_app_integration.py::TestMainAppIntegration::test_error_handling", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_data_manager_initialization", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_loading_system_components", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_patches_application", "tests/test_main_app_integration.py::TestMainAppIntegration::test_performance_benefits", "tests/test_performance_optimization.py::TestCacheOptimization::test_cache_integrated_data_manager_async_operations", "tests/test_performance_optimization.py::TestCacheOptimization::test_enhanced_cache_manager_initialization", "tests/test_performance_optimization.py::TestFileSystemOptimization::test_async_ability_operations", "tests/test_performance_optimization.py::TestFileSystemOptimization::test_async_file_operations", "tests/test_performance_optimization.py::TestFileSystemOptimization::test_memory_cleanup_functionality", "tests/test_performance_optimization.py::TestFileSystemOptimization::test_memory_monitoring_initialization", "tests/test_performance_optimization.py::TestLazyLoadingOptimization::test_cache_cleanup_functionality", "tests/test_performance_optimization.py::TestLazyLoadingOptimization::test_memory_management_initialization", "tests/test_performance_optimization.py::TestLazyLoadingOptimization::test_memory_usage_checking", "tests/test_performance_optimization.py::TestPerformanceIntegration::test_all_modules_import_successfully", "tests/test_performance_optimization.py::TestPerformanceIntegration::test_memory_monitoring_across_modules", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_creation_complete", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_creation_minimal", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_serialization", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_validation_errors", "tests/test_pydantic_models.py::TestEdgeCases::test_empty_strings_and_lists", "tests/test_pydantic_models.py::TestEdgeCases::test_large_numbers", "tests/test_pydantic_models.py::TestEdgeCases::test_unicode_and_special_characters", "tests/test_pydantic_models.py::TestModelIntegration::test_complex_movement_pattern", "tests/test_pydantic_models.py::TestModelIntegration::test_model_version_consistency", "tests/test_pydantic_models.py::TestModelIntegration::test_piece_with_abilities_references", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_creation_default", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_custom_pattern", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_validation_errors", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_creation_complete", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_creation_minimal", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_serialization", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_validation_errors", "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_direct_to_pydantic_compatibility", "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_pydantic_to_direct_compatibility", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_ability_save_load_cycle", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_file_corruption_handling", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_piece_save_load_cycle", "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_concurrent_save_load", "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_large_data_save_load", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_ability_validation_workflow", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_piece_validation_workflow", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_validation_error_handling", "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_ability_ui_workflow_simulation", "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_piece_ui_workflow_simulation", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_core_module_structure", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_data_consistency_patterns", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_data_ownership_registry_exists", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_legacy_compatibility_layer", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_no_duplicate_data_storage", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_no_legacy_ui_references", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_no_redundant_imports", "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_single_source_data_interface_exists", "tests/test_ui_automation.py::TestAbilityEditor::test_ability_editor_initialization", "tests/test_ui_automation.py::TestAbilityEditor::test_ability_editor_tag_management", "tests/test_ui_automation.py::TestDialogIntegration::test_dialog_creation", "tests/test_ui_automation.py::TestDialogIntegration::test_dialog_execution", "tests/test_ui_automation.py::TestMainWindowIntegration::test_main_window_creation", "tests/test_ui_automation.py::TestMainWindowIntegration::test_main_window_editor_integration", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_data_collection", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_data_population", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_initialization", "tests/test_ui_automation.py::TestUIDataIntegrity::test_ui_data_round_trip", "tests/test_ui_automation.py::TestUIDataIntegrity::test_ui_validation_feedback", "tests/test_ui_automation.py::TestUIPerformance::test_ui_responsiveness"]