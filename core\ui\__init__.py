"""
Core UI Module for Adventure Chess Creator

This module contains core UI components and visual feedback systems.
Consolidated from the enhancements/ui/ folder and ui/ directory for better organization.
"""

from .adjacency_preview import AdjacencyPreviewWidget

# Visual feedback components (from enhancements)
from .centralized_board import EnhancedChessBoardWidget
from .color_schemes import ColorSchemes
from .file_operations import FileOperationsWidget

# UI components (from ui/ directory)
# Note: GridToggleWidget and AreaEffectGridWidget have been replaced by EnhancedChessBoardWidget
from .grid_visualization import EnhancedGridVisualization, GridPatternEditor
from .inline_selection_widgets import InlineAbilitySelector, InlinePieceSelector
from .loading_indicators import <PERSON>hancedLoadingIndicator, OperationFeedbackManager
from .range_preview import RangePreviewWidget
from .status_display import ValidationStatusWidget

# UI components (from enhancements integration)
from .ui_components import (  # Search components
    EnhancedSearchWidget,
    FileIndexBrowser,
    SearchResultsWidget,
    SearchWorker,
)

# Validation components (from validation module)
from ..validation.validation_rules import (
    EnhancedValidationMixin,
    ValidationRules,
    create_ability_validation_rules,
    create_piece_validation_rules,
)
from .ui_utilities import (
    create_dialog_buttons,
    create_grid_instructions,
    create_info_box,
    create_legend_item,
    create_section_header,
)
from .ui_utils import (
    ResponsiveLayout,
    ResponsiveScrollArea,
    ResponsiveSplitter,
    ResponsiveWidget,
    TabWidgetResponsive,
    create_scrollable_content,
    get_screen_size_category,
    make_widget_responsive,
    optimize_layout_for_small_screens,
    setup_responsive_window,
    setup_responsive_window_with_fallback,
)
from .visual_feedback_integration import (
    VisualFeedbackIntegrator,
    VisualFeedbackManager,
    apply_visual_feedback_to_editor,
    apply_visual_feedback_to_main_window,
    get_visual_feedback_manager,
)

__all__ = [
    # Visual feedback components
    "ColorSchemes",
    "EnhancedLoadingIndicator",
    "OperationFeedbackManager",
    "EnhancedGridVisualization",
    "GridPatternEditor",
    "VisualFeedbackIntegrator",
    "VisualFeedbackManager",
    "get_visual_feedback_manager",
    "apply_visual_feedback_to_editor",
    "apply_visual_feedback_to_main_window",
    # Enhanced Chess Board Widget
    "EnhancedChessBoardWidget",
    # Grid components (replaced by EnhancedChessBoardWidget)
    # "GridToggleWidget",  # Deprecated - use EnhancedChessBoardWidget
    # "AreaEffectGridWidget",  # Deprecated - use EnhancedChessBoardWidget
    # File operations
    "FileOperationsWidget",
    # Status display
    "ValidationStatusWidget",
    # Utility functions
    "create_section_header",
    "create_info_box",
    "create_legend_item",
    "create_dialog_buttons",
    "create_grid_instructions",
    # Inline selection widgets
    "InlinePieceSelector",
    "InlineAbilitySelector",
    # Adjacency preview
    "AdjacencyPreviewWidget",
    "RangePreviewWidget",
    # Responsive UI utilities
    "ResponsiveScrollArea",
    "ResponsiveWidget",
    "ResponsiveLayout",
    "ResponsiveSplitter",
    "TabWidgetResponsive",
    "setup_responsive_window",
    "get_screen_size_category",
    "setup_responsive_window_with_fallback",
    "make_widget_responsive",
    "create_scrollable_content",
    "optimize_layout_for_small_screens",
    # Search components
    "SearchWorker",
    "EnhancedSearchWidget",
    "SearchResultsWidget",
    "FileIndexBrowser",
    # Validation components
    "ValidationRules",
    "EnhancedValidationMixin",
    "create_piece_validation_rules",
    "create_ability_validation_rules",
]
