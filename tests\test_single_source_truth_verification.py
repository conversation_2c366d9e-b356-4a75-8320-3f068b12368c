#!/usr/bin/env python3
"""
Single Source of Truth Verification Tests for Adventure Chess Creator

This test suite verifies that the application properly implements single source
of truth patterns and has eliminated redundant data storage.
"""

import unittest
import os
import sys
import json
from typing import Dict, Any, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestSingleSourceTruthVerification(unittest.TestCase):
    """Test single source of truth implementation"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_data_dir = "data"
        self.test_pieces_dir = os.path.join(self.test_data_dir, "pieces")
        self.test_abilities_dir = os.path.join(self.test_data_dir, "abilities")
    
    def test_data_ownership_registry_exists(self):
        """Test that data ownership registry is properly implemented"""
        try:
            from core.managers.data_ownership_registry import data_ownership_registry
            self.assertIsNotNone(data_ownership_registry)
            print("✅ Data ownership registry exists and is accessible")
        except ImportError as e:
            self.fail(f"Data ownership registry not found: {e}")
    
    def test_single_source_data_interface_exists(self):
        """Test that single source data interface is implemented"""
        try:
            from core.interfaces.single_source_data_interface import SingleSourceDataInterface
            interface = SingleSourceDataInterface()
            self.assertIsNotNone(interface)
            print("✅ Single source data interface exists and is instantiable")
        except ImportError as e:
            self.fail(f"Single source data interface not found: {e}")
    
    def test_no_duplicate_data_storage(self):
        """Test that there are no duplicate data storage patterns"""
        # Check that piece data is only stored in one canonical location
        try:
            from schemas.data_manager import pydantic_data_manager, DirectDataManager

            # Both should exist - pydantic_data_manager is the main instance, DirectDataManager is compatibility layer
            self.assertIsNotNone(pydantic_data_manager)
            self.assertIsNotNone(DirectDataManager)
            print("✅ Unified data management system with compatibility layer exists")
        except ImportError as e:
            self.fail(f"Data managers not properly accessible: {e}")
    
    def test_legacy_compatibility_layer(self):
        """Test that legacy compatibility is properly implemented"""
        try:
            from schemas.migration import CompatibilityLayer
            
            # Test that compatibility layer methods exist
            self.assertTrue(hasattr(CompatibilityLayer, 'piece_to_dict'))
            self.assertTrue(hasattr(CompatibilityLayer, 'dict_to_piece'))
            self.assertTrue(hasattr(CompatibilityLayer, 'ability_to_dict'))
            self.assertTrue(hasattr(CompatibilityLayer, 'dict_to_ability'))
            print("✅ Legacy compatibility layer is properly implemented")
        except ImportError as e:
            self.fail(f"Legacy compatibility layer not found: {e}")
    
    def test_no_redundant_imports(self):
        """Test that there are no imports from removed packages"""
        # Check that no code tries to import from the removed 'enhancements' package
        import glob
        import re

        python_files = glob.glob("**/*.py", recursive=True)
        redundant_imports = []

        # Pattern to match actual import statements (not comments)
        import_pattern = re.compile(r'^\s*(from\s+enhancements|import\s+enhancements)', re.MULTILINE)

        for file_path in python_files:
            if "archive" in file_path or "__pycache__" in file_path:
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if import_pattern.search(content):
                        redundant_imports.append(file_path)
            except (UnicodeDecodeError, PermissionError):
                continue

        if redundant_imports:
            self.fail(f"Found redundant imports from 'enhancements' package in: {redundant_imports}")

        print("✅ No redundant imports from removed packages found")
    
    def test_core_module_structure(self):
        """Test that core module structure is properly organized"""
        core_modules = [
            "core.ui",
            "core.managers", 
            "core.interfaces",
            "core.workflow",
            "core.performance",
            "core.validation",
            "core.error_handling",
            "core.security"
        ]
        
        missing_modules = []
        for module in core_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.fail(f"Missing core modules: {missing_modules}")
        
        print("✅ Core module structure is properly organized")
    
    def test_data_consistency_patterns(self):
        """Test that data consistency patterns are implemented"""
        try:
            # Test that data managers use consistent patterns
            from schemas.data_manager import PydanticDataManager
            from utils.simple_bridge import SimpleBridge
            
            # Both should be accessible and functional
            manager = PydanticDataManager()
            self.assertIsNotNone(manager)
            
            # SimpleBridge should provide unified interface
            abilities = SimpleBridge.list_abilities()
            pieces = SimpleBridge.list_pieces()
            
            self.assertIsInstance(abilities, list)
            self.assertIsInstance(pieces, list)
            print("✅ Data consistency patterns are properly implemented")
        except Exception as e:
            self.fail(f"Data consistency patterns not working: {e}")
    
    def test_no_legacy_ui_references(self):
        """Test that there are no references to old UI structure"""
        import glob
        import re

        python_files = glob.glob("**/*.py", recursive=True)
        legacy_ui_refs = []

        # Pattern to match actual import statements (not comments)
        ui_import_pattern = re.compile(r'^\s*(from\s+ui\.|import\s+ui\.)', re.MULTILINE)

        for file_path in python_files:
            if "archive" in file_path or "__pycache__" in file_path:
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Check for old UI import patterns
                    if ui_import_pattern.search(content):
                        legacy_ui_refs.append(file_path)
            except (UnicodeDecodeError, PermissionError):
                continue

        if legacy_ui_refs:
            self.fail(f"Found legacy UI references in: {legacy_ui_refs}")

        print("✅ No legacy UI references found")

def run_comprehensive_verification():
    """Run comprehensive single source of truth verification"""
    print("🔍 Running Single Source of Truth Verification Tests...")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSingleSourceTruthVerification)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All Single Source of Truth verification tests passed!")
        print("✅ Application maintains proper data integrity patterns")
        print("✅ Legacy code cleanup is complete")
    else:
        print("❌ Some verification tests failed")
        print("❌ Manual review required for data integrity")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_verification()
    sys.exit(0 if success else 1)
