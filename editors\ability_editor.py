"""
Ability Editor for Adventure Chess - Refactored Version

This file now serves as a compatibility layer that imports the refactored
AbilityEditorWindow from the new ability_editor package.

The original 3,570-line file has been split into focused modules:
- ability_editor_main.py: Main editor coordination
- ability_data_handlers.py: Data operations and validation
- ability_ui_components.py: UI creation and layout
- ability_tag_managers.py: Tag management and configuration

This refactoring improves maintainability and makes debugging easier,
especially for loading and saving issues.
"""

# Import the refactored AbilityEditorWindow from the new package structure
from .ability_editor.ability_editor_main import AbilityEditorWindow

# For backward compatibility, make the class available at the module level
__all__ = ['AbilityEditorWindow']