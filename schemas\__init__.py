"""
Adventure Chess Pydantic Schemas Package

Location: schemas/

Provides strongly-typed data models for pieces, abilities, and game components.
This package contains:
- base.py: Core data types and enums
- piece_schema.py: Piece and movement models
- ability_schema.py: Ability data models
- ability_tags.py: Tag-specific validation models
- data_manager.py: Pydantic-based data management
- migration.py: Data migration and compatibility utilities

Used by: All editors and data handlers for validation and type safety
"""

from .base import (
    Coordinate, 
    MovementType, 
    RechargeType, 
    ActivationMode,
    PieceRole,
    Pattern8x8,
    RangeMask8x8
)

from .piece_schema import Piece, Movement
from .ability_schema import Ability
from .ability_tags import *
from .data_manager import PydanticDataManager, pydantic_data_manager
from .migration import DataMigrationManager, CompatibilityLayer

__version__ = "1.0.0"

__all__ = [
    # Base types
    "Coordinate",
    "MovementType",
    "RechargeType",
    "ActivationMode",
    "PieceRole",
    "Pattern8x8",
    "RangeMask8x8",

    # Main models
    "Piece",
    "Movement",
    "Ability",

    # Data management
    "PydanticDataManager",
    "pydantic_data_manager",

    # Migration utilities
    "DataMigrationManager",
    "CompatibilityLayer",

    # Ability tag models (imported from ability_tags)
    "TAG_MODEL_REGISTRY",
    "get_tag_model",
    "validate_tag_data",
]
