{"Basic Attack": {"version": "1.0.0", "name": "", "description": "A basic attack ability", "cost": 1, "tags": ["capture"], "activationMode": "click", "captureTarget": "Enemy"}, "Magic Bolt": {"version": "1.0.0", "name": "", "description": "A magical projectile attack", "cost": 2, "tags": ["capture", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [true, true, true, true, false, true, true, true, true], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "captureTarget": "Enemy"}, "Teleport": {"version": "1.0.0", "name": "", "description": "Teleport to any empty square", "cost": 3, "tags": ["move", "range"], "activationMode": "click", "rangeMask": [[true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, false, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true, true]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false}, "Heal": {"version": "1.0.0", "name": "", "description": "Heal a friendly piece", "cost": 2, "tags": ["b<PERSON><PERSON><PERSON><PERSON>", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, true, false, true, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": true, "rangeEnemyOnly": false}, "Summon Pawn": {"version": "1.0.0", "name": "", "description": "Su<PERSON>on a pawn piece", "cost": 3, "tags": ["summon", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, true, false, true, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "summonPieces": ["Adventure Pawn"]}, "Shield": {"version": "1.0.0", "name": "", "description": "Create a protective barrier", "cost": 2, "tags": ["addObstacle", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, true, false, true, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false}, "Area Blast": {"version": "1.0.0", "name": "", "description": "Area of effect damage", "cost": 4, "tags": ["capture", "range", "areaEffect"], "activationMode": "click", "rangeMask": [[false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [true, true, true, true, false, true, true, true, true], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false], [false, false, false, false, true, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "captureTarget": "Enemy", "areaEffectMask": [[false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, true, false, true, false, false, false], [false, false, false, true, true, true, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false, false]], "areaEffectPosition": [4, 4]}}