# Adventure Chess Creator - Naming Conventions Guide

This document establishes consistent naming conventions across the Adventure Chess Creator codebase to improve maintainability and readability for both human developers and AI assistants.

## File and Directory Naming

### Python Files
- Use `snake_case` for all Python files
- Use descriptive names that clearly indicate the file's purpose
- Prefix with category when appropriate:
  - `base_*.py` for base classes
  - `*_manager.py` for manager classes
  - `*_handler.py` for data handler classes
  - `*_utils.py` for utility modules

**Examples:**
```
base_widgets.py
piece_data_handler.py
ability_tag_manager.py
ui_utils.py
editor_data_interface.py
```

### Directories
- Use `snake_case` for directory names
- Use plural forms for collections
- Group related functionality together

**Examples:**
```
editors/
ui/components/
utils/
schemas/
ability_tags/
```

## Class Naming

### Base Classes
- Use `Base` prefix for abstract base classes
- Use descriptive names that indicate the class purpose
- Follow PascalCase convention

**Examples:**
```python
class BaseWidget(ResponsiveWidget)
class BaseDataHandler
class BaseManager
class BaseFormWidget(BaseWidget)
```

### Concrete Classes
- Use PascalCase for all class names
- Include the class type as suffix when appropriate
- Use descriptive names that clearly indicate functionality

**Examples:**
```python
class PieceEditor(BaseEditor)
class AbilityTagManager(BaseManager)
class GridToggleWidget(BaseWidget)
class EnhancedPieceDataHandler(BaseDataHandler)
```

### Manager Classes
- Always end with `Manager` suffix
- Prefix with the domain they manage
- Use descriptive middle terms when needed

**Examples:**
```python
class AbilityTagManager
class PieceMovementManager
class UIComponentManager
class DataValidationManager
```

## Method and Function Naming

### Public Methods
- Use `snake_case` for all method names
- Use verb-noun pattern for actions
- Use descriptive names that indicate the method's purpose

**Examples:**
```python
def collect_data_from_ui()
def populate_ui_from_data()
def validate_piece_data()
def update_movement_pattern()
def save_ability_configuration()
```

### Private Methods
- Prefix with single underscore `_`
- Follow same naming conventions as public methods

**Examples:**
```python
def _validate_internal_state()
def _update_ui_components()
def _process_tag_data()
```

### Event Handlers
- Prefix with `on_` for event handler methods
- Use descriptive names that indicate the event

**Examples:**
```python
def on_data_changed()
def on_movement_type_selected()
def on_ability_tag_added()
def on_validation_failed()
```

### Property Methods
- Use descriptive names without prefixes
- Use `get_` and `set_` prefixes only when necessary for clarity

**Examples:**
```python
@property
def current_piece_data(self):
    return self._piece_data

@property
def is_valid(self):
    return len(self.validation_errors) == 0

def get_widget_by_name(self, name):
    return self.stored_widgets.get(name)
```

## Variable Naming

### Instance Variables
- Use `snake_case` for all variable names
- Use descriptive names that indicate the variable's purpose
- Prefix with underscore for private variables

**Examples:**
```python
self.current_data = {}
self.validation_errors = []
self.stored_widgets = {}
self._internal_state = {}
```

### Constants
- Use `UPPER_SNAKE_CASE` for constants
- Group related constants together
- Use descriptive names

**Examples:**
```python
DEFAULT_PIECE_ROLE = "Commander"
MAX_ABILITY_COST = 99
LAYOUT_MARGIN = 10
WIDGET_SPACING = 5
```

### Configuration Keys
- Use `snake_case` for configuration dictionary keys
- Use consistent naming patterns across similar configurations
- Group related keys with common prefixes

**Examples:**
```python
piece_config = {
    "name": "Knight",
    "role": "Guardian",
    "movement_type": "knight",
    "can_capture": True,
    "max_points": 3,
    "starting_points": 2
}
```

## Signal and Slot Naming

### PyQt6 Signals
- Use `snake_case` with descriptive names
- End with appropriate suffix indicating the signal type

**Examples:**
```python
data_changed = pyqtSignal()
validation_changed = pyqtSignal(bool)
piece_selected = pyqtSignal(str)
ability_added = pyqtSignal(dict)
```

### Slot Methods
- Use `on_` prefix followed by descriptive name
- Match the signal name when possible

**Examples:**
```python
def on_data_changed(self):
def on_validation_changed(self, is_valid):
def on_piece_selected(self, piece_name):
def on_ability_added(self, ability_data):
```

## Data Structure Naming

### Dictionary Keys
- Use `snake_case` for dictionary keys
- Use consistent naming across similar data structures
- Use descriptive names that indicate the data type

**Examples:**
```python
piece_data = {
    "name": "Custom Knight",
    "description": "A knight with special abilities",
    "movement_type": "knight",
    "ability_list": [],
    "max_points": 3
}
```

### Schema Field Names
- Use `snake_case` for Pydantic model fields
- Match corresponding UI widget names when possible
- Use consistent naming patterns

**Examples:**
```python
class PieceSchema(BaseAdventureChessModel):
    name: str
    description: str
    movement_type: str
    can_capture: bool
    max_points: int
    starting_points: int
```

## UI Component Naming

### Widget Names
- Use descriptive names that indicate the widget's purpose
- Include the widget type when storing widgets
- Use consistent patterns across similar widgets

**Examples:**
```python
self.store_widget("name_input", name_line_edit)
self.store_widget("description_text", description_text_edit)
self.store_widget("role_combo", role_combo_box)
self.store_widget("capture_checkbox", can_capture_checkbox)
```

### Layout Names
- Use descriptive names that indicate the layout's purpose
- Include the layout type when storing layouts

**Examples:**
```python
self.store_layout("main_layout", main_vertical_layout)
self.store_layout("form_layout", piece_form_layout)
self.store_layout("button_layout", button_horizontal_layout)
```

## File Operation Naming

### Save/Load Methods
- Use consistent naming patterns for file operations
- Include the data type in the method name
- Use descriptive suffixes for different operation types

**Examples:**
```python
def save_piece_data(self, filename=None)
def load_piece_data(self, filename)
def save_ability_configuration(self, filename=None)
def load_ability_from_file(self, filepath)
def export_piece_to_json(self, filepath)
```

## Error Handling Naming

### Exception Classes
- Use descriptive names ending with `Error` or `Exception`
- Include the context in the name

**Examples:**
```python
class PieceValidationError(Exception)
class AbilityLoadError(Exception)
class UIComponentError(Exception)
```

### Error Message Variables
- Use descriptive names that indicate the error context
- Use consistent patterns across similar error handling

**Examples:**
```python
validation_error_msg = "Piece name cannot be empty"
file_load_error = f"Could not load file: {filename}"
ui_update_error = "Failed to update movement pattern display"
```

## Documentation and Comments

### Docstring Conventions
- Use descriptive docstrings for all public classes and methods
- Include parameter and return type information
- Use consistent formatting

**Examples:**
```python
def collect_data_from_ui(self, editor_instance, data_type: str) -> Dict[str, Any]:
    """
    Collect data from UI widgets using standardized interface.
    
    Args:
        editor_instance: The editor instance (PieceEditor or AbilityEditor)
        data_type: Type of data being collected ('piece' or 'ability')
        
    Returns:
        Dictionary containing all collected data
        
    Raises:
        ValueError: If data_type is not supported
        UIError: If UI widgets cannot be accessed
    """
```

### Comment Conventions
- Use descriptive comments for complex logic
- Use consistent formatting and style
- Group related code sections with comments

**Examples:**
```python
# Initialize UI components
self.setup_basic_info_section()
self.setup_movement_section()
self.setup_abilities_section()

# Connect event handlers
self.connect_change_tracking()
self.connect_validation_handlers()

# Load default configuration
self.reset_to_defaults()
```

## Implementation Guidelines

### Consistency Rules
1. **Always use the same naming pattern** for similar functionality across different modules
2. **Maintain consistency** between UI widget names and data dictionary keys
3. **Use descriptive names** that make the code self-documenting
4. **Follow Python PEP 8** conventions as the foundation
5. **Group related functionality** with consistent naming prefixes

### Refactoring Guidelines
1. **Update all related names** when changing a naming pattern
2. **Maintain backward compatibility** in public APIs when possible
3. **Document naming changes** in commit messages and documentation
4. **Use IDE refactoring tools** to ensure all references are updated
5. **Test thoroughly** after naming changes to ensure functionality is preserved

This naming convention guide should be followed for all new code and applied gradually to existing code during refactoring efforts to improve overall codebase consistency and maintainability.
