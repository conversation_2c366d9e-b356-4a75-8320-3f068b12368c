"""
Core Performance Module for Adventure Chess Creator

This module contains performance optimization and lazy loading systems.
Consolidated from the enhancements/performance/ folder for better organization.
"""

# Editor Integration and Cache Management
from .editor_integration import (
    CacheEntry,
    EnhancedCacheManager,
    LazyAbilityEditorPatches,
    LazyPieceEditorPatches,
    apply_lazy_loading_patches,
    create_lazy_file_selector_widget,
    get_cache_manager,
)

# File System Optimization
from .file_system_optimization import (
    FileIndexEntry,
    FileSystemOptimizer,
    OptimizedDataManager,
    SearchResult,
    get_file_system_optimizer,
    get_optimized_data_manager,
    reset_optimized_data_manager,
    reset_optimizer,
)

# Lazy Loading System
from .lazy_loading_system import (
    LazyComboBox,
    LazyDataManager,
    LazyFileListManager,
    LazyFileSelector,
    LazyIntegratedDataManager,
    LazyListWidget,
    LazyLoadingStatusWidget,
    LoadingProgressWidget,
    get_lazy_data_manager,
    get_lazy_manager,
    reset_lazy_manager,
)

__all__ = [
    # Lazy Loading System
    "LazyDataManager",
    "LazyFileListManager",
    "LazyIntegratedDataManager",
    "LazyComboBox",
    "LazyListWidget",
    "LazyLoadingStatusWidget",
    "LazyFileSelector",
    "LoadingProgressWidget",
    "get_lazy_manager",
    "get_lazy_data_manager",
    "reset_lazy_manager",
    # File System Optimization
    "FileSystemOptimizer",
    "FileIndexEntry",
    "SearchResult",
    "OptimizedDataManager",
    "get_file_system_optimizer",
    "get_optimized_data_manager",
    "reset_optimizer",
    "reset_optimized_data_manager",
    # Editor Integration and Cache Management
    "EnhancedCacheManager",
    "CacheEntry",
    "LazyPieceEditorPatches",
    "LazyAbilityEditorPatches",
    "apply_lazy_loading_patches",
    "create_lazy_file_selector_widget",
    "get_cache_manager",
]
