"""
Adjacency Preview Component for Adventure Chess Creator

This module provides enhanced visualization components for adjacency patterns,
offering better visual feedback for adjacency configuration in dialogs and editors.
"""

from typing import List, Set, Tuple

from PyQt6.QtCore import pyqtSignal
from PyQt6.QtWidgets import (
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QVBoxLayout,
    QWidget,
)


class AdjacencyPreviewWidget(QWidget):
    """
    Enhanced preview widget for adjacency patterns with visual chess board representation
    """

    pattern_changed = pyqtSignal(set)  # Emitted when pattern changes

    def __init__(self, parent=None, max_distance=3):
        super().__init__(parent)
        self.max_distance = max_distance
        self.current_distance = 1
        self.selected_pattern = set()  # Set of (row, col) relative positions
        self.center_piece = None  # Piece type at center
        self.required_pieces = []  # List of required piece types
        self.grid_buttons = []
        self.setup_ui()

    def setup_ui(self):
        """Setup the preview UI with enhanced visualization"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # Header with title and info
        header_layout = QHBoxLayout()

        title_label = QLabel("Adjacency Pattern Preview")
        title_label.setStyleSheet(
            """
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #e2e8f0;
                padding: 5px;
            }
        """
        )
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Info label for pattern count
        self.info_label = QLabel("0 tiles selected")
        self.info_label.setStyleSheet(
            """
            QLabel {
                color: #a0aec0;
                font-size: 11px;
                padding: 5px;
            }
        """
        )
        header_layout.addWidget(self.info_label)

        layout.addLayout(header_layout)

        # Chess board preview
        self.create_chess_board()
        layout.addWidget(self.board_widget)

        # Pattern description
        self.description_label = QLabel("Click tiles to select adjacency pattern")
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet(
            """
            QLabel {
                color: #a0aec0;
                font-style: italic;
                padding: 8px;
                background-color: #2d3748;
                border-radius: 4px;
                border: 1px solid #4a5568;
            }
        """
        )
        layout.addWidget(self.description_label)

        self.setLayout(layout)
        self.update_display()

    def create_chess_board(self):
        """Create the chess board grid for pattern visualization"""
        self.board_widget = QGroupBox("Pattern Grid")
        self.board_widget.setStyleSheet(
            """
            QGroupBox {
                color: #e2e8f0;
                border: 2px solid #4a5568;
                border-radius: 8px;
                background-color: #2d3748;
                font-weight: bold;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        )

        board_layout = QVBoxLayout()

        # Grid container
        grid_container = QWidget()
        self.grid_layout = QGridLayout(grid_container)
        self.grid_layout.setSpacing(1)

        # Create grid based on current distance
        self.create_grid()

        board_layout.addWidget(grid_container)
        self.board_widget.setLayout(board_layout)

    def create_grid(self):
        """Create the grid buttons based on current distance"""
        # Clear existing buttons
        for row in self.grid_buttons:
            for btn in row:
                btn.deleteLater()
        self.grid_buttons.clear()

        # Calculate grid size (distance * 2 + 1)
        grid_size = self.current_distance * 2 + 1
        center = self.current_distance

        # Create new buttons
        for r in range(grid_size):
            row = []
            for c in range(grid_size):
                btn = QPushButton()
                btn.setFixedSize(32, 32)
                btn.setCheckable(True)
                btn.clicked.connect(
                    lambda checked, row=r, col=c: self.toggle_tile(row, col, checked)
                )

                # Set initial style
                self.update_tile_style(btn, r, c, center)

                self.grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.grid_buttons.append(row)

    def toggle_tile(self, row: int, col: int, checked: bool):
        """Toggle tile selection"""
        center = self.current_distance
        relative_pos = (row - center, col - center)

        # Don't allow selecting the center tile (where the piece is)
        if relative_pos == (0, 0):
            self.grid_buttons[row][col].setChecked(False)
            return

        if checked:
            self.selected_pattern.add(relative_pos)
        else:
            self.selected_pattern.discard(relative_pos)

        self.update_display()
        self.pattern_changed.emit(self.selected_pattern)

    def update_tile_style(self, btn: QPushButton, row: int, col: int, center: int):
        """Update the style of a grid tile with enhanced fog-of-war appearance"""
        is_light = (row + col) % 2 == 0
        relative_pos = (row - center, col - center)
        is_center = relative_pos == (0, 0)
        is_selected = relative_pos in self.selected_pattern

        if is_center:
            # Center piece (current piece position)
            btn.setStyleSheet(
                """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4CAF50, stop:1 #388E3C);
                    border: 2px solid #2E7D32;
                    border-radius: 4px;
                    font-weight: bold;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #66BB6A, stop:1 #4CAF50);
                }
            """
            )
            btn.setText("♔")
            btn.setToolTip("Current piece position")
        elif is_selected:
            # Selected adjacency tile
            btn.setStyleSheet(
                """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #FF9800, stop:1 #F57C00);
                    border: 2px solid #E65100;
                    border-radius: 4px;
                    font-weight: bold;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #FFB74D, stop:1 #FF9800);
                }
            """
            )
            btn.setText("⚡")
            btn.setToolTip("Required adjacency position")
        else:
            # Regular chess board tiles
            if is_light:
                btn.setStyleSheet(
                    """
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #f0d9b5, stop:1 #e8d1a8);
                        border: 1px solid #b58863;
                        border-radius: 2px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #f5e4c1, stop:1 #f0d9b5);
                        border: 2px solid #8b4513;
                    }
                    QPushButton:checked {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #FF9800, stop:1 #F57C00);
                        border: 2px solid #E65100;
                        color: white;
                    }
                """
                )
            else:
                btn.setStyleSheet(
                    """
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #b58863, stop:1 #a67c52);
                        border: 1px solid #8b4513;
                        border-radius: 2px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #c19975, stop:1 #b58863);
                        border: 2px solid #654321;
                    }
                    QPushButton:checked {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #FF9800, stop:1 #F57C00);
                        border: 2px solid #E65100;
                        color: white;
                    }
                """
                )
            btn.setText("")
            btn.setToolTip("Click to require adjacency here")

    def set_distance(self, distance: int):
        """Set the maximum distance for adjacency"""
        if distance != self.current_distance and 1 <= distance <= self.max_distance:
            self.current_distance = distance
            # Clear pattern that's outside new distance
            self.selected_pattern = {
                pos
                for pos in self.selected_pattern
                if abs(pos[0]) <= distance and abs(pos[1]) <= distance
            }
            self.create_grid()
            self.update_display()

    def set_pattern(self, pattern: Set[Tuple[int, int]]):
        """Set the selected pattern"""
        self.selected_pattern = pattern.copy()
        self.update_display()

    def get_pattern(self) -> Set[Tuple[int, int]]:
        """Get the current selected pattern"""
        return self.selected_pattern.copy()

    def set_required_pieces(self, pieces: List[str]):
        """Set the list of required piece types"""
        self.required_pieces = pieces.copy()
        self.update_display()

    def update_display(self):
        """Update the visual display"""
        # Update info label
        count = len(self.selected_pattern)
        self.info_label.setText(f"{count} tile{'s' if count != 1 else ''} selected")

        # Update description
        if not self.selected_pattern:
            description = (
                "Click tiles around the center piece to define adjacency requirements"
            )
        else:
            # Handle both string and dict formats for required_pieces
            if self.required_pieces:
                piece_names = []
                for piece in self.required_pieces:
                    if isinstance(piece, dict):
                        # Extract name from dict format
                        piece_names.append(piece.get("name", str(piece)))
                    else:
                        # Use string directly
                        piece_names.append(str(piece))
                piece_text = f" for {', '.join(piece_names)}"
            else:
                piece_text = ""
            description = f"Adjacency required at {count} position{'s' if count != 1 else ''}{piece_text}"

        self.description_label.setText(description)

        # Update tile styles
        if self.grid_buttons:
            center = self.current_distance
            for r, row in enumerate(self.grid_buttons):
                for c, btn in enumerate(row):
                    self.update_tile_style(btn, r, c, center)
                    # Update checked state
                    relative_pos = (r - center, c - center)
                    btn.setChecked(
                        relative_pos in self.selected_pattern and relative_pos != (0, 0)
                    )

    def clear_pattern(self):
        """Clear the selected pattern"""
        self.selected_pattern.clear()
        self.update_display()
        self.pattern_changed.emit(self.selected_pattern)
