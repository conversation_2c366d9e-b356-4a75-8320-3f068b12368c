"""
Dialog modules for Adventure Chess

Location: dialogs/

Contains all dialog windows and popup interfaces:
- area_effect_mask_dialog.py: Area effect pattern configuration
- batch_update_dialog.py: Batch file operations
- pattern_editor_dialog.py: Movement pattern editing
- piece_ability_manager.py: Piece-ability relationship management
- range_editor_dialog.py: Range and spatial configuration
- unified_adjacency_dialog.py: Adjacency requirement settings

Used by: Editors for complex configuration tasks requiring dedicated UI
"""
