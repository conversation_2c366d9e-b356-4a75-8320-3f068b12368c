"""
Configuration and Constants for Adventure Chess
Centralized location for all shared constants, paths, and configurations

This module provides:
- Directory paths and file management
- UI constants and responsive design settings
- Game data structures and enums
- Default values for pieces and abilities
"""
from pathlib import Path
from typing import Dict, List, Tuple, Any

# ========== DIRECTORY CONFIGURATION ==========

# Base directory
BASE_DIR = Path(__file__).parent

# Data directories
DATA_DIR = BASE_DIR / 'data'
PIECES_DIR = DATA_DIR / 'pieces'
ABILITIES_DIR = DATA_DIR / 'abilities'
ICONS_DIR = DATA_DIR / 'icons'

# Log directory
LOGS_DIR = BASE_DIR / 'logs'


def ensure_directories() -> None:
    """
    Create all necessary directories if they don't exist.

    This function is called automatically when the module is imported
    to ensure the application has all required directories.
    """
    directories = [DATA_DIR, PIECES_DIR, ABILITIES_DIR, ICONS_DIR, LOGS_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# ========== GAME DATA DEFINITIONS ==========

# Ability tag definitions (centralized)
ABILITY_TAGS: Dict[str, List[Tuple[str, str]]] = {
    'action': [
        ("move", "Teleports piece to target square within range"),
        ("summon", "Creates new pieces at target locations"),
        ("revival", "Resurrects destroyed pieces at target locations"),
        ("capture", "Destroys pieces at target locations"),
        ("carryPiece", "Allows piece to carry other pieces while moving"),
        ("swapPlaces", "Exchanges positions with target piece"),
        ("displacePiece", "Pushes target piece in specified direction"),
        ("immobilize", "Prevents piece movement for specified turns"),
        ("convertPiece", "Changes target pieces into different piece types"),
        ("duplicate", "Creates copies of piece at offset positions"),
        ("buffPiece", "Temporarily enhances target pieces"),
        ("debuffPiece", "Temporarily weakens target pieces"),
        ("addObstacle", "Places obstacles on target squares"),
        ("removeObstacle", "Removes obstacles from target squares"),
        ("trapTile", "Creates hidden traps on target squares")
    ],
    'targeting': [
        ("range", "Defines targeting area for abilities"),
        ("areaEffect", "Affects multiple squares around target")
    ],
    'condition': [
        ("adjacencyRequired", "Ability only works when adjacent to specific pieces"),
        ("losRequired", "Requires clear line of sight to target"),
        ("noTurnCost", "Ability doesn't consume turn points"),
        ("delay", "Ability effect occurs after specified turns")
    ],
    'special': [
        ("shareSpace", "Multiple pieces can occupy same square"),
        ("passThrough", "Can target through other pieces"),
        ("pulseEffect", "Repeating effect that triggers every N turns"),
        ("fogOfWar", "Reveals hidden areas of the board"),
        ("invisible", "Makes piece undetectable under certain conditions"),
        ("reaction", "Triggers automatically in response to events"),
        ("requiresStartingPosition", "Ability only works if piece hasn't moved from starting position")
    ]
}

# Piece role types - Updated per glossary V1.0.1
PIECE_ROLES: List[Tuple[str, str]] = [
    ("Commander", "Your key piece affected by check/checkmate"),
    ("Supporter", "A supporter of your Commander")
]

# ========== DEFAULT DATA STRUCTURES ==========

# Default piece structure (with version)
DEFAULT_PIECE: Dict[str, Any] = {
    "version": "1.0.0",
    "name": "New Piece",
    "description": "",
    "role": "Commander",
    "can_castle": False,
    "movement": {
        "type": "orthogonal",
        "distance": 1
    },
    "range": 1,
    "can_capture": True,
    "color_directional": False,
    "max_points": 0,
    "starting_points": 0,
    "recharge_type": "turnRecharge",
    "turn_points": 1,
    "abilities": [],
    "promotions": [],
    "promotions_2nd": []
}

# Default ability structure (with version)
DEFAULT_ABILITY: Dict[str, Any] = {
    "version": "1.0.0",
    "name": "New Ability",
    "description": "",
    "cost": 0,
    "activation_mode": "auto",
    "tags": []
}

# Movement types
MOVEMENT_TYPES = [
    ("orthogonal", "Move in straight lines (up, down, left, right)"),
    ("diagonal", "Move diagonally"),
    ("lShape", "Move in L-shape like a knight"),
    ("any", "Move in any direction"),
    ("custom", "Custom movement pattern")
]

# Recharge types - Updated per glossary V1.0.1
RECHARGE_TYPES = [
    ("turnRecharge", "Recharges after a number of turns"),
    ("adjacencyRecharge", "Recharges when adjacent to specific pieces"),
    ("committedRecharge", "Requires commitment to recharge")
]

# Activation modes - Updated per glossary V1.0.1
ACTIVATION_MODES = [
    ("auto", "Activates automatically when conditions are met"),
    ("click", "Requires manual activation")
]

# Direction options for displacement and movement
DISPLACEMENT_DIRECTIONS = [
    ("N", "North"),
    ("S", "South"),
    ("E", "East"),
    ("W", "West"),
    ("NE", "Northeast"),
    ("NW", "Northwest"),
    ("SE", "Southeast"),
    ("SW", "Southwest")
]

# Target type options
TARGET_TYPES = [
    ("enemy", "Enemy pieces only"),
    ("ally", "Friendly pieces only"),
    ("any", "Any piece")
]

# Obstacle types
OBSTACLE_TYPES = [
    ("wall", "Blocks all movement"),
    ("spike", "Captures unit on enter"),
    ("crystal", "+1 point per turn if adjacent"),
    ("ice", "Slides to adjacent random tile"),
    ("fire", "Captures unit if not moved after 2 turns"),
    ("portal", "Transfers to random adjacent tile of another portal")
]

# Event types for reaction abilities
REACTION_EVENT_TYPES = [
    ("onEnemyMove", "When any enemy piece moves"),
    ("onAllyMove", "When any friendly piece moves"),
    ("onEnemyCapture", "When an enemy piece is captured"),
    ("onAllyCapture", "When a friendly piece is captured"),
    ("onEnemyDeath", "When any enemy piece dies"),
    ("onAllyDeath", "When any friendly piece dies"),
    ("onPieceEnterRange", "When a piece enters this piece's range"),
    ("onPieceLeaveRange", "When a piece leaves this piece's range"),
    ("onTurnStart", "At the start of each turn"),
    ("onTurnEnd", "At the end of each turn")
]

# Vision types for fog of war
VISION_TYPES = [
    ("sight", "Standard sight vision with radius/custom range"),
    ("lantern", "Lantern-style area vision with duration and cost")
]

# Delay types
DELAY_TYPES = [
    ("turnDelay", "Delay by number of turns"),
    ("actionDelay", "Delay by number of actions")
]

# Buff/Debuff effect types
BUFF_EFFECT_TYPES = [
    ("addAbility", "Add temporary abilities"),
    ("enhanceMovement", "Modify movement pattern"),
    ("enhanceAttack", "Modify attack range/pattern")
]

# Invisibility reveal conditions
INVISIBILITY_REVEAL_CONDITIONS = [
    ("revealOnMove", "Reveal after N moves"),
    ("revealOnCapture", "Reveal after N captures"),
    ("revealOnAction", "Reveal after N total actions"),
    ("revealOnEnemyLoS", "Auto-reveal if seen by enemy")
]

# Trap effects
TRAP_EFFECTS = [
    ("capture", "Destroys target"),
    ("immobilize", "Prevents movement for duration"),
    ("teleport", "Moves target to different location"),
    ("addAbility", "Adds embedded effect to target")
]

# Area effect shapes
AREA_EFFECT_SHAPES = [
    ("Circle", "Circular area effect"),
    ("Square", "Square area effect"),
    ("Cross", "Cross-shaped area effect"),
    ("Line", "Linear area effect"),
    ("Custom", "Custom area pattern")
]

# Capture target types
CAPTURE_TARGET_TYPES = [
    ("Enemy", "Enemy pieces only"),
    ("Friendly", "Friendly pieces only"),
    ("Any", "Any piece")
]

# ========== FILE CONFIGURATION ==========

# File extensions
PIECE_EXTENSION = '.json'
ABILITY_EXTENSION = '.json'

# ========== UI CONSTANTS ==========

# Basic window settings
DEFAULT_WINDOW_SIZE: Tuple[int, int] = (800, 600)
MIN_WINDOW_SIZE: Tuple[int, int] = (700, 500)
GRID_SIZE: int = 8  # 8x8 chess board

# Responsive Design Constants - Updated for better screen compatibility
MAIN_WINDOW_DEFAULT: Tuple[int, int] = (1000, 700)
MAIN_WINDOW_MIN: Tuple[int, int] = (800, 600)

ABILITY_EDITOR_DEFAULT: Tuple[int, int] = (1100, 800)
ABILITY_EDITOR_MIN: Tuple[int, int] = (900, 700)

PIECE_EDITOR_DEFAULT: Tuple[int, int] = (1000, 750)
PIECE_EDITOR_MIN: Tuple[int, int] = (850, 650)

# Small screen fallback sizes (for screens 800x600 and smaller)
MAIN_WINDOW_SMALL_SCREEN: Tuple[int, int] = (720, 540)
ABILITY_EDITOR_SMALL_SCREEN: Tuple[int, int] = (720, 540)
PIECE_EDITOR_SMALL_SCREEN: Tuple[int, int] = (720, 540)

# Layout spacing and margins
LAYOUT_MARGIN: int = 10
LAYOUT_SPACING: int = 5
WIDGET_SPACING: int = 3
GROUP_SPACING: int = 8

# Scroll area policies
SCROLL_POLICY_AUTO: str = "auto"
SCROLL_POLICY_ALWAYS: str = "always"
SCROLL_POLICY_NEVER: str = "never"

# ========== UTILITY FUNCTIONS ==========

def get_canonical_ability_tags() -> List[str]:
    """
    Get all canonical ability tags as a flat list.

    Returns:
        List of all ability tag names from all categories
    """
    all_tags = []
    for tag_category in ABILITY_TAGS.values():
        all_tags.extend([tag[0] for tag in tag_category])
    return all_tags


def get_ability_tags_by_category(category: str) -> List[Tuple[str, str]]:
    """
    Get ability tags for a specific category.

    Args:
        category: The category name ('action', 'targeting', 'condition', 'special')

    Returns:
        List of (tag_name, description) tuples for the category
    """
    return ABILITY_TAGS.get(category, [])


def validate_ability_tag(tag: str) -> bool:
    """
    Validate if a tag is a canonical ability tag.

    Args:
        tag: The tag name to validate

    Returns:
        True if the tag is valid, False otherwise
    """
    return tag in get_canonical_ability_tags()


# ========== MODULE INITIALIZATION ==========

# Initialize directories when module is imported
ensure_directories()