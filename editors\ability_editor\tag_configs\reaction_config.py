"""
Reaction tag configuration for ability editor.
Handles reaction ability configurations with event triggers and target selection.
"""

from PyQt6.QtWidgets import (QForm<PERSON>ayout, QCheckBox, QGroupBox, QWidget, QLabel, QVBoxLayout, QSpinBox)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from core.ui.inline_selection_widgets import InlinePieceSelector


class ReactionConfig(BaseTagConfig):
    """Configuration for reaction tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "reaction")
        # Initialize reaction targets list
        self.reaction_targets = []
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for reaction configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting reaction UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Trigger this ability in response to specific events.")
            layout.addWidget(description)

            # Max targets spinner at top
            max_targets_layout = QFormLayout()
            max_targets_spin = QSpinBox()
            max_targets_spin.setRange(0, 99)
            max_targets_spin.setValue(1)
            max_targets_spin.setSpecialValueText("Unlimited")
            max_targets_spin.setToolTip("Maximum number of targets that can trigger this reaction (0 = unlimited)")
            self.store_widget("reaction_max_targets", max_targets_spin)
            max_targets_layout.addRow("Max Targets:", max_targets_spin)
            layout.addLayout(max_targets_layout)

            # Target piece selection
            target_group = QGroupBox("Reaction Targets")
            target_layout = QVBoxLayout()

            target_info = QLabel("Select which pieces can trigger this reaction:")
            target_info.setStyleSheet("color: #666; font-style: italic;")
            target_layout.addWidget(target_info)

            # Enhanced inline piece selector for reaction targets
            reaction_target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("reaction_target_selector", reaction_target_selector)
            target_layout.addWidget(reaction_target_selector)
            target_group.setLayout(target_layout)
            layout.addWidget(target_group)

            # Event type checkboxes
            events_group = QGroupBox("Trigger Events")
            events_layout = QVBoxLayout()

            events_info = QLabel("Select which events trigger this reaction:")
            events_info.setStyleSheet("color: #666; font-style: italic;")
            events_layout.addWidget(events_info)

            # Create checkboxes for each event type
            reaction_move_check = QCheckBox("Move - When target piece moves")
            self.store_widget("reaction_move_check", reaction_move_check)
            events_layout.addWidget(reaction_move_check)

            reaction_capture_check = QCheckBox("Capture - When target piece captures or is captured")
            self.store_widget("reaction_capture_check", reaction_capture_check)
            events_layout.addWidget(reaction_capture_check)

            reaction_death_check = QCheckBox("Death - When target piece dies")
            self.store_widget("reaction_death_check", reaction_death_check)
            events_layout.addWidget(reaction_death_check)

            reaction_enter_range_check = QCheckBox("Enter Range - When target piece enters this piece's range")
            self.store_widget("reaction_enter_range_check", reaction_enter_range_check)
            events_layout.addWidget(reaction_enter_range_check)

            reaction_exit_range_check = QCheckBox("Exit Range - When target piece exits this piece's range")
            self.store_widget("reaction_exit_range_check", reaction_exit_range_check)
            events_layout.addWidget(reaction_exit_range_check)

            reaction_turn_start_check = QCheckBox("Turn Start - At the start of each turn")
            self.store_widget("reaction_turn_start_check", reaction_turn_start_check)
            events_layout.addWidget(reaction_turn_start_check)

            reaction_turn_end_check = QCheckBox("Turn End - At the end of each turn")
            self.store_widget("reaction_turn_end_check", reaction_turn_end_check)
            events_layout.addWidget(reaction_turn_end_check)

            events_group.setLayout(events_layout)
            layout.addWidget(events_group)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Reaction UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating reaction data")

            # Populate max targets
            max_targets_spin = self.get_widget_by_name("reaction_max_targets")
            if max_targets_spin:
                max_targets = data.get("reactionMaxTargets", 1)
                max_targets_spin.setValue(max_targets)

            # Populate target pieces
            reaction_target_selector = self.get_widget_by_name("reaction_target_selector")
            if reaction_target_selector and "reactionTargets" in data:
                reaction_target_selector.set_pieces(data["reactionTargets"])

            # Populate event checkboxes
            events = data.get("reactionEvents", [])
            
            reaction_move_check = self.get_widget_by_name("reaction_move_check")
            if reaction_move_check:
                reaction_move_check.setChecked("move" in events)

            reaction_capture_check = self.get_widget_by_name("reaction_capture_check")
            if reaction_capture_check:
                reaction_capture_check.setChecked("capture" in events)

            reaction_death_check = self.get_widget_by_name("reaction_death_check")
            if reaction_death_check:
                reaction_death_check.setChecked("death" in events)

            reaction_enter_range_check = self.get_widget_by_name("reaction_enter_range_check")
            if reaction_enter_range_check:
                reaction_enter_range_check.setChecked("enterRange" in events)

            reaction_exit_range_check = self.get_widget_by_name("reaction_exit_range_check")
            if reaction_exit_range_check:
                reaction_exit_range_check.setChecked("exitRange" in events)

            reaction_turn_start_check = self.get_widget_by_name("reaction_turn_start_check")
            if reaction_turn_start_check:
                reaction_turn_start_check.setChecked("turnStart" in events)

            reaction_turn_end_check = self.get_widget_by_name("reaction_turn_end_check")
            if reaction_turn_end_check:
                reaction_turn_end_check.setChecked("turnEnd" in events)

            self.log_debug("Reaction data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting reaction data")
            data = {}

            # Collect max targets
            max_targets_spin = self.get_widget_by_name("reaction_max_targets")
            if max_targets_spin:
                data["reactionMaxTargets"] = max_targets_spin.value()

            # Collect target pieces
            reaction_target_selector = self.get_widget_by_name("reaction_target_selector")
            if reaction_target_selector:
                targets = reaction_target_selector.get_pieces()
                if targets:
                    data["reactionTargets"] = targets

            # Collect event types
            events = []
            
            reaction_move_check = self.get_widget_by_name("reaction_move_check")
            if reaction_move_check and reaction_move_check.isChecked():
                events.append("move")

            reaction_capture_check = self.get_widget_by_name("reaction_capture_check")
            if reaction_capture_check and reaction_capture_check.isChecked():
                events.append("capture")

            reaction_death_check = self.get_widget_by_name("reaction_death_check")
            if reaction_death_check and reaction_death_check.isChecked():
                events.append("death")

            reaction_enter_range_check = self.get_widget_by_name("reaction_enter_range_check")
            if reaction_enter_range_check and reaction_enter_range_check.isChecked():
                events.append("enterRange")

            reaction_exit_range_check = self.get_widget_by_name("reaction_exit_range_check")
            if reaction_exit_range_check and reaction_exit_range_check.isChecked():
                events.append("exitRange")

            reaction_turn_start_check = self.get_widget_by_name("reaction_turn_start_check")
            if reaction_turn_start_check and reaction_turn_start_check.isChecked():
                events.append("turnStart")

            reaction_turn_end_check = self.get_widget_by_name("reaction_turn_end_check")
            if reaction_turn_end_check and reaction_turn_end_check.isChecked():
                events.append("turnEnd")

            if events:
                data["reactionEvents"] = events

            self.log_debug("Reaction data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
