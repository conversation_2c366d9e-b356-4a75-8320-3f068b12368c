#!/usr/bin/env python3
"""
Theme System Demonstration for Adventure Chess Creator

This script demonstrates how to use the standardized theming system
to create consistently styled widgets.
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox, QGroupBox,
    QFormLayout, QTabWidget
)

# Import the theme system (prioritizing manual theming)
from core.ui import (
    ThemeManager,
    apply_theme_to_widget,
    apply_manual_themes,
    create_themed_button,
    create_themed_label,
    create_themed_line_edit,
    create_themed_combo_box,
    create_themed_group_box,
    apply_adventure_chess_theme,
    manual_theme_context
)


class ThemeDemo(QMainWindow):
    """Demonstration of the Adventure Chess theming system"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Adventure Chess - Theme System Demo")
        self.setGeometry(100, 100, 800, 600)
        
        # Apply main window theme
        ThemeManager.apply_window_theme(self)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the demonstration UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # Create tabs to show different approaches
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # Tab 1: Manual theming
        manual_tab = self.create_manual_theming_tab()
        tab_widget.addTab(manual_tab, "Manual Theming")
        
        # Tab 2: Manual theming
        manual_tab = self.create_manual_theming_tab()
        tab_widget.addTab(manual_tab, "Manual Theming (Recommended)")
        
        # Tab 3: Factory functions
        factory_tab = self.create_factory_functions_tab()
        tab_widget.addTab(factory_tab, "Factory Functions")
        
        # Tab 4: Theme showcase
        showcase_tab = self.create_theme_showcase_tab()
        tab_widget.addTab(showcase_tab, "Theme Showcase")
        
        # Apply theme to the tab widget itself
        apply_theme_to_widget(tab_widget, 'tab_widget')
    
    def create_manual_theming_tab(self):
        """Demonstrate manual theme application (recommended approach)"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # Header
        header = QLabel("Manual Theme Application (Recommended)")
        apply_theme_to_widget(header, 'header_label')
        layout.addWidget(header)
        
        # Description
        desc = QLabel("Manual theming provides precise control and consistent results")
        apply_theme_to_widget(desc, 'muted_label')
        layout.addWidget(desc)
        
        # Button examples with explicit manual theming
        button_group = create_themed_group_box("Manual Button Theming")
        button_layout = QHBoxLayout()
        button_group.setLayout(button_layout)
        
        # Create buttons and apply themes manually - this gives us full control
        primary_btn = QPushButton("Save Document")
        apply_theme_to_widget(primary_btn, 'primary_button')
        button_layout.addWidget(primary_btn)
        
        secondary_btn = QPushButton("Load File")
        apply_theme_to_widget(secondary_btn, 'secondary_button')
        button_layout.addWidget(secondary_btn)
        
        success_btn = QPushButton("Create New")
        apply_theme_to_widget(success_btn, 'success_button')
        button_layout.addWidget(success_btn)
        
        danger_btn = QPushButton("Delete Item")
        apply_theme_to_widget(danger_btn, 'danger_button')
        button_layout.addWidget(danger_btn)
        
        layout.addWidget(button_group)
        
        # Input examples with manual theming
        input_group = create_themed_group_box("Manual Input Theming")
        input_layout = QFormLayout()
        input_group.setLayout(input_layout)
        
        # Labels with explicit theming
        name_label = QLabel("Name:")
        apply_theme_to_widget(name_label, 'subheader_label')
        name_input = QLineEdit()
        name_input.setPlaceholderText("Enter your name...")
        apply_theme_to_widget(name_input, 'line_edit')
        
        desc_label = QLabel("Description:")
        apply_theme_to_widget(desc_label, 'subheader_label')
        description_input = QTextEdit()
        description_input.setPlaceholderText("Enter description...")
        description_input.setMaximumHeight(80)
        apply_theme_to_widget(description_input, 'text_edit')
        
        role_label = QLabel("Role:")
        apply_theme_to_widget(role_label, 'subheader_label')
        role_combo = QComboBox()
        role_combo.addItems(["Commander", "Support", "Scout"])
        apply_theme_to_widget(role_combo, 'combo_box')
        
        input_layout.addRow(name_label, name_input)
        input_layout.addRow(desc_label, description_input)
        input_layout.addRow(role_label, role_combo)
        
        layout.addWidget(input_group)
        
        # Context manager example
        context_group = create_themed_group_box("Manual Context Manager")
        context_layout = QVBoxLayout()
        context_group.setLayout(context_layout)
        
        with manual_theme_context(context_group):
            context_btn1 = QPushButton("Context Button 1")
            context_btn1.setObjectName("save_btn")  # Will get primary theme
            context_btn2 = QPushButton("Context Button 2") 
            context_btn2.setObjectName("delete_btn")  # Will get danger theme
            
            context_layout.addWidget(context_btn1)
            context_layout.addWidget(context_btn2)
        
        layout.addWidget(context_group)
        layout.addStretch()
        
        return widget
    
    def create_automatic_theming_tab(self):
        """Demonstrate automatic theme detection"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # Header
        header = QLabel("Automatic Theme Detection")
        layout.addWidget(header)
        
        # Description
        desc = QLabel("These widgets are themed automatically based on their text and type")
        layout.addWidget(desc)
        
        # Create widgets without manual theming
        button_group = QGroupBox("Auto-detected Button Themes")
        button_layout = QHBoxLayout()
        button_group.setLayout(button_layout)
        
        # These will be auto-themed based on text patterns
        save_btn = QPushButton("Save")  # Will become primary
        delete_btn = QPushButton("Delete")  # Will become danger
        new_btn = QPushButton("New")  # Will become success
        load_btn = QPushButton("Load")  # Will become secondary
        
        button_layout.addWidget(save_btn)
        button_layout.addWidget(delete_btn)
        button_layout.addWidget(new_btn)
        button_layout.addWidget(load_btn)
        
        layout.addWidget(button_group)
        
        # Form group
        form_group = QGroupBox("Manual-themed Form")
        form_layout = QFormLayout()
        form_group.setLayout(form_layout)
        
        # Apply manual themes to each widget for better control
        manual_name = QLineEdit()
        manual_name.setPlaceholderText("Manual-themed input...")
        apply_theme_to_widget(manual_name, 'line_edit')
        
        manual_desc = QTextEdit()
        manual_desc.setMaximumHeight(60)
        apply_theme_to_widget(manual_desc, 'text_edit')
        
        manual_combo = QComboBox()
        manual_combo.addItems(["Manual Option 1", "Manual Option 2"])
        apply_theme_to_widget(manual_combo, 'combo_box')
        
        form_layout.addRow("Name:", manual_name)
        form_layout.addRow("Description:", manual_desc)
        form_layout.addRow("Type:", manual_combo)
        
        # Apply theme to the group box itself
        apply_theme_to_widget(form_group, 'group_box')
        layout.addWidget(form_group)
        
        # Apply manual theming using the new utility function
        apply_manual_themes(widget)
        
        layout.addStretch()
        return widget
    
    def create_factory_functions_tab(self):
        """Demonstrate themed widget factories"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # Use themed widget factories
        header = create_themed_label("Themed Widget Factories", 'header_label')
        layout.addWidget(header)
        
        desc = create_themed_label(
            "These widgets are created using factory functions with themes pre-applied",
            'muted_label'
        )
        layout.addWidget(desc)
        
        # Button factories
        button_group = create_themed_group_box("Factory-created Buttons")
        button_layout = QHBoxLayout()
        button_group.setLayout(button_layout)
        
        factory_primary = create_themed_button("💾 Factory Primary", 'primary_button')
        factory_secondary = create_themed_button("📁 Factory Secondary", 'secondary_button')
        factory_success = create_themed_button("✨ Factory Success", 'success_button')
        factory_danger = create_themed_button("⚠️ Factory Danger", 'danger_button')
        
        button_layout.addWidget(factory_primary)
        button_layout.addWidget(factory_secondary)
        button_layout.addWidget(factory_success)
        button_layout.addWidget(factory_danger)
        
        layout.addWidget(button_group)
        
        # Input factories
        input_group = create_themed_group_box("Factory-created Inputs")
        input_layout = QFormLayout()
        input_group.setLayout(input_layout)
        
        factory_name = create_themed_line_edit("Factory-themed input...")
        factory_combo = create_themed_combo_box()
        factory_combo.addItems(["Factory Option 1", "Factory Option 2", "Factory Option 3"])
        
        name_label = create_themed_label("Name:", 'subheader_label')
        type_label = create_themed_label("Type:", 'subheader_label')
        
        input_layout.addRow(name_label, factory_name)
        input_layout.addRow(type_label, factory_combo)
        
        layout.addWidget(input_group)
        layout.addStretch()
        
        return widget
    
    def create_theme_showcase_tab(self):
        """Showcase all available themes"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        header = create_themed_label("Complete Theme Showcase", 'header_label')
        layout.addWidget(header)
        
        # Color showcase
        color_group = create_themed_group_box("Color Palette")
        color_layout = QVBoxLayout()
        color_group.setLayout(color_layout)
        
        colors = ThemeManager.get_colors()
        color_info = create_themed_label(
            f"Current theme: {ThemeManager.get_current_theme().value.title()}\n"
            f"Available colors: {len(colors)} defined colors",
            'body_label'
        )
        color_layout.addWidget(color_info)
        
        layout.addWidget(color_group)
        
        # Typography showcase
        typo_group = create_themed_group_box("Typography Styles")
        typo_layout = QVBoxLayout()
        typo_group.setLayout(typo_layout)
        
        header_demo = create_themed_label("Header Label Style", 'header_label')
        subheader_demo = create_themed_label("Subheader Label Style", 'subheader_label')
        body_demo = create_themed_label("Body Label Style", 'body_label')
        muted_demo = create_themed_label("Muted Label Style", 'muted_label')
        
        typo_layout.addWidget(header_demo)
        typo_layout.addWidget(subheader_demo)
        typo_layout.addWidget(body_demo)
        typo_layout.addWidget(muted_demo)
        
        layout.addWidget(typo_group)
        
        # Adventure Chess specific
        chess_group = create_themed_group_box("Adventure Chess Enhancements")
        chess_layout = QVBoxLayout()
        chess_group.setLayout(chess_layout)
        
        chess_info = create_themed_label(
            "Adventure Chess specific theming includes chess board colors, "
            "piece movement indicators, and game-specific UI elements.",
            'body_label'
        )
        chess_layout.addWidget(chess_info)
        
        # Apply Adventure Chess theming to this group
        apply_adventure_chess_theme(chess_group)
        
        layout.addWidget(chess_group)
        layout.addStretch()
        
        return widget


def main():
    """Run the theme demonstration"""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')  # Use Fusion style for better theming
    
    demo = ThemeDemo()
    demo.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
