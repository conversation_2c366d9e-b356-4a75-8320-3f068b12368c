"""
LosRequired tag configuration for ability editor.
Handles line of sight required ability configurations.
"""

from PyQt6.QtWidgets import (QFormLayout, QCheckBox, QWidget, QLabel, QVBoxLayout)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class LosRequiredConfig(BaseTagConfig):
    """Configuration for losRequired tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "losRequired")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for line of sight required configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting line of sight required UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Requires line of sight to target.")
            layout.addWidget(description)

            # Options layout
            options_layout = QFormLayout()

            # Ignore Friendly checkbox - Updated per glossary V1.0.1
            los_ignore_enemy_check = QCheckBox("Ignore Friendly (no block)")
            los_ignore_enemy_check.setToolTip("Friendly pieces don't block line of sight")
            self.store_widget("los_ignore_enemy", los_ignore_enemy_check)
            options_layout.addRow("Friendly Blocking:", los_ignore_enemy_check)

            # Ignore All checkbox
            los_ignore_all_check = QCheckBox("Ignore All (blocks nothing)")
            los_ignore_all_check.setToolTip("No pieces block line of sight")
            self.store_widget("los_ignore_all", los_ignore_all_check)
            options_layout.addRow("All Blocking:", los_ignore_all_check)

            # Make mutually exclusive
            los_ignore_enemy_check.stateChanged.connect(self._on_los_ignore_changed)
            los_ignore_all_check.stateChanged.connect(self._on_los_ignore_changed)

            layout.addLayout(options_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Line of sight required UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def _on_los_ignore_changed(self):
        """Handle mutual exclusivity of ignore checkboxes."""
        try:
            sender = self.sender()
            los_ignore_enemy_check = self.get_widget_by_name("los_ignore_enemy")
            los_ignore_all_check = self.get_widget_by_name("los_ignore_all")
            
            if sender == los_ignore_enemy_check and los_ignore_enemy_check.isChecked():
                los_ignore_all_check.setChecked(False)
            elif sender == los_ignore_all_check and los_ignore_all_check.isChecked():
                los_ignore_enemy_check.setChecked(False)
                
        except Exception as e:
            self.log_error(f"Error handling LOS ignore change: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating line of sight required data")

            # Populate ignore friendly checkbox
            los_ignore_enemy_check = self.get_widget_by_name("los_ignore_enemy")
            if los_ignore_enemy_check and "losIgnoreFriendly" in data:
                los_ignore_enemy_check.setChecked(data["losIgnoreFriendly"])

            # Populate ignore all checkbox
            los_ignore_all_check = self.get_widget_by_name("los_ignore_all")
            if los_ignore_all_check and "losIgnoreAll" in data:
                los_ignore_all_check.setChecked(data["losIgnoreAll"])

            self.log_debug("Line of sight required data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting line of sight required data")
            data = {}

            # Collect ignore friendly setting
            los_ignore_enemy_check = self.get_widget_by_name("los_ignore_enemy")
            if los_ignore_enemy_check and los_ignore_enemy_check.isChecked():
                data["losIgnoreFriendly"] = True

            # Collect ignore all setting
            los_ignore_all_check = self.get_widget_by_name("los_ignore_all")
            if los_ignore_all_check and los_ignore_all_check.isChecked():
                data["losIgnoreAll"] = True

            self.log_debug("Line of sight required data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
