"""
Auto-Save System for Adventure Chess Creator

This module provides automatic saving functionality for editors with
configurable intervals and backup management.
"""

import json
import logging
from pathlib import Path

from PyQt6.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)


class AutoSaveManager(QObject):
    """Manages automatic saving of editor data"""

    auto_saved = pyqtSignal(str)  # Emitted when auto-save occurs

    def __init__(self, editor, interval_seconds: int = 300):  # 5 minutes default
        super().__init__()
        self.editor = editor
        self.interval_seconds = interval_seconds
        self.timer = QTimer()
        self.timer.timeout.connect(self.auto_save)
        self.enabled = True
        self.last_data_hash = None

    def start(self):
        """Start auto-save timer"""
        if self.enabled:
            self.timer.start(self.interval_seconds * 1000)
            logger.info(f"Auto-save started with {self.interval_seconds}s interval")

    def stop(self):
        """Stop auto-save timer"""
        self.timer.stop()
        logger.info("Auto-save stopped")

    def set_interval(self, seconds: int):
        """Set auto-save interval"""
        self.interval_seconds = seconds
        if self.timer.isActive():
            self.timer.start(self.interval_seconds * 1000)
        logger.info(f"Auto-save interval set to {seconds}s")

    def enable(self):
        """Enable auto-save"""
        self.enabled = True
        logger.info("Auto-save enabled")

    def disable(self):
        """Disable auto-save"""
        self.enabled = False
        self.stop()
        logger.info("Auto-save disabled")

    def auto_save(self):
        """Perform auto-save if data has changed"""
        try:
            if not self.enabled or not hasattr(self.editor, "collect_data"):
                return

            current_data = self.editor.collect_data()
            current_hash = hash(str(current_data))

            # Check if data has changed
            if current_hash != self.last_data_hash:
                backup_path = self._create_backup_path()

                # Ensure backup directory exists
                backup_path.parent.mkdir(parents=True, exist_ok=True)

                # Save backup
                with open(backup_path, "w", encoding="utf-8") as f:
                    json.dump(current_data, f, indent=2)

                self.last_data_hash = current_hash
                self.auto_saved.emit(str(backup_path))
                logger.info(f"Auto-saved to: {backup_path}")

        except Exception as e:
            logger.error(f"Auto-save failed: {e}")

    def _create_backup_path(self) -> Path:
        """Create backup file path"""
        if hasattr(self.editor, "current_filename") and self.editor.current_filename:
            # Create backup next to original file
            original_path = Path(self.editor.current_filename)
            backup_dir = original_path.parent / "backups"
            backup_name = f"{original_path.stem}_autosave{original_path.suffix}"
            return backup_dir / backup_name
        else:
            # Create temp backup
            backup_dir = Path("data") / "temp_backups"
            data_type = getattr(self.editor, "data_type", "unknown")
            return backup_dir / f"autosave_{data_type}.json"

    def force_save(self):
        """Force an immediate auto-save"""
        self.auto_save()

    def cleanup_old_backups(self, keep_count: int = 10):
        """Clean up old backup files, keeping only the most recent ones"""
        try:
            backup_path = self._create_backup_path()
            backup_dir = backup_path.parent

            if not backup_dir.exists():
                return

            # Find all backup files for this editor
            pattern = backup_path.name.replace("_autosave", "_autosave*")
            backup_files = list(backup_dir.glob(pattern))

            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove old backups
            for old_backup in backup_files[keep_count:]:
                old_backup.unlink()
                logger.debug(f"Removed old backup: {old_backup}")

            logger.info(
                f"Cleaned up old backups, kept {min(len(backup_files), keep_count)} files"
            )

        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")

    def get_backup_files(self) -> list:
        """Get list of available backup files"""
        try:
            backup_path = self._create_backup_path()
            backup_dir = backup_path.parent

            if not backup_dir.exists():
                return []

            # Find all backup files for this editor
            pattern = backup_path.name.replace("_autosave", "_autosave*")
            backup_files = list(backup_dir.glob(pattern))

            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            return [str(f) for f in backup_files]

        except Exception as e:
            logger.error(f"Error getting backup files: {e}")
            return []

    def restore_from_backup(self, backup_path: str):
        """Restore editor data from a backup file"""
        try:
            with open(backup_path, "r", encoding="utf-8") as f:
                backup_data = json.load(f)

            if hasattr(self.editor, "load_data_from_dict"):
                self.editor.load_data_from_dict(backup_data)
                logger.info(f"Restored data from backup: {backup_path}")
            else:
                logger.warning("Editor does not support data restoration")

        except Exception as e:
            logger.error(f"Error restoring from backup {backup_path}: {e}")


class BackupManager:
    """Manages backup operations across multiple editors"""

    def __init__(self):
        self.auto_save_managers = {}

    def register_editor(self, editor_id: str, editor, interval_seconds: int = 300):
        """Register an editor for auto-save"""
        auto_save_manager = AutoSaveManager(editor, interval_seconds)
        self.auto_save_managers[editor_id] = auto_save_manager
        auto_save_manager.start()
        logger.info(f"Registered editor for auto-save: {editor_id}")

    def unregister_editor(self, editor_id: str):
        """Unregister an editor from auto-save"""
        if editor_id in self.auto_save_managers:
            self.auto_save_managers[editor_id].stop()
            del self.auto_save_managers[editor_id]
            logger.info(f"Unregistered editor from auto-save: {editor_id}")

    def start_all(self):
        """Start auto-save for all registered editors"""
        for manager in self.auto_save_managers.values():
            manager.start()

    def stop_all(self):
        """Stop auto-save for all registered editors"""
        for manager in self.auto_save_managers.values():
            manager.stop()

    def force_save_all(self):
        """Force immediate save for all registered editors"""
        for manager in self.auto_save_managers.values():
            manager.force_save()

    def cleanup_all_backups(self, keep_count: int = 10):
        """Clean up old backups for all registered editors"""
        for manager in self.auto_save_managers.values():
            manager.cleanup_old_backups(keep_count)

    def get_manager(self, editor_id: str) -> AutoSaveManager:
        """Get auto-save manager for a specific editor"""
        return self.auto_save_managers.get(editor_id)
