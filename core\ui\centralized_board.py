#!/usr/bin/env python3
"""
Enhanced Unified Chess Board Widget for Adventure Chess Creator

This module provides a comprehensive, unified 8x8 chess board component that replaces
all legacy board implementations across the application. It standardizes appearance,
behavior, and data formats for all board-based interactions.

Features:
- Professional dark theme chess board appearance with consistent styling
- Standardized 8x8 array data format for seamless JSON serialization
- Advanced piece position management with visual indicators
- Multi-mode pattern editing (movement, attack, range, area, adjacency)
- Configurable tile states and color schemes
- Preset pattern support (rook, bishop, queen, knight, king, global)
- Interactive controls with hover effects and visual feedback
- Signal-based architecture for component communication
- Performance optimized for responsive UI
- Type hints and comprehensive documentation
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
    QLabel, QCheckBox, QButtonGroup, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class BoardMode:
    """Enumeration of supported board modes"""
    PATTERN = "pattern"          # Movement/attack pattern editing
    RANGE = "range"             # Target range selection
    AREA = "area"               # Area effect configuration
    ADJACENCY = "adjacency"     # Adjacency pattern editing
    PREVIEW = "preview"         # Read-only preview mode
    SELECTION = "selection"     # Multi-select mode


class TileState:
    """Enumeration of tile states for different modes"""
    EMPTY = 0
    MOVE = 1
    ATTACK = 2
    BOTH = 3
    ACTION = 4
    ANY = 5
    SELECTED = 1  # For boolean modes
    BLOCKED = -1


class EnhancedChessBoardWidget(QWidget):
    """
    Enhanced unified 8x8 chess board widget for Adventure Chess Creator.

    This comprehensive widget replaces all legacy board implementations and provides
    a consistent, professional interface for all board-based interactions across
    the application.

    Supported Modes:
    - Pattern: Movement/attack pattern editing with multi-state tiles
    - Range: Target range selection with boolean states
    - Area: Area effect configuration with custom patterns
    - Adjacency: Adjacency pattern editing for piece relationships
    - Preview: Read-only display mode for pattern visualization
    - Selection: Multi-select mode for tile selection

    Features:
    - Professional dark theme chess board styling
    - Configurable tile states and color schemes
    - Interactive preset pattern buttons
    - Real-time visual feedback and hover effects
    - Piece position management with visual indicators
    - Signal-based communication for component integration
    - Performance optimized rendering
    """

    # Signals for component communication
    pattern_changed = pyqtSignal(list)  # Emitted when pattern changes (8x8 array)
    piece_position_changed = pyqtSignal(list)  # Emitted when piece position changes
    tile_clicked = pyqtSignal(int, int, int)  # Emitted on tile click (row, col, state)
    preset_selected = pyqtSignal(str)  # Emitted when preset pattern is selected

    def __init__(self, parent: Optional[QWidget] = None, mode: str = BoardMode.PATTERN,
                 title: str = "Board Editor", show_presets: bool = True,
                 show_controls: bool = True, tile_size: int = 50):
        """
        Initialize the enhanced chess board widget.

        Args:
            parent: Parent widget
            mode: Board mode (see BoardMode class for options)
            title: Title for the board section
            show_presets: Whether to show preset pattern buttons
            show_controls: Whether to show control checkboxes
            tile_size: Size of each tile in pixels
        """
        super().__init__(parent)

        # Configuration
        self.mode = mode
        self.title = title
        self.show_presets = show_presets
        self.show_controls = show_controls
        self.tile_size = tile_size

        # Data storage - standardized 8x8 array format
        self.pattern: List[List[int]] = [[0 for _ in range(8)] for _ in range(8)]
        self.piece_position: List[int] = [3, 3]  # Default center position

        # UI components
        self.grid_buttons: List[List[QPushButton]] = []
        self.preset_buttons: List[QPushButton] = []
        self.preset_button_group: Optional[QButtonGroup] = None
        self.info_label: Optional[QLabel] = None
        self.controls_frame: Optional[QFrame] = None

        # Configuration options
        self.include_starting_square: bool = False
        self.continue_off_board: bool = False
        self.read_only: bool = (mode == BoardMode.PREVIEW)

        # Visual state
        self.current_paint_mode: Optional[int] = None
        self.selected_preset: Optional[str] = None

        # Performance optimization
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self._perform_update)

        # Set minimum size to prevent squishing in dialogs - larger for better chess board feel
        self.setMinimumSize(450, 520)

        self.setup_ui()
        self.update_display()
    
    def setup_ui(self) -> None:
        """Setup the comprehensive user interface"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Title section with enhanced styling
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    font-size: 16px;
                    color: #e2e8f0;
                    padding: 8px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a5568, stop:1 #2d3748);
                    border-radius: 6px;
                    border: 1px solid #718096;
                }
            """)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(title_label)

        # Preset pattern buttons (if enabled)
        if self.show_presets and self.mode in [BoardMode.PATTERN, BoardMode.RANGE]:
            preset_widget = self.create_preset_controls()
            main_layout.addWidget(preset_widget)

        # Main board grid
        board_frame = self.create_board_frame()
        main_layout.addWidget(board_frame)

        # Control checkboxes (if enabled)
        if self.show_controls and self.mode in [BoardMode.PATTERN, BoardMode.RANGE]:
            controls_widget = self.create_control_checkboxes()
            main_layout.addWidget(controls_widget)

        # Paint mode controls for pattern mode
        if self.mode == BoardMode.PATTERN:
            paint_widget = self.create_paint_mode_controls()
            main_layout.addWidget(paint_widget)

        # Info display with enhanced styling
        self.info_label = QLabel("0 tiles selected")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #a0aec0;
                font-style: italic;
                font-size: 12px;
                padding: 4px 8px;
                background: #2d3748;
                border-radius: 4px;
                border: 1px solid #4a5568;
            }
        """)
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(self.info_label)

        self.setLayout(main_layout)
    
    def create_board_frame(self) -> QFrame:
        """Create the main board frame with chess board styling"""
        board_frame = QFrame()
        board_frame.setStyleSheet("""
            QFrame {
                background: #2d1810;
                border: 3px solid #8b4513;
                border-radius: 6px;
                padding: 6px;
            }
        """)

        frame_layout = QVBoxLayout()
        frame_layout.setContentsMargins(8, 8, 8, 8)

        # Create the actual board grid
        self.board_widget = self.create_board_grid()
        frame_layout.addWidget(self.board_widget)

        board_frame.setLayout(frame_layout)
        return board_frame

    def create_board_grid(self) -> QWidget:
        """Create the enhanced 8x8 chess board grid"""
        board_widget = QWidget()
        grid_layout = QGridLayout()
        grid_layout.setSpacing(0)  # No spacing between tiles for seamless chess board
        grid_layout.setContentsMargins(2, 2, 2, 2)  # Minimal border around the board

        # Create grid buttons with enhanced functionality
        self.grid_buttons = []
        for r in range(8):
            row = []
            for c in range(8):
                btn = QPushButton()
                btn.setFixedSize(self.tile_size, self.tile_size)
                btn.setCheckable(True)

                # Connect signals with proper lambda capture
                btn.clicked.connect(
                    lambda checked=False, row=r, col=c: self.toggle_tile(row, col)
                )

                # Right-click for piece position (if not read-only)
                if not self.read_only:
                    btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                    btn.customContextMenuRequested.connect(
                        lambda pos=None, row=r, col=c: self.move_piece_position(row, col)
                    )

                # Apply professional chess board styling
                self.apply_enhanced_tile_style(btn, r, c)

                grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.grid_buttons.append(row)

        board_widget.setLayout(grid_layout)
        return board_widget
    
    def apply_enhanced_tile_style(self, button: QPushButton, row: int, col: int) -> None:
        """Apply professional chess board styling to a tile with enhanced visual feedback"""
        is_light = (row + col) % 2 == 0
        is_piece_pos = [row, col] == self.piece_position
        tile_state = self.pattern[row][col]

        # Classic chess board colors
        if is_light:
            base_color = "#f0d9b5"  # Light squares (cream/beige)
            border_color = "#d4c4a8"
            hover_color = "#f5e6d3"
            text_color = "#8b4513"  # Dark brown text on light squares
        else:
            base_color = "#b58863"  # Dark squares (brown)
            border_color = "#a0784d"
            hover_color = "#c49a7a"
            text_color = "#f0d9b5"  # Light text on dark squares

        # Get state-specific overlay colors
        state_overlay = self.get_state_overlay_colors(tile_state, is_light)

        # Final background color (blend base with state overlay if needed)
        if state_overlay['background']:
            final_bg = state_overlay['background']
            final_text = state_overlay['text']
        else:
            final_bg = base_color
            final_text = text_color

        # Piece position indicator
        piece_indicator = ""
        if is_piece_pos:
            piece_indicator = "♔"  # King symbol for piece position

        # Create comprehensive stylesheet with chess board feel
        stylesheet = f"""
            QPushButton {{
                background: {final_bg};
                border: 1px solid {border_color};
                border-radius: 0px;
                font-size: 18px;
                font-weight: bold;
                color: {final_text};
                margin: 0px;
                padding: 0px;
            }}
            QPushButton:hover {{
                background: {hover_color};
                border: 2px solid #4a90e2;
                border-radius: 0px;
            }}
            QPushButton:pressed {{
                background: {state_overlay.get('pressed', '#94a3b8')};
                border: 2px solid #2c5aa0;
            }}
            QPushButton:disabled {{
                background: #718096;
                color: #a0aec0;
                border-color: #4a5568;
            }}
        """

        button.setStyleSheet(stylesheet)
        button.setText(piece_indicator)
        button.setEnabled(not self.read_only)

    def get_state_overlay_colors(self, state: int, is_light_square: bool) -> dict:
        """Get overlay colors for a tile state that blend well with chess board"""
        if state == TileState.EMPTY:
            return {'background': None, 'text': None, 'pressed': '#94a3b8'}

        # For movement states, use colors that work on both light and dark squares
        if state == TileState.MOVE:
            # Green for movement - darker on light squares, lighter on dark squares
            bg_color = "#2d7d32" if is_light_square else "#4caf50"
            return {'background': bg_color, 'text': 'white', 'pressed': '#1b5e20'}
        elif state == TileState.ATTACK:
            # Red for attack
            bg_color = "#c62828" if is_light_square else "#f44336"
            return {'background': bg_color, 'text': 'white', 'pressed': '#b71c1c'}
        elif state == TileState.BOTH:
            # Orange for both move and attack
            bg_color = "#ef6c00" if is_light_square else "#ff9800"
            return {'background': bg_color, 'text': 'white', 'pressed': '#e65100'}
        elif state == TileState.ACTION:
            # Purple for special actions
            bg_color = "#6a1b9a" if is_light_square else "#9c27b0"
            return {'background': bg_color, 'text': 'white', 'pressed': '#4a148c'}
        elif state == TileState.ANY:
            # Blue for any/general
            bg_color = "#1565c0" if is_light_square else "#2196f3"
            return {'background': bg_color, 'text': 'white', 'pressed': '#0d47a1'}
        else:
            return {'background': None, 'text': None, 'pressed': '#94a3b8'}

    def get_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for different tile states based on current mode"""
        if self.mode == BoardMode.PATTERN:
            return self.get_pattern_state_colors(state)
        elif self.mode in [BoardMode.RANGE, BoardMode.AREA, BoardMode.ADJACENCY]:
            return self.get_boolean_state_colors(state)
        else:
            return {'background': None, 'text': '#2d3748', 'pressed': '#94a3b8'}

    def get_pattern_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for pattern mode states (movement/attack patterns)"""
        color_map = {
            TileState.EMPTY: {'background': None, 'text': '#2d3748'},
            TileState.MOVE: {'background': '#48bb78', 'text': 'white'},      # Green for movement
            TileState.ATTACK: {'background': '#f56565', 'text': 'white'},    # Red for attack
            TileState.BOTH: {'background': '#ed8936', 'text': 'white'},      # Orange for both
            TileState.ACTION: {'background': '#9f7aea', 'text': 'white'},    # Purple for action
            TileState.ANY: {'background': '#4299e1', 'text': 'white'},       # Blue for any
        }
        return color_map.get(state, {'background': None, 'text': '#2d3748'})

    def get_boolean_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for boolean mode states (range/area/adjacency)"""
        if state > 0:
            return {'background': '#4299e1', 'text': 'white', 'pressed': '#3182ce'}  # Blue for selected
        else:
            return {'background': None, 'text': '#2d3748', 'pressed': '#94a3b8'}

    def create_preset_controls(self) -> QFrame:
        """Create preset pattern control buttons"""
        preset_frame = QFrame()
        preset_frame.setStyleSheet("""
            QFrame {
                background: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(8, 8, 8, 8)

        # Title
        title_label = QLabel("Quick Patterns")
        title_label.setStyleSheet("""
            QLabel {
                color: #e2e8f0;
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)

        # Preset buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(6)

        # Define preset patterns with enhanced tooltips
        presets = [
            ("♜", "rook", "Orthogonal lines (Rook movement)"),
            ("♝", "bishop", "Diagonal lines (Bishop movement)"),
            ("♛", "queen", "All directions (Queen movement)"),
            ("♞", "knight", "L-shaped moves (Knight movement)"),
            ("♚", "king", "Adjacent squares (King movement)"),
            ("🌐", "global", "Entire board range")
        ]

        # Create button group for exclusive selection
        self.preset_button_group = QButtonGroup()
        self.preset_button_group.setExclusive(True)

        for symbol, preset_type, tooltip in presets:
            btn = QPushButton(symbol)
            btn.setFixedSize(40, 35)
            btn.setCheckable(True)
            btn.setToolTip(f"{tooltip}\nClick to apply this pattern")

            # Enhanced button styling
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 18px;
                    font-weight: bold;
                    border: 2px solid #718096;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a5568, stop:1 #2d3748);
                    color: #e2e8f0;
                    padding: 4px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #5a6578, stop:1 #3d4758);
                    border-color: #66aaff;
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4299e1, stop:1 #3182ce);
                    border-color: #66aaff;
                    border-width: 3px;
                }
            """)

            btn.clicked.connect(lambda checked=False, p=preset_type: self.apply_preset_pattern(p))
            self.preset_button_group.addButton(btn)
            self.preset_buttons.append(btn)
            button_layout.addWidget(btn)

        # Clear button
        clear_btn = QPushButton("Clear")
        clear_btn.setFixedSize(50, 35)
        clear_btn.setToolTip("Clear all pattern selections")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #e53e3e;
                color: white;
                font-weight: bold;
                border: 2px solid #c53030;
                border-radius: 6px;
                padding: 4px;
            }
            QPushButton:hover {
                background: #f56565;
                border-color: #e53e3e;
            }
            QPushButton:pressed {
                background: #c53030;
            }
        """)
        clear_btn.clicked.connect(self.clear_pattern)
        button_layout.addWidget(clear_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)
        preset_frame.setLayout(layout)

        return preset_frame

    def create_control_checkboxes(self) -> QFrame:
        """Create control checkboxes for pattern/range configuration"""
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                background: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        layout = QHBoxLayout()
        layout.setContentsMargins(8, 8, 8, 8)

        # Starting square checkbox
        self.starting_square_check = QCheckBox("Include starting square")
        self.starting_square_check.setChecked(self.include_starting_square)
        self.starting_square_check.toggled.connect(self.on_starting_square_changed)
        self.starting_square_check.setStyleSheet("""
            QCheckBox {
                color: #e2e8f0;
                font-size: 12px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #718096;
                border-radius: 3px;
                background: #4a5568;
            }
            QCheckBox::indicator:checked {
                background: #48bb78;
                border-color: #38a169;
            }
        """)
        layout.addWidget(self.starting_square_check)

        # Continue off board checkbox
        self.continue_off_board_check = QCheckBox("Continue off board")
        self.continue_off_board_check.setChecked(self.continue_off_board)
        self.continue_off_board_check.toggled.connect(self.on_continue_off_board_changed)
        self.continue_off_board_check.setStyleSheet(self.starting_square_check.styleSheet())
        layout.addWidget(self.continue_off_board_check)

        layout.addStretch()
        controls_frame.setLayout(layout)
        return controls_frame

    def create_paint_mode_controls(self) -> QFrame:
        """Create paint mode controls for pattern editing"""
        paint_frame = QFrame()
        paint_frame.setStyleSheet("""
            QFrame {
                background: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(8, 8, 8, 8)

        # Title
        title_label = QLabel("Paint Mode")
        title_label.setStyleSheet("""
            QLabel {
                color: #e2e8f0;
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)

        # Paint mode buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(4)

        paint_modes = [
            ("Move", TileState.MOVE, "#48bb78"),
            ("Attack", TileState.ATTACK, "#f56565"),
            ("Both", TileState.BOTH, "#ed8936"),
            ("Action", TileState.ACTION, "#9f7aea"),
            ("Any", TileState.ANY, "#4299e1"),
            ("Clear", TileState.EMPTY, "#718096")
        ]

        self.paint_button_group = QButtonGroup()
        self.paint_button_group.setExclusive(True)

        for label, mode, color in paint_modes:
            btn = QPushButton(label)
            btn.setFixedSize(50, 30)
            btn.setCheckable(True)
            btn.setToolTip(f"Paint {label.lower()} tiles")

            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    color: white;
                    font-weight: bold;
                    font-size: 11px;
                    border: 2px solid {color};
                    border-radius: 4px;
                    padding: 2px;
                }}
                QPushButton:hover {{
                    border-color: #66aaff;
                }}
                QPushButton:checked {{
                    border-color: #66aaff;
                    border-width: 3px;
                }}
            """)

            btn.clicked.connect(lambda checked=False, m=mode: self.set_paint_mode(m))
            self.paint_button_group.addButton(btn)
            button_layout.addWidget(btn)

        # Normal mode button
        normal_btn = QPushButton("Normal")
        normal_btn.setFixedSize(50, 30)
        normal_btn.setCheckable(True)
        normal_btn.setChecked(True)  # Default mode
        normal_btn.setToolTip("Normal click-to-cycle mode")
        normal_btn.setStyleSheet("""
            QPushButton {
                background: #4a5568;
                color: #e2e8f0;
                font-weight: bold;
                font-size: 11px;
                border: 2px solid #718096;
                border-radius: 4px;
                padding: 2px;
            }
            QPushButton:hover {
                border-color: #66aaff;
            }
            QPushButton:checked {
                border-color: #66aaff;
                border-width: 3px;
            }
        """)
        normal_btn.clicked.connect(lambda checked=False: self.set_paint_mode(None))
        self.paint_button_group.addButton(normal_btn)
        button_layout.addWidget(normal_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)
        paint_frame.setLayout(layout)

        return paint_frame

    # Core Functionality Methods

    def toggle_tile(self, row: int, col: int) -> None:
        """Toggle a tile state based on current mode and paint settings"""
        if self.read_only or [row, col] == self.piece_position:
            return

        if self.current_paint_mode is not None:
            # Paint mode: set tile to specific state
            self.pattern[row][col] = self.current_paint_mode
        else:
            # Normal mode: cycle through states based on mode
            if self.mode == BoardMode.PATTERN:
                # Cycle through: empty -> move -> attack -> both -> action -> any -> empty
                self.pattern[row][col] = (self.pattern[row][col] + 1) % 6
            else:
                # Boolean modes: toggle between 0 and 1
                self.pattern[row][col] = 1 - self.pattern[row][col]

        self.update_tile_display(row, col)
        self.update_info_display()
        self.pattern_changed.emit(self.pattern)

    def move_piece_position(self, row: int, col: int) -> None:
        """Move the piece position to a new location"""
        if self.read_only:
            return

        old_pos = self.piece_position[:]
        self.piece_position = [row, col]

        # Update display for old and new positions
        if 0 <= old_pos[0] < 8 and 0 <= old_pos[1] < 8:
            self.update_tile_display(old_pos[0], old_pos[1])
        self.update_tile_display(row, col)

        self.piece_position_changed.emit(self.piece_position)

    def set_paint_mode(self, mode: Optional[int]) -> None:
        """Set the current paint mode for tile editing"""
        self.current_paint_mode = mode

        # Update info display to show current mode
        if mode is None:
            mode_text = "Normal (click to cycle)"
        else:
            mode_names = {
                TileState.EMPTY: "Clear",
                TileState.MOVE: "Move",
                TileState.ATTACK: "Attack",
                TileState.BOTH: "Both",
                TileState.ACTION: "Action",
                TileState.ANY: "Any"
            }
            mode_text = f"Paint: {mode_names.get(mode, 'Unknown')}"

        # Update info label if it exists
        if hasattr(self, 'info_label') and self.info_label:
            current_text = self.info_label.text()
            if " | " in current_text:
                tile_info = current_text.split(" | ")[0]
                self.info_label.setText(f"{tile_info} | Mode: {mode_text}")
            else:
                self.info_label.setText(f"{current_text} | Mode: {mode_text}")

    def apply_preset_pattern(self, preset_type: str) -> None:
        """Apply a preset pattern to the board"""
        if self.read_only:
            return

        self.clear_pattern()
        self.selected_preset = preset_type

        piece_r, piece_c = self.piece_position

        if preset_type == "rook":
            # Rook: orthogonal lines
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        elif preset_type == "bishop":
            # Bishop: diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c and abs(r - piece_r) == abs(c - piece_c):
                        self.pattern[r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        elif preset_type == "queen":
            # Queen: combine rook and bishop
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c and abs(r - piece_r) == abs(c - piece_c):
                        self.pattern[r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2), (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        elif preset_type == "king":
            # King: adjacent squares
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        self.pattern[r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        elif preset_type == "global":
            # Global: entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.piece_position:
                        self.pattern[r][c] = TileState.BOTH if self.mode == BoardMode.PATTERN else 1

        self.update_display()
        self.pattern_changed.emit(self.pattern)
        self.preset_selected.emit(preset_type)

    def clear_pattern(self) -> None:
        """Clear all pattern selections"""
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.selected_preset = None

        # Clear preset button selections
        if self.preset_button_group:
            self.preset_button_group.setExclusive(False)
            for btn in self.preset_button_group.buttons():
                btn.setChecked(False)
            self.preset_button_group.setExclusive(True)

        self.update_display()
        self.pattern_changed.emit(self.pattern)

    # Display Update Methods

    def update_display(self) -> None:
        """Update the entire board display"""
        if not self.grid_buttons:
            return

        for r in range(8):
            for c in range(8):
                self.update_tile_display(r, c)

        self.update_info_display()

    def update_tile_display(self, row: int, col: int) -> None:
        """Update the display for a specific tile"""
        if not self.grid_buttons or row < 0 or row >= 8 or col < 0 or col >= 8:
            return

        button = self.grid_buttons[row][col]
        self.apply_enhanced_tile_style(button, row, col)

    def update_info_display(self) -> None:
        """Update the information display"""
        if not self.info_label:
            return

        # Count selected tiles
        selected_count = sum(1 for r in range(8) for c in range(8) if self.pattern[r][c] > 0)

        # Create info text
        if selected_count == 0:
            info_text = "No tiles selected"
        elif selected_count == 1:
            info_text = "1 tile selected"
        else:
            info_text = f"{selected_count} tiles selected"

        # Add mode information if in paint mode
        if self.current_paint_mode is not None:
            mode_names = {
                TileState.EMPTY: "Clear",
                TileState.MOVE: "Move",
                TileState.ATTACK: "Attack",
                TileState.BOTH: "Both",
                TileState.ACTION: "Action",
                TileState.ANY: "Any"
            }
            mode_text = mode_names.get(self.current_paint_mode, "Unknown")
            info_text += f" | Paint Mode: {mode_text}"

        self.info_label.setText(info_text)

    def _perform_update(self) -> None:
        """Perform deferred update for performance optimization"""
        self.update_display()

    # Event Handlers

    def on_starting_square_changed(self, checked: bool) -> None:
        """Handle starting square checkbox change"""
        self.include_starting_square = checked
        # Optionally update pattern based on this setting

    def on_continue_off_board_changed(self, checked: bool) -> None:
        """Handle continue off board checkbox change"""
        self.continue_off_board = checked
        # Optionally update pattern based on this setting

    # Data Management Methods

    def set_pattern_from_array(self, pattern_array: List[List[int]]) -> None:
        """Set the pattern from an 8x8 array"""
        if not pattern_array or len(pattern_array) != 8:
            logger.warning("Invalid pattern array provided")
            return

        for r in range(8):
            if len(pattern_array[r]) != 8:
                logger.warning(f"Invalid pattern array row {r}")
                return

        self.pattern = [row[:] for row in pattern_array]  # Deep copy
        self.update_display()
        self.pattern_changed.emit(self.pattern)

    def get_pattern_as_array(self) -> List[List[int]]:
        """Get the current pattern as an 8x8 array"""
        return [row[:] for row in self.pattern]  # Deep copy

    def set_piece_position(self, row: int, col: int) -> None:
        """Set the piece position"""
        if 0 <= row < 8 and 0 <= col < 8:
            old_pos = self.piece_position[:]
            self.piece_position = [row, col]

            # Update display for old and new positions
            if 0 <= old_pos[0] < 8 and 0 <= old_pos[1] < 8:
                self.update_tile_display(old_pos[0], old_pos[1])
            self.update_tile_display(row, col)

            self.piece_position_changed.emit(self.piece_position)

    def get_piece_position(self) -> List[int]:
        """Get the current piece position"""
        return self.piece_position[:]

    def set_mode(self, mode: str) -> None:
        """Change the board mode"""
        if mode in [BoardMode.PATTERN, BoardMode.RANGE, BoardMode.AREA,
                   BoardMode.ADJACENCY, BoardMode.PREVIEW, BoardMode.SELECTION]:
            self.mode = mode
            self.read_only = (mode == BoardMode.PREVIEW)

            # Update UI based on new mode
            for r in range(8):
                for c in range(8):
                    if self.grid_buttons:
                        self.grid_buttons[r][c].setEnabled(not self.read_only)

            self.update_display()

    # Legacy Compatibility Methods (for backward compatibility)

    def get_checkbox_states(self) -> Dict[str, bool]:
        """Get checkbox states for backward compatibility"""
        return {
            'include_starting_square': self.include_starting_square,
            'continue_off_board': self.continue_off_board
        }

    def set_checkbox_states(self, states: Dict[str, bool]) -> None:
        """Set checkbox states for backward compatibility"""
        if 'include_starting_square' in states:
            self.include_starting_square = states['include_starting_square']
            if hasattr(self, 'starting_square_check'):
                self.starting_square_check.setChecked(self.include_starting_square)

        if 'continue_off_board' in states:
            self.continue_off_board = states['continue_off_board']
            if hasattr(self, 'continue_off_board_check'):
                self.continue_off_board_check.setChecked(self.continue_off_board)


# Note: CentralizedBoardWidget has been replaced by EnhancedChessBoardWidget
# All imports should use EnhancedChessBoardWidget directly
