"""
Batch Update System for Adventure Chess
Updates all files to latest structure while preserving data
Handles outdated tags with error reporting and cleanup
"""

import os
import json
import logging
from typing import Dict, List, Tuple, Any
from pathlib import Path

from schemas import (
    pydantic_data_manager, DataMigrationManager, 
    CompatibilityLayer, Ability, Piece
)
from config import AB<PERSON><PERSON>IES_DIR, PIECES_DIR, ABILITY_TAGS

logger = logging.getLogger(__name__)


class BatchUpdateManager:
    """Manages batch updates of all Adventure Chess files"""
    
    def __init__(self):
        self.update_results = {
            'abilities_updated': 0,
            'pieces_updated': 0,
            'abilities_errors': [],
            'pieces_errors': [],
            'outdated_tags_found': [],
            'outdated_tags_removed': []
        }
    
    def update_all_files(self, remove_outdated_tags: bool = False) -> Dict[str, Any]:
        """
        Update all ability and piece files to latest structure
        
        Args:
            remove_outdated_tags: If True, remove outdated tags on second save
            
        Returns:
            Dictionary with update results
        """
        logger.info("Starting batch update of all files...")
        
        # Reset results
        self.update_results = {
            'abilities_updated': 0,
            'pieces_updated': 0,
            'abilities_errors': [],
            'pieces_errors': [],
            'outdated_tags_found': [],
            'outdated_tags_removed': []
        }
        
        # Update abilities
        self._update_abilities(remove_outdated_tags)
        
        # Update pieces
        self._update_pieces(remove_outdated_tags)
        
        logger.info(f"Batch update complete: {self.update_results}")
        return self.update_results
    
    def _update_abilities(self, remove_outdated_tags: bool):
        """Update all ability files"""
        abilities_dir = Path(ABILITIES_DIR)
        if not abilities_dir.exists():
            logger.warning(f"Abilities directory not found: {ABILITIES_DIR}")
            return
        
        for file_path in abilities_dir.glob("*.json"):
            try:
                self._update_ability_file(file_path, remove_outdated_tags)
                self.update_results['abilities_updated'] += 1
            except Exception as e:
                error_msg = f"Failed to update {file_path.name}: {str(e)}"
                self.update_results['abilities_errors'].append(error_msg)
                logger.error(error_msg)
    
    def _update_pieces(self, remove_outdated_tags: bool):
        """Update all piece files"""
        pieces_dir = Path(PIECES_DIR)
        if not pieces_dir.exists():
            logger.warning(f"Pieces directory not found: {PIECES_DIR}")
            return
        
        for file_path in pieces_dir.glob("*.json"):
            try:
                self._update_piece_file(file_path, remove_outdated_tags)
                self.update_results['pieces_updated'] += 1
            except Exception as e:
                error_msg = f"Failed to update {file_path.name}: {str(e)}"
                self.update_results['pieces_errors'].append(error_msg)
                logger.error(error_msg)
    
    def _update_ability_file(self, file_path: Path, remove_outdated_tags: bool):
        """Update a single ability file"""
        # Load original data
        with open(file_path, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # Check for outdated tags
        outdated_tags = self._find_outdated_ability_tags(original_data)
        if outdated_tags:
            if remove_outdated_tags:
                # Remove outdated tags
                self._remove_outdated_tags(original_data, outdated_tags)
                self.update_results['outdated_tags_removed'].extend(
                    [f"{file_path.name}: {tag}" for tag in outdated_tags]
                )
            else:
                # Add to error list and move to bottom
                self._move_outdated_tags_to_bottom(original_data, outdated_tags)
                self.update_results['outdated_tags_found'].extend(
                    [f"{file_path.name}: {tag}" for tag in outdated_tags]
                )
        
        # Migrate to latest structure
        ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(original_data)
        if not ability:
            raise ValueError("Failed to migrate ability data")
        
        # Convert back to latest format
        updated_data = ability.to_legacy_dict()
        
        # Add outdated tag errors if not removing them
        if outdated_tags and not remove_outdated_tags:
            updated_data['_outdated_tags_error'] = {
                'message': 'Outdated tags found - save again to remove them',
                'tags': outdated_tags
            }
        
        # Save updated file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(updated_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Updated ability: {file_path.name}")
    
    def _update_piece_file(self, file_path: Path, remove_outdated_tags: bool):
        """Update a single piece file"""
        # Load original data
        with open(file_path, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # Migrate to latest structure
        piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(original_data)
        if not piece:
            raise ValueError("Failed to migrate piece data")
        
        # Convert back to latest format
        updated_data = piece.to_legacy_dict()
        
        # Save updated file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(updated_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Updated piece: {file_path.name}")
    
    def _find_outdated_ability_tags(self, data: Dict[str, Any]) -> List[str]:
        """Find outdated ability tags in data"""
        tags = data.get('tags', [])
        if not isinstance(tags, list):
            return []
        
        # Get current valid tags from config
        valid_tags = set()
        for category in ABILITY_TAGS.values():
            for tag, _ in category:
                valid_tags.add(tag)
        
        # Find outdated tags
        outdated = []
        for tag in tags:
            if tag not in valid_tags:
                outdated.append(tag)
        
        return outdated
    
    def _remove_outdated_tags(self, data: Dict[str, Any], outdated_tags: List[str]):
        """Remove outdated tags from data"""
        tags = data.get('tags', [])
        if isinstance(tags, list):
            data['tags'] = [tag for tag in tags if tag not in outdated_tags]
        
        # Remove any error markers
        if '_outdated_tags_error' in data:
            del data['_outdated_tags_error']
    
    def _move_outdated_tags_to_bottom(self, data: Dict[str, Any], outdated_tags: List[str]):
        """Move outdated tags to bottom of file as error marker"""
        # This will be handled by adding the error marker in the main update method
        pass


# Global instance
batch_update_manager = BatchUpdateManager()
