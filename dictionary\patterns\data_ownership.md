# Data Ownership Patterns

## Overview

Data ownership patterns define clear rules about which component is responsible for storing, managing, and providing access to each type of data in Adventure Chess Creator. These patterns eliminate confusion and ensure consistent data handling across the entire application.

## Core Ownership Principles

### 1. Single Responsibility
Each data type has exactly one component responsible for its storage and management:
- **Piece Configuration**: `PieceEditor` owns all piece-related data
- **Ability Configuration**: `AbilityEditor` owns all ability-related data
- **Movement Patterns**: `PieceEditor` owns movement data as part of piece configuration
- **UI State**: Each editor owns its own UI state

### 2. Clear Boundaries
Data ownership boundaries are clearly defined and documented:
- **Editor Level**: High-level configuration data (piece, ability)
- **Manager Level**: Specialized data handling (tags, movement patterns)
- **Interface Level**: Cross-cutting concerns (validation, serialization)

### 3. Access Control
Data access is controlled through well-defined patterns:
- **Direct Access**: Simple data accessed directly through properties
- **Property Redirection**: Legacy fields redirect to canonical sources
- **Manager Mediation**: Complex data accessed through specialized managers
- **Interface Mediation**: Standardized data accessed through unified interfaces

## Ownership Registry

The `DataOwnershipRegistry` serves as the authoritative source for ownership rules:

```python
# Example ownership rule registration
registry.register_ownership_rule(DataOwnershipRule(
    data_type="piece_data",
    owner_component="PieceEditor",
    owner_property="current_data",
    access_pattern=DataAccessPattern.DIRECT_PROPERTY,
    description="All piece configuration data",
    legacy_aliases=["current_piece"],
    validation_rules=["Must have name", "Must have valid role"]
))
```

## Access Patterns

### 1. Direct Property Access

**When to Use**: Simple data with straightforward ownership
**Pattern**: Direct access to object properties
**Example**: Basic piece/ability configuration data

```python
# Reading data
piece_name = editor.current_data.get('name')
ability_cost = editor.current_data.get('cost')

# Writing data
editor.current_data['name'] = 'New Name'
editor.current_data['cost'] = 5
```

**Characteristics**:
- Fastest access method
- Clear ownership (data lives in the object)
- Simple to understand and debug
- Best for frequently accessed data

### 2. Computed Property Access

**When to Use**: Legacy compatibility or derived data
**Pattern**: Properties that compute values from canonical sources
**Example**: Movement data fields that redirect to consolidated storage

```python
# Property implementation
@property
def current_custom_pattern(self):
    """Get pattern from consolidated movement data"""
    return self.current_movement_data.get("pattern", default_pattern)

@current_custom_pattern.setter
def current_custom_pattern(self, value):
    """Set pattern in consolidated movement data"""
    self.current_movement_data["pattern"] = value

# Usage (appears like direct access)
pattern = editor.current_custom_pattern
editor.current_custom_pattern = new_pattern
```

**Characteristics**:
- Maintains backward compatibility
- Redirects to canonical source
- Can perform validation or transformation
- Transparent to calling code

### 3. Manager-Mediated Access

**When to Use**: Complex data requiring specialized handling
**Pattern**: Access through dedicated manager classes
**Example**: Tag data in abilities, complex movement patterns

```python
# Manager-based access
tag_data = editor.tag_manager.collect_tag_data()
editor.tag_manager.populate_tag_data(data)
editor.tag_manager.validate_tag_configuration()

# Manager handles complexity internally
class AbilityTagManager:
    def collect_tag_data(self):
        # Complex logic to gather tag-specific data
        pass
    
    def populate_tag_data(self, data):
        # Complex logic to populate tag configurations
        pass
```

**Characteristics**:
- Encapsulates complex logic
- Provides specialized operations
- Can maintain internal state
- Clear separation of concerns

### 4. Interface-Mediated Access

**When to Use**: Standardized operations across multiple components
**Pattern**: Access through unified interfaces
**Example**: UI data collection, serialization operations

```python
# Interface-based access
ui_data = editor.data_interface.collect_data_from_ui(editor, "piece")
editor.data_interface.populate_ui_from_data(editor, data, "piece")

# Interface provides consistent API
class EditorDataInterface:
    def collect_data_from_ui(self, editor, data_type):
        # Standardized data collection logic
        pass
    
    def populate_ui_from_data(self, editor, data, data_type):
        # Standardized UI population logic
        pass
```

**Characteristics**:
- Consistent API across components
- Standardized behavior
- Easy to test and mock
- Supports polymorphism

## Ownership Hierarchy

### Level 1: Application Data
- **Owner**: Main application or global managers
- **Scope**: Application-wide settings, global state
- **Access**: Through singleton managers or global interfaces

### Level 2: Editor Data
- **Owner**: Specific editor instances (PieceEditor, AbilityEditor)
- **Scope**: Editor-specific configuration and state
- **Access**: Direct property access or editor methods

### Level 3: Component Data
- **Owner**: Specialized components within editors
- **Scope**: Component-specific functionality
- **Access**: Through component interfaces or managers

### Level 4: UI Data
- **Owner**: Individual UI widgets or widget groups
- **Scope**: Widget state and user input
- **Access**: Through data collection interfaces

## Legacy Compatibility Strategy

### Phase 1: Identify Legacy Fields
```python
# Legacy field usage
old_pattern = editor.current_custom_pattern
old_type = editor.selected_movement_type
old_pos = editor.custom_pattern_piece_pos
```

### Phase 2: Create Property Redirections
```python
# Property redirections to canonical source
@property
def current_custom_pattern(self):
    return self.current_movement_data.get("pattern")

@property
def selected_movement_type(self):
    return self.current_movement_data.get("type")

@property
def custom_pattern_piece_pos(self):
    return self.current_movement_data.get("piecePosition")
```

### Phase 3: Update Internal Usage
```python
# Internal code uses canonical source
def update_movement(self, movement_type):
    self.current_movement_data["type"] = movement_type
    self.current_movement_data["pattern"] = self.generate_pattern(movement_type)
```

### Phase 4: Deprecation and Migration
```python
# Add deprecation warnings
@property
def current_custom_pattern(self):
    logger.warning("current_custom_pattern is deprecated, use current_movement_data['pattern']")
    return self.current_movement_data.get("pattern")
```

## Best Practices

### For New Data Types
1. **Register Ownership**: Add rule to DataOwnershipRegistry
2. **Choose Pattern**: Select appropriate access pattern
3. **Document Clearly**: Explain ownership and access methods
4. **Validate Access**: Ensure access follows established patterns

### For Existing Data
1. **Audit Current Usage**: Find all access points
2. **Identify Canonical Source**: Determine single source of truth
3. **Create Redirections**: Implement property-based compatibility
4. **Update Internal Code**: Use canonical source internally

### For Complex Data
1. **Use Managers**: Create specialized manager classes
2. **Encapsulate Logic**: Keep complex operations in managers
3. **Provide Clear APIs**: Offer simple, consistent interfaces
4. **Maintain State**: Let managers handle internal state

## Common Pitfalls

### 1. Multiple Sources of Truth
**Problem**: Same data stored in multiple places
**Solution**: Consolidate to single canonical source with property redirections

### 2. Unclear Ownership
**Problem**: Confusion about which component owns data
**Solution**: Document ownership clearly in the registry

### 3. Inconsistent Access
**Problem**: Different access patterns for similar data
**Solution**: Standardize access patterns and use interfaces

### 4. Breaking Legacy Code
**Problem**: Changes break existing functionality
**Solution**: Implement property redirections for backward compatibility

## Validation and Debugging

### Ownership Validation
```python
# Check ownership compliance
ownership_info = editor.get_data_ownership_info()
if not ownership_info['is_compliant']:
    logger.warning(f"Ownership violation: {ownership_info}")
```

### Access Pattern Validation
```python
# Validate access patterns
rule = data_ownership_registry.get_ownership_rule("piece_data")
if rule.access_pattern != DataAccessPattern.DIRECT_PROPERTY:
    logger.warning("Unexpected access pattern")
```

### Data Flow Debugging
```python
# Track data changes
def data_change_listener(data_type, key, value):
    logger.debug(f"Data changed: {data_type}.{key} = {value}")

single_source_interface.add_change_listener("piece_data", data_change_listener)
```

## Related Documentation
- [Single Source of Truth Implementation](../architecture/single_source_truth.md)
- [Base Class Hierarchy](../architecture/base_classes.md)
- [Data Flow Patterns](../architecture/data_flow.md)
- [Legacy Field Mappings](../cross_references/legacy_mappings.md)
