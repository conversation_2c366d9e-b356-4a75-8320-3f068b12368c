"""
Base Editor Class for Adventure Chess
Provides standardized data management, state tracking, and file operations
"""

import logging
import os
from typing import Any, Dict, Optional, Tuple

from PyQt6.QtWidgets import Q<PERSON>ainWindow, QMessageBox

# Lazy imports to avoid circular dependencies
# from core.interfaces.editor_data_interface import EditorDataInterface
# from core.managers.data_ownership_registry import data_ownership_registry
# from utils.simple_bridge import simple_bridge

logger = logging.getLogger(__name__)


class BaseEditor(QMainWindow):
    """
    Base class for all Adventure Chess editors
    Provides standardized data management and file operations
    """

    def __init__(self, data_type: str):
        super().__init__()

        # Core data management - single source of truth
        self.data_type = data_type  # 'ability' or 'piece'
        self.current_data = {}
        self.last_saved_data = {}
        self.current_filename = None
        self.unsaved_changes = False

        # Data interface and ownership validation (lazy import)
        from core.interfaces.editor_data_interface import EditorDataInterface

        self.data_interface = EditorDataInterface()
        self._validate_data_ownership()

        # Initialize UI state
        self.init_base_state()

    def init_base_state(self):
        """Initialize base editor state"""
        self.setWindowTitle(f"Adventure Chess - {self.data_type.title()} Editor")

        # Connect change tracking to common widgets (override in subclasses for specific widgets)
        self.setup_change_tracking()

    def setup_change_tracking(self):
        """Setup automatic change tracking for common widgets (override in subclasses)"""

    # ========== STANDARDIZED DATA OPERATIONS ==========

    def collect_widget_data(self) -> Dict[str, Any]:
        """
        Standardized data collection from UI widgets
        Uses EditorDataInterface for consistent field mapping
        """
        return self.data_interface.collect_data_from_ui(self, self.data_type)

    def set_widget_values_from_data(self, data: Dict[str, Any]):
        """
        Standardized widget population from data
        Uses EditorDataInterface for consistent field mapping
        """
        self.data_interface.populate_ui_from_data(self, data, self.data_type)
        self.current_data = data.copy()

    def mark_unsaved_changes(self):
        """Mark that there are unsaved changes"""
        self.data_interface.track_changes(self, True)

    def clear_unsaved_changes(self):
        """Clear the unsaved changes flag"""
        self.data_interface.track_changes(self, False)

    def _validate_data_ownership(self):
        """Validate that this editor follows data ownership patterns"""
        from core.managers.data_ownership_registry import data_ownership_registry

        data_type_key = f"{self.data_type}_data"
        rule = data_ownership_registry.get_ownership_rule(data_type_key)

        if rule:
            expected_owner = rule.owner_component
            actual_owner = self.__class__.__name__

            if expected_owner != actual_owner:
                logger.warning(
                    f"Data ownership mismatch: {data_type_key} should be owned by "
                    f"{expected_owner}, but is in {actual_owner}"
                )
        else:
            logger.debug(f"No ownership rule found for {data_type_key}")

    def get_data_ownership_info(self) -> Dict[str, Any]:
        """Get data ownership information for this editor"""
        from core.managers.data_ownership_registry import data_ownership_registry

        data_type_key = f"{self.data_type}_data"
        rule = data_ownership_registry.get_ownership_rule(data_type_key)

        if rule:
            return {
                "data_type": data_type_key,
                "owner": f"{rule.owner_component}.{rule.owner_property}",
                "access_pattern": rule.access_pattern.value,
                "legacy_aliases": rule.legacy_aliases,
                "is_compliant": rule.owner_component == self.__class__.__name__,
            }

        return {"data_type": data_type_key, "owner": "Unknown", "is_compliant": False}

    # ========== STANDARDIZED FILE OPERATIONS ==========

    def new_data(self):
        """Create new data (ability/piece)"""
        if self.unsaved_changes:
            reply = QMessageBox.question(
                self,
                "Unsaved Changes",
                f"You have unsaved changes. Do you want to save before creating a new {self.data_type}?",
                QMessageBox.StandardButton.Save
                | QMessageBox.StandardButton.Discard
                | QMessageBox.StandardButton.Cancel,
            )

            if reply == QMessageBox.StandardButton.Save:
                if not self.save_data():
                    return  # Save failed or cancelled
            elif reply == QMessageBox.StandardButton.Cancel:
                return

        # Reset to default state
        self.reset_form()
        self.current_filename = None
        self.clear_unsaved_changes()

        # For piece editor, also refresh the quick load dropdown to reset selection
        if hasattr(self, "refresh_quick_load_dropdown"):
            self.refresh_quick_load_dropdown()

        logger.info(f"New {self.data_type} created")

    def load_data(self, filename: str) -> bool:
        """
        Load data from file using standardized Pydantic bridge

        Args:
            filename: Base filename without extension

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use appropriate bridge method based on data type (lazy import)
            from utils.simple_bridge import simple_bridge

            if self.data_type == "ability":
                data, error = simple_bridge.load_ability_for_ui(filename)
            elif self.data_type == "piece":
                data, error = simple_bridge.load_piece_for_ui(filename)
            else:
                raise ValueError(f"Unknown data type: {self.data_type}")

            if error:
                QMessageBox.critical(
                    self, "Error", f"Failed to load {self.data_type}:\n{error}"
                )
                return False

            # Populate UI with loaded data
            self.set_widget_values_from_data(data)
            self.current_filename = filename
            self.clear_unsaved_changes()

            # Update UI components (override in subclasses)
            self.post_load_update()

            logger.info(f"Loaded {self.data_type}: {filename}")
            return True

        except Exception as e:
            QMessageBox.critical(
                self, "Error", f"Failed to load {self.data_type}: {str(e)}"
            )
            logger.error(f"Error loading {self.data_type} {filename}: {e}")
            return False

    def save_data(self, filename: Optional[str] = None) -> bool:
        """
        Save data using standardized Pydantic bridge

        Args:
            filename: Optional filename override

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use current filename if none provided
            if filename is None:
                if not self.current_filename:
                    return self.save_as_data()
                filename = self.current_filename

            # Extract base filename
            base_filename = os.path.splitext(os.path.basename(filename))[0]

            # Use appropriate bridge method based on data type (lazy import)
            from utils.simple_bridge import simple_bridge

            if self.data_type == "ability":
                success, error = simple_bridge.save_ability_from_ui(self, base_filename)
            elif self.data_type == "piece":
                success, error = simple_bridge.save_piece_from_ui(self, base_filename)
            else:
                raise ValueError(f"Unknown data type: {self.data_type}")

            if success:
                self.current_filename = filename
                self.clear_unsaved_changes()

                # Update UI components (override in subclasses)
                self.post_save_update()

                # Get name for display
                data = self.collect_widget_data()
                name = data.get("name", "Unknown")

                QMessageBox.information(
                    self,
                    "Success",
                    f"{self.data_type.title()} '{name}' saved successfully.",
                )
                logger.info(f"Saved {self.data_type}: {name}")
                return True
            else:
                # Handle validation errors
                if "validation" in error.lower():
                    reply = QMessageBox.question(
                        self,
                        "Validation Errors",
                        f"The {self.data_type} has validation errors:\n\n{error}\n\nWould you like to review and fix these issues?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    )
                    if reply == QMessageBox.StandardButton.Yes:
                        return False  # Let user fix issues

                QMessageBox.critical(
                    self, "Error", f"Failed to save {self.data_type}:\n{error}"
                )
                logger.error(f"Error saving {self.data_type}: {error}")
                return False

        except Exception as e:
            QMessageBox.critical(
                self, "Error", f"Failed to save {self.data_type}: {str(e)}"
            )
            logger.error(f"Error saving {self.data_type}: {e}")
            return False

    def save_as_data(self) -> bool:
        """Save data with new filename (override in subclasses for file dialog)"""
        # This should be overridden in subclasses to show appropriate file dialog
        raise NotImplementedError("save_as_data must be implemented in subclasses")

    def validate_data(self) -> Tuple[bool, list]:
        """
        Validate current data - PERMISSIVE MODE for maximum flexibility

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        # Skip validation - allow saving any data for maximum flexibility
        return True, []

    # ========== ABSTRACT METHODS (OVERRIDE IN SUBCLASSES) ==========

    def reset_form(self):
        """Reset form to default values (override in subclasses)"""
        raise NotImplementedError("reset_form must be implemented in subclasses")

    def post_load_update(self):
        """Update UI components after loading data (override in subclasses)"""

    def post_save_update(self):
        """Update UI components after saving data (override in subclasses)"""

    def refresh_file_lists(self):
        """Refresh file lists/dropdowns (override in subclasses)"""

    # ========== UTILITY METHODS ==========

    def get_data_name(self) -> str:
        """Get the name of the current data"""
        data = self.collect_widget_data()
        return data.get("name", "Unnamed")

    def has_unsaved_changes(self) -> bool:
        """Check if there are unsaved changes"""
        return self.unsaved_changes

    def closeEvent(self, a0):
        """Handle window close event"""
        if self.unsaved_changes:
            reply = QMessageBox.question(
                self,
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before closing?",
                QMessageBox.StandardButton.Save
                | QMessageBox.StandardButton.Discard
                | QMessageBox.StandardButton.Cancel,
            )

            if reply == QMessageBox.StandardButton.Save:
                if not self.save_data():
                    a0.ignore()
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                a0.ignore()
                return

        a0.accept()

    # ========== CHANGE TRACKING HELPERS ==========

    def connect_widget_changes(self, widget, signal_name: str = None):
        """Connect widget change signals to mark_unsaved_changes"""
        try:
            if signal_name:
                signal = getattr(widget, signal_name)
            else:
                # Auto-detect common signals
                if hasattr(widget, "textChanged"):
                    signal = widget.textChanged
                elif hasattr(widget, "valueChanged"):
                    signal = widget.valueChanged
                elif hasattr(widget, "currentTextChanged"):
                    signal = widget.currentTextChanged
                elif hasattr(widget, "toggled"):
                    signal = widget.toggled
                else:
                    logger.warning(f"Could not auto-detect signal for widget: {widget}")
                    return

            signal.connect(self.mark_unsaved_changes)

        except Exception as e:
            logger.warning(f"Could not connect change tracking for widget: {e}")
