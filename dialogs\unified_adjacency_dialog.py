#!/usr/bin/env python3
"""
Unified Adjacency Dialog for Adventure Chess
Combines InlinePieceSelector with custom grid pattern system for all adjacency needs
"""

from PyQt6.QtWidgets import QVBoxLayout, QDialog
from typing import Dict, Any

# Import shared UI utilities
from core.ui import InlinePieceSelector
from .base_dialog import BaseChessBoardDialog


class UnifiedAdjacencyDialog(BaseChessBoardDialog):
    """
    Unified dialog for all adjacency configuration needs
    Used for both piece editor recharge and ability editor adjacency tags
    """
    
    def __init__(self, parent=None, initial_config=None, dialog_type="adjacency_required"):
        self.dialog_type = dialog_type  # "adjacency_required" or "adjacency_recharge"

        # Determine title based on dialog type
        if dialog_type == "adjacency_recharge":
            title = "Adjacency Recharge Configuration"
        else:
            title = "Adjacency Required Configuration"

        # Initialize data before calling parent constructor
        self._initialize_data(initial_config)

        # Call parent constructor with standardized parameters
        super().__init__(parent, title, (800, 600), "adjacency")

        # Load configuration after UI is set up
        self.load_config()

    def _initialize_data(self, initial_config):
        """Initialize dialog data before UI setup."""
        # Initialize configuration data
        if initial_config:
            self.config = initial_config.copy()
        else:
            self.config = {
                'pieces': [],
                'pattern': [[0 for _ in range(8)] for _ in range(8)]
            }

        # Initialize adjacency selection tracking
        self.adjacency_selected = set()
        if 'pattern' in self.config and self.config['pattern']:
            # Convert 2D pattern to set of (row, col) tuples
            pattern = self.config['pattern']
            if isinstance(pattern, list) and len(pattern) > 0:
                if isinstance(pattern[0], list):
                    # 2D array format
                    for r in range(len(pattern)):
                        for c in range(len(pattern[r])):
                            if pattern[r][c]:
                                self.adjacency_selected.add((r, c))
                else:
                    # Flat list format (legacy)
                    self.adjacency_selected = set(pattern)

    def setup_controls(self):
        """Setup the control widgets on the left side."""
        # Instructions
        if self.dialog_type == "adjacency_recharge":
            instructions_text = "Configure which pieces must be adjacent for recharge to work."
        else:
            instructions_text = "Configure which pieces must be adjacent for this ability to activate."

        instructions = self.create_description_label(instructions_text)
        self.left_layout.addWidget(instructions)

        # Piece Selector Section
        pieces_group = self.create_section_group("Required Adjacent Pieces")
        pieces_layout = QVBoxLayout()

        # Use inline piece selector with costs for recharge
        allow_costs = self.dialog_type == "adjacency_recharge"
        self.piece_selector = InlinePieceSelector(
            self,
            "Adjacent Pieces",
            allow_costs=allow_costs
        )
        self.piece_selector.pieces_changed.connect(self.on_pieces_changed)
        self.piece_selector.pieces_changed.connect(self.mark_data_changed)
        pieces_layout.addWidget(self.piece_selector)

        pieces_group.setLayout(pieces_layout)
        self.left_layout.addWidget(pieces_group)

    def setup_chess_board(self):
        """Setup the chess board on the right side."""
        super().setup_chess_board()

        # Connect pattern change signals
        if hasattr(self.chess_board, 'pattern_changed'):
            self.chess_board.pattern_changed.connect(self.on_pattern_changed)
            self.chess_board.pattern_changed.connect(self.mark_data_changed)

    def validate_data(self) -> bool:
        """Validate the current dialog data."""
        return True  # Adjacency dialog always has valid data

    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the dialog."""
        return {
            'pieces': self.piece_selector.get_pieces() if hasattr(self, 'piece_selector') else [],
            'pattern': self.adjacency_selected if hasattr(self, 'adjacency_selected') else set(),
            'dialog_type': self.dialog_type
        }

    def on_pattern_changed(self):
        """Handle pattern changes from the chess board"""
        if hasattr(self, 'chess_board'):
            self.adjacency_selected = self.chess_board.get_pattern_as_array()

    def on_pieces_changed(self):
        """Handle piece selection changes"""
        # Pieces are handled by the inline piece selector
        pass


    def load_config(self):
        """Load configuration into the dialog"""
        if hasattr(self, 'piece_selector') and 'pieces' in self.config:
            self.piece_selector.set_pieces(self.config['pieces'])

        if hasattr(self, 'chess_board') and 'pattern' in self.config:
            # Convert pattern to 8x8 array format for chess board
            pattern_array = [[0 for _ in range(8)] for _ in range(8)]
            if isinstance(self.config['pattern'], list):
                # Handle both list of tuples and 8x8 array formats
                if self.config['pattern'] and isinstance(self.config['pattern'][0], (list, tuple)):
                    if len(self.config['pattern']) == 8:  # 8x8 array format
                        pattern_array = self.config['pattern']
                    else:  # List of (row, col) tuples
                        for row, col in self.config['pattern']:
                            if 0 <= row < 8 and 0 <= col < 8:
                                pattern_array[row][col] = 1
            self.chess_board.set_pattern_from_array(pattern_array)

    def get_config(self):
        """Get the current configuration"""
        pieces = self.piece_selector.get_pieces() if hasattr(self, 'piece_selector') else []
        pattern = self.chess_board.get_pattern_as_array() if hasattr(self, 'chess_board') else []
        return {
            'pieces': pieces,
            'pattern': pattern
        }


def edit_adjacency_config(parent=None, initial_config=None, dialog_type="adjacency_required"):
    """
    Convenience function to edit adjacency configuration
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
        dialog_type: "adjacency_required" or "adjacency_recharge"
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = UnifiedAdjacencyDialog(parent, initial_config, dialog_type)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_config()
    return None


# Legacy compatibility functions
def edit_adjacency_recharge_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency recharge"""
    return edit_adjacency_config(parent, initial_config, "adjacency_recharge")


def edit_adjacency_required_config(parent=None, initial_config=None):
    """Legacy compatibility for adjacency required"""
    return edit_adjacency_config(parent, initial_config, "adjacency_required")
