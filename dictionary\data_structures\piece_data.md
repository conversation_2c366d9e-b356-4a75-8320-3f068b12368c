# Piece Data Structure

## Overview

Piece data represents the complete configuration of a chess piece in Adventure Chess Creator, including its movement patterns, abilities, visual appearance, and game mechanics.

## Canonical Storage Location

**Owner**: `PieceEditor.current_data`  
**Access Pattern**: Direct Property Access  
**Legacy Aliases**: `current_piece`

## Core Data Structure

```python
piece_data = {
    # Basic Information
    "version": "1.0.0",                    # Data format version
    "name": "Knight",                      # Piece name (required)
    "description": "Moves in L-shape",     # Piece description
    
    # Game Mechanics
    "role": "Commander",                   # Piece role (Commander, Soldier, etc.)
    "canCapture": True,                    # Can capture other pieces
    "canCastle": False,                    # Can participate in castling
    "colorDirectional": False,             # Different behavior by color
    
    # Movement Configuration
    "movement": {                          # Movement data (see movement_data.md)
        "type": "knight",                  # Movement type
        "pattern": [[0,1,0], ...],        # 8x8 movement pattern
        "piecePosition": [3, 3]            # Piece position in pattern
    },
    
    # Abilities System
    "abilities": [                         # List of ability references
        {
            "name": "Charge",              # Ability name
            "cost": 2,                     # Point cost
            "description": "Rush forward"   # Ability description
        }
    ],
    
    # Points System
    "maxPoints": 3,                        # Maximum action points
    "startingPoints": 3,                   # Starting action points
    "rechargeType": "turnRecharge",        # How points recharge
    
    # Visual Configuration
    "whiteIcon": "♘",                      # White piece icon
    "blackIcon": "♞",                      # Black piece icon
    
    # Advanced Options
    "trackStartingPosition": False,        # Track if piece has moved
    "customProperties": {}                 # Custom game-specific properties
}
```

## Field Definitions

### Basic Information Fields

#### `version` (string, required)
- **Purpose**: Data format version for migration compatibility
- **Default**: "1.0.0"
- **Validation**: Must be valid semantic version
- **Example**: "1.0.0", "1.2.3"

#### `name` (string, required)
- **Purpose**: Human-readable piece name
- **Default**: ""
- **Validation**: Must not be empty, should be unique
- **Example**: "Knight", "Super Pawn", "Dragon"

#### `description` (string, optional)
- **Purpose**: Detailed description of piece behavior
- **Default**: ""
- **Validation**: No specific requirements
- **Example**: "Moves in an L-shape pattern"

### Game Mechanics Fields

#### `role` (string, required)
- **Purpose**: Defines piece's role in the game
- **Default**: "Commander"
- **Valid Values**: "Commander", "Soldier", "Support", "Special"
- **Example**: "Commander" (like King), "Soldier" (like Pawn)

#### `canCapture` (boolean, required)
- **Purpose**: Whether piece can capture enemy pieces
- **Default**: true
- **Validation**: Must be boolean
- **Example**: true (normal pieces), false (support pieces)

#### `canCastle` (boolean, optional)
- **Purpose**: Whether piece can participate in castling
- **Default**: false
- **Validation**: Must be boolean
- **Example**: true (King, Rook), false (other pieces)

#### `colorDirectional` (boolean, optional)
- **Purpose**: Whether piece behaves differently for white/black
- **Default**: false
- **Validation**: Must be boolean
- **Example**: true (Pawns), false (most pieces)

### Movement Configuration

#### `movement` (object, required)
- **Purpose**: Complete movement configuration
- **Structure**: See [Movement Data Structure](movement_data.md)
- **Validation**: Must contain valid movement data
- **Access**: Through `PieceEditor.current_movement_data`

### Abilities System

#### `abilities` (array, optional)
- **Purpose**: List of abilities this piece can use
- **Default**: []
- **Structure**: Array of ability reference objects
- **Validation**: Each ability must exist in abilities directory

**Ability Reference Structure**:
```python
{
    "name": "Ability Name",        # Must match ability file name
    "cost": 2,                     # Point cost (0-99)
    "description": "Brief desc"    # Optional description override
}
```

### Points System

#### `maxPoints` (integer, required)
- **Purpose**: Maximum action points piece can have
- **Default**: 1
- **Range**: 1-99
- **Validation**: Must be positive integer

#### `startingPoints` (integer, required)
- **Purpose**: Action points piece starts with
- **Default**: 1
- **Range**: 0 to maxPoints
- **Validation**: Must not exceed maxPoints

#### `rechargeType` (string, required)
- **Purpose**: How action points are recharged
- **Default**: "turnRecharge"
- **Valid Values**: "turnRecharge", "adjacencyRecharge", "commitRecharge"
- **Example**: "turnRecharge" (regain points each turn)

### Visual Configuration

#### `whiteIcon` (string, optional)
- **Purpose**: Unicode character or image path for white piece
- **Default**: "♔" (based on role)
- **Validation**: Should be single Unicode character or valid path
- **Example**: "♘", "🐴", "/images/knight_white.png"

#### `blackIcon` (string, optional)
- **Purpose**: Unicode character or image path for black piece
- **Default**: "♚" (based on role)
- **Validation**: Should be single Unicode character or valid path
- **Example**: "♞", "🐴", "/images/knight_black.png"

### Advanced Options

#### `trackStartingPosition` (boolean, optional)
- **Purpose**: Whether to track if piece has moved from start
- **Default**: false
- **Use Case**: Castling, pawn double-move, etc.
- **Example**: true (King, Rook for castling)

#### `customProperties` (object, optional)
- **Purpose**: Game-specific or variant-specific properties
- **Default**: {}
- **Structure**: Key-value pairs of custom data
- **Example**: {"canPromote": true, "promotionRank": 8}

## Data Validation Rules

### Required Fields
- `version`: Must be present and valid semantic version
- `name`: Must be non-empty string
- `role`: Must be valid role value
- `canCapture`: Must be boolean
- `movement`: Must be valid movement object
- `maxPoints`: Must be positive integer
- `startingPoints`: Must be non-negative integer ≤ maxPoints
- `rechargeType`: Must be valid recharge type

### Optional Fields
- All other fields have sensible defaults
- Missing optional fields are populated with defaults during loading

### Cross-Field Validation
- `startingPoints` ≤ `maxPoints`
- If `canCastle` is true, `trackStartingPosition` should be true
- Abilities in `abilities` array must exist in abilities directory

## Access Patterns

### Reading Piece Data
```python
# Direct access to current piece data
piece_name = editor.current_data.get('name')
piece_role = editor.current_data.get('role')
max_points = editor.current_data.get('maxPoints')

# Access through data handler
data = editor.data_handler.collect_data()
piece_name = data.get('name')
```

### Modifying Piece Data
```python
# Direct modification
editor.current_data['name'] = 'New Name'
editor.current_data['maxPoints'] = 5

# Through data handler
editor.data_handler.populate_data(new_piece_data)
```

### Legacy Compatibility
```python
# Legacy access (deprecated but supported)
piece_data = editor.current_piece  # Redirects to current_data
```

## Related Documentation
- [Movement Data Structure](movement_data.md)
- [Ability Data Structure](ability_data.md)
- [Data Ownership Patterns](../patterns/data_ownership.md)
- [Piece Field Definitions](../field_definitions/piece_fields.md)
