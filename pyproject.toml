[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "adventure-chess-creator"
version = "1.1.0"
description = "Adventure Chess Creator - A customizable chess variant editor"
authors = [
    {name = "Adventure Chess Team"}
]
dependencies = [
    "PyQt6",
    "pydantic>=2.11.7",
]
requires-python = ">=3.8"

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "flake8",
    "mypy",
    "pytest",
    "pytest-qt",
]

# Black code formatter configuration
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  __pycache__
  | \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.venv
  | venv
  | build
  | dist
)/
'''

# isort import sorter configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["config", "editors", "dialogs", "utils", "schemas", "core"]
known_third_party = ["PyQt6", "pydantic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# MyPy type checker configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false  # Can be enabled for stricter type checking
disallow_incomplete_defs = false  # Can be enabled for stricter type checking
check_untyped_defs = true
disallow_untyped_decorators = false  # Can be enabled for stricter type checking
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Ignore missing imports for third-party libraries without stubs
[[tool.mypy.overrides]]
module = [
    "PyQt6.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "-ra",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "asyncio: marks tests as async tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
    "setup.py",
    "dev_tools.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
