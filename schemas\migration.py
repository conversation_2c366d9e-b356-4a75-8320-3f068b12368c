"""
Migration utilities for transitioning from dictionary-based to Pydantic-based data management
Provides backward compatibility and data migration tools
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union

from .piece_schema import Piece
from .ability_schema import Ability
from .data_manager import pydantic_data_manager

logger = logging.getLogger(__name__)


class DataMigrationManager:
    """
    Handles migration from old dictionary-based system to new Pydantic models
    Provides compatibility layer during transition period
    """
    
    @staticmethod
    def migrate_piece_dict_to_model(piece_dict: Dict[str, Any]) -> Tuple[Optional[Piece], List[str]]:
        """
        Migrate a piece dictionary to a Pydantic model
        Returns: (piece_model, list_of_warnings)
        """
        warnings = []
        
        try:
            # Handle legacy field mappings and data cleanup
            normalized_dict = DataMigrationManager._normalize_piece_dict(piece_dict)
            
            # Create Pydantic model
            piece = Piece.from_legacy_dict(normalized_dict)
            
            # Check for any data that was lost in migration
            lost_fields = DataMigrationManager._check_lost_piece_fields(piece_dict, piece.to_legacy_dict())
            if lost_fields:
                warnings.append(f"Some fields were not migrated: {lost_fields}")
            
            return piece, warnings
            
        except Exception as e:
            logger.error(f"Error migrating piece dict: {e}")
            return None, [f"Migration failed: {e}"]
    
    @staticmethod
    def migrate_ability_dict_to_model(ability_dict: Dict[str, Any]) -> Tuple[Optional[Ability], List[str]]:
        """
        Migrate an ability dictionary to a Pydantic model
        Returns: (ability_model, list_of_warnings)
        """
        warnings = []
        
        try:
            # Handle legacy field mappings and data cleanup
            normalized_dict = DataMigrationManager._normalize_ability_dict(ability_dict)
            
            # Create Pydantic model
            ability = Ability.from_legacy_dict(normalized_dict)
            
            # Validate tag data
            validation_errors = ability.validate_all_tags()
            if validation_errors:
                for tag, error in validation_errors.items():
                    warnings.append(f"Tag '{tag}' validation warning: {error}")
            
            # Check for any data that was lost in migration
            lost_fields = DataMigrationManager._check_lost_ability_fields(ability_dict, ability.to_legacy_dict())
            if lost_fields:
                warnings.append(f"Some fields were not migrated: {lost_fields}")
            
            return ability, warnings
            
        except Exception as e:
            logger.error(f"Error migrating ability dict: {e}")
            return None, [f"Migration failed: {e}"]
    
    @staticmethod
    def _normalize_piece_dict(piece_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize legacy piece dictionary for migration"""
        normalized = piece_dict.copy()

        # Handle UI movement format conversion - only if movement data doesn't already exist
        if 'movement' not in normalized or not isinstance(normalized['movement'], dict):
            movement_data = {}

            # Check for UI movement type field
            if 'movementType' in normalized:
                movement_type = normalized.pop('movementType').lower()
                if movement_type == 'l-shape':
                    movement_type = 'lShape'
                movement_data['type'] = movement_type

            # Handle range fields from UI based on movement type
            movement_type = movement_data.get('type', 'orthogonal')

            if 'orthogonalRange' in normalized:
                if movement_type == 'orthogonal':
                    distance = normalized.pop('orthogonalRange')
                    movement_data['distance'] = distance
                else:
                    normalized.pop('orthogonalRange')  # Remove unused field

            if 'diagonalRange' in normalized:
                if movement_type == 'diagonal':
                    movement_data['distance'] = normalized.pop('diagonalRange')
                else:
                    normalized.pop('diagonalRange')  # Remove unused field

            if 'anyRange' in normalized:
                if movement_type == 'any':
                    movement_data['distance'] = normalized.pop('anyRange')
                else:
                    normalized.pop('anyRange')  # Remove unused field

            # Handle custom movement pattern from UI
            if 'customMovementPattern' in normalized:
                movement_data['pattern'] = normalized.pop('customMovementPattern')

            if 'customPatternPiecePosition' in normalized:
                movement_data['piece_position'] = normalized.pop('customPatternPiecePosition')

            # Set default distance if not set (only for legacy data)
            if 'distance' not in movement_data:
                movement_data['distance'] = 1

            # Set default type if not set
            if 'type' not in movement_data:
                movement_data['type'] = 'orthogonal'

            # Store the movement data (only if we built it from legacy fields)
            normalized['movement'] = movement_data
        else:
            # Movement data already exists - preserve it and just clean up legacy fields
            if 'movementType' in normalized:
                normalized.pop('movementType')
            if 'orthogonalRange' in normalized:
                normalized.pop('orthogonalRange')
            if 'diagonalRange' in normalized:
                normalized.pop('diagonalRange')
            if 'anyRange' in normalized:
                normalized.pop('anyRange')
            if 'customMovementPattern' in normalized:
                normalized.pop('customMovementPattern')
            if 'customPatternPiecePosition' in normalized:
                normalized.pop('customPatternPiecePosition')
        
        # Handle legacy recharge structure
        if 'recharge' in normalized and isinstance(normalized['recharge'], dict):
            recharge = normalized['recharge']
            if 'type' in recharge and 'rechargeType' not in normalized:
                normalized['rechargeType'] = recharge['type']
            if 'value' in recharge and 'turnPoints' not in normalized:
                normalized['turnPoints'] = recharge['value']
        
        # Ensure required fields have defaults
        if 'version' not in normalized:
            normalized['version'] = '1.0.0'
        if 'name' not in normalized:
            normalized['name'] = 'Unnamed Piece'
        if 'role' not in normalized:
            normalized['role'] = 'Commander'
        
        # Handle boolean fields
        boolean_fields = ['canCastle', 'trackStartingPosition', 'colorDirectional', 'canCapture']
        for field in boolean_fields:
            if field in normalized and not isinstance(normalized[field], bool):
                normalized[field] = bool(normalized[field])
        
        # Handle numeric fields
        numeric_fields = ['maxPoints', 'startingPoints', 'turnPoints', 'range']
        for field in numeric_fields:
            if field in normalized and not isinstance(normalized[field], (int, float)):
                try:
                    normalized[field] = int(normalized[field])
                except (ValueError, TypeError):
                    normalized[field] = 0
        
        # Handle list fields
        list_fields = ['abilities', 'promotions', 'promotions_2nd']
        for field in list_fields:
            if field not in normalized:
                normalized[field] = []
            elif not isinstance(normalized[field], list):
                normalized[field] = []
        
        return normalized
    
    @staticmethod
    def _normalize_ability_dict(ability_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize legacy ability dictionary for migration"""
        normalized = ability_dict.copy()
        
        # Ensure required fields have defaults
        if 'version' not in normalized:
            normalized['version'] = '1.0.0'
        if 'name' not in normalized:
            normalized['name'] = 'Unnamed Ability'
        if 'tags' not in normalized:
            normalized['tags'] = []
        elif not isinstance(normalized['tags'], list):
            normalized['tags'] = []
        
        # Handle numeric fields
        numeric_fields = ['cost', 'delay']
        for field in numeric_fields:
            if field in normalized and not isinstance(normalized[field], (int, float)):
                try:
                    normalized[field] = int(normalized[field])
                except (ValueError, TypeError):
                    normalized[field] = 0
        
        # Handle boolean fields
        boolean_fields = ['rangeFriendlyOnly', 'rangeEnemyOnly', 'autoCost', 'autoCalculateCost']
        for field in boolean_fields:
            if field in normalized and not isinstance(normalized[field], bool):
                normalized[field] = bool(normalized[field])
        
        # Handle activation mode
        if 'activationMode' not in normalized:
            normalized['activationMode'] = 'auto'
        elif normalized['activationMode'] not in ['auto', 'click']:
            normalized['activationMode'] = 'auto'
        
        # Validate range mask structure
        if 'rangeMask' in normalized:
            range_mask = normalized['rangeMask']
            if not DataMigrationManager._is_valid_8x8_grid(range_mask, bool):
                logger.warning("Invalid rangeMask structure, creating default")
                normalized['rangeMask'] = [[False for _ in range(8)] for _ in range(8)]
        
        # Validate piece position
        if 'piecePosition' in normalized:
            pos = normalized['piecePosition']
            if not isinstance(pos, list) or len(pos) != 2 or not all(isinstance(x, int) and 0 <= x <= 7 for x in pos):
                logger.warning("Invalid piecePosition, using default [4, 4]")
                normalized['piecePosition'] = [4, 4]
        
        return normalized
    
    @staticmethod
    def _is_valid_8x8_grid(grid: Any, expected_type: type) -> bool:
        """Check if a grid is a valid 8x8 structure with expected type"""
        if not isinstance(grid, list) or len(grid) != 8:
            return False
        
        for row in grid:
            if not isinstance(row, list) or len(row) != 8:
                return False
            for cell in row:
                if not isinstance(cell, expected_type):
                    return False
        
        return True
    
    @staticmethod
    def _check_lost_piece_fields(original: Dict[str, Any], migrated: Dict[str, Any]) -> List[str]:
        """Check for fields that were lost during piece migration"""
        lost_fields = []
        
        # Define fields that are expected to be lost/transformed
        expected_transformations = {
            'recharge', 'enableRecharge', 'range',  # Legacy fields
            'movementType', 'orthogonalRange', 'diagonalRange', 'anyRange',  # UI movement fields
            'customMovementPattern', 'customPatternPiecePosition'  # UI custom pattern fields
        }
        
        for key in original:
            if key not in migrated and key not in expected_transformations:
                lost_fields.append(key)
        
        return lost_fields
    
    @staticmethod
    def _check_lost_ability_fields(original: Dict[str, Any], migrated: Dict[str, Any]) -> List[str]:
        """Check for fields that were lost during ability migration"""
        lost_fields = []
        
        # Define fields that are expected to be lost/transformed
        expected_transformations = {
            'delay', 'autoCost', 'autoCalculateCost', 'individualCosts', '_dialogStates'
        }
        
        for key in original:
            if key not in migrated and key not in expected_transformations:
                lost_fields.append(key)
        
        return lost_fields


class CompatibilityLayer:
    """
    Provides compatibility methods for old code that expects dictionary-based data
    This allows gradual migration without breaking existing functionality
    """
    
    @staticmethod
    def piece_to_dict(piece: Union[Piece, Dict[str, Any]]) -> Dict[str, Any]:
        """Convert piece to dictionary format (handles both Pydantic and dict input)"""
        if isinstance(piece, Piece):
            return piece.to_legacy_dict()
        elif isinstance(piece, dict):
            return piece
        else:
            raise ValueError(f"Expected Piece model or dict, got {type(piece)}")
    
    @staticmethod
    def ability_to_dict(ability: Union[Ability, Dict[str, Any]]) -> Dict[str, Any]:
        """Convert ability to dictionary format (handles both Pydantic and dict input)"""
        if isinstance(ability, Ability):
            return ability.to_legacy_dict()
        elif isinstance(ability, dict):
            return ability
        else:
            raise ValueError(f"Expected Ability model or dict, got {type(ability)}")
    
    @staticmethod
    def dict_to_piece(data: Dict[str, Any]) -> Piece:
        """Convert dictionary to Piece model with migration"""
        piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(data)
        if piece is None:
            raise ValueError("Failed to migrate piece data")
        
        if warnings:
            for warning in warnings:
                logger.warning(f"Piece migration warning: {warning}")
        
        return piece
    
    @staticmethod
    def dict_to_ability(data: Dict[str, Any]) -> Ability:
        """Convert dictionary to Ability model with migration"""
        ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(data)
        if ability is None:
            raise ValueError("Failed to migrate ability data")
        
        if warnings:
            for warning in warnings:
                logger.warning(f"Ability migration warning: {warning}")
        
        return ability
    
    @staticmethod
    def load_piece_as_dict(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load piece and return as dictionary (for backward compatibility)"""
        piece, error = pydantic_data_manager.load_piece(filename)
        if piece is None:
            return None, error
        
        return piece.to_legacy_dict(), None
    
    @staticmethod
    def load_ability_as_dict(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load ability and return as dictionary (for backward compatibility)"""
        ability, error = pydantic_data_manager.load_ability(filename)
        if ability is None:
            return None, error
        
        return ability.to_legacy_dict(), None
    
    @staticmethod
    def save_piece_from_dict(data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save piece from dictionary data (with migration)"""
        try:
            # Check if data is already from a Pydantic model (has proper movement structure)
            # Updated to handle new movement structure without distance field
            if ('movement' in data and
                isinstance(data['movement'], dict) and
                'type' in data['movement'] and
                ('distance' in data['movement'] or 'pattern' in data['movement'])):
                # Data is already properly formatted, create piece directly
                piece = Piece(**data)
            else:
                # Data needs migration
                piece = CompatibilityLayer.dict_to_piece(data)

            return pydantic_data_manager.save_piece(piece, filename)
        except Exception as e:
            return False, str(e)
    
    @staticmethod
    def save_ability_from_dict(data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save ability from dictionary data (with migration)"""
        try:
            ability = CompatibilityLayer.dict_to_ability(data)
            return pydantic_data_manager.save_ability(ability, filename)
        except Exception as e:
            return False, str(e)
