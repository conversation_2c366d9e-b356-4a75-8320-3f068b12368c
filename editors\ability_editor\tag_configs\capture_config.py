"""
Capture tag configuration for ability editor.
Handles capture-based ability configurations.
"""

from PyQt6.QtWidgets import QComboBox, QCheckBox, QWidget, QSpin<PERSON><PERSON>
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class CaptureConfig(BaseTagConfig):
    """Configuration for capture tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "capture")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for capture configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting capture UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Capture target dropdown
            capture_target = QComboBox()
            capture_target.addItems([
                "Enemy",       # capture enemy pieces only
                "Friendly",    # capture friendly pieces only
                "Any",         # capture any pieces
                "Neutral"      # capture neutral pieces only
            ])
            capture_target.setToolTip("Type of pieces that can be captured")
            self.store_widget("capture_target", capture_target)
            self.connect_change_signals(capture_target)
            form_layout.addRow("Capture Target:", capture_target)
            self.log_debug("Added capture target dropdown")
            
            # Capture through pieces checkbox
            capture_through = QCheckBox("Can capture through pieces")
            capture_through.setToolTip("Whether the capture can go through other pieces")
            self.store_widget("capture_through_pieces", capture_through)
            self.connect_change_signals(capture_through)
            form_layout.addRow("", capture_through)
            self.log_debug("Added capture through pieces checkbox")

            # Must capture checkbox
            must_capture = QCheckBox("Must capture if possible")
            must_capture.setToolTip("Whether the ability must capture if a valid target exists")
            self.store_widget("capture_must_capture", must_capture)
            self.connect_change_signals(must_capture)
            form_layout.addRow("", must_capture)
            self.log_debug("Added must capture checkbox")

            # Max chain captures spinner
            max_chain_spin = QSpinBox()
            max_chain_spin.setRange(0, 20)
            max_chain_spin.setValue(0)
            max_chain_spin.setSpecialValueText("No chain captures")
            max_chain_spin.setToolTip("Maximum number of chain captures allowed (0 = no chain captures)")
            self.store_widget("capture_max_chain", max_chain_spin)
            self.connect_change_signals(max_chain_spin)
            form_layout.addRow("Max Chain Captures:", max_chain_spin)
            self.log_debug("Added max chain captures spinner")
            
            # Add the form layout to the parent
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            parent_layout.addWidget(form_widget)
            
            self.log_debug("Capture UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting capture data population with: {data}")
            
            # Populate capture target
            capture_target = self.get_widget_by_name("capture_target")
            if capture_target:
                target_value = data.get("captureTarget", "Enemy")
                self.log_debug(f"Setting capture target to: {target_value}")
                index = capture_target.findText(target_value)
                if index >= 0:
                    capture_target.setCurrentIndex(index)
            
            # Populate capture through pieces
            capture_through = self.get_widget_by_name("capture_through_pieces")
            if capture_through:
                through_value = data.get("captureThroughPieces", False)
                self.log_debug(f"Setting capture through pieces to: {through_value}")
                capture_through.setChecked(through_value)

            # Populate must capture
            must_capture = self.get_widget_by_name("capture_must_capture")
            if must_capture:
                must_value = data.get("captureMustCapture", False)
                self.log_debug(f"Setting must capture to: {must_value}")
                must_capture.setChecked(must_value)

            # Populate max chain captures
            max_chain_spin = self.get_widget_by_name("capture_max_chain")
            if max_chain_spin:
                max_chain_value = data.get("captureMaxChain", 0)
                self.log_debug(f"Setting max chain captures to: {max_chain_value}")
                max_chain_spin.setValue(max_chain_value)
            
            self.log_debug("Capture data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the capture configuration data
        """
        try:
            data = {}
            
            # Collect capture target
            capture_target = self.get_widget_by_name("capture_target")
            if capture_target:
                data["captureTarget"] = capture_target.currentText()
            
            # Collect capture through pieces
            capture_through = self.get_widget_by_name("capture_through_pieces")
            if capture_through:
                data["captureThroughPieces"] = capture_through.isChecked()

            # Collect must capture
            must_capture = self.get_widget_by_name("capture_must_capture")
            if must_capture:
                data["captureMustCapture"] = must_capture.isChecked()

            # Collect max chain captures
            max_chain_spin = self.get_widget_by_name("capture_max_chain")
            if max_chain_spin:
                data["captureMaxChain"] = max_chain_spin.value()
            
            self.log_debug(f"Collected capture data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
