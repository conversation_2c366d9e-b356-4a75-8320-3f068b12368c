#!/usr/bin/env python3
"""
Centralized Theme Manager for Adventure Chess Creator

This module provides a comprehensive theming system to ensure consistent
visual styling across all components of the application. It includes:

- Standardized color palettes
- Reusable widget styles
- Theme utilities and helpers
- Dark/light theme support
- Responsive design considerations

Usage:
    from core.ui.theme_manager import ThemeManager, apply_theme_to_widget
    
    # Apply theme to widget
    apply_theme_to_widget(my_button, 'primary_button')
    
    # Get themed stylesheet
    style = ThemeManager.get_style('secondary_button')
    my_widget.setStyleSheet(style)
    
    # Apply theme to entire window
    ThemeManager.apply_window_theme(my_window)
"""

from typing import Dict, Optional, Union
from enum import Enum
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QObject, pyqtSignal


class ThemeVariant(Enum):
    """Available theme variants"""
    DARK = "dark"
    LIGHT = "light"
    AUTO = "auto"


class ThemeManager(QObject):
    """
    Centralized theme management system for Adventure Chess Creator
    
    Provides consistent styling across all application components with
    support for multiple theme variants and responsive design.
    """
    
    # Theme change signal
    theme_changed = pyqtSignal(str)  # Emits theme variant name
    
    # Current theme
    _current_theme = ThemeVariant.DARK
    
    # Color Palettes
    COLORS = {
        'dark': {
            # Primary colors
            'primary': '#4a90e2',
            'primary_light': '#66aaff',
            'primary_dark': '#2c5aa0',
            'primary_hover': '#5599dd',
            'primary_pressed': '#2255aa',
            
            # Secondary colors
            'secondary': '#6a1b9a',
            'secondary_light': '#9c27b0',
            'secondary_dark': '#4a148c',
            'secondary_hover': '#7e37bf',
            'secondary_pressed': '#3d1078',
            
            # Success/Action colors
            'success': '#4caf50',
            'success_light': '#6fbf73',
            'success_dark': '#2e7d32',
            'success_hover': '#5cbf60',
            'success_pressed': '#1b5e20',
            
            # Warning colors
            'warning': '#ff9800',
            'warning_light': '#ffb74d',
            'warning_dark': '#ef6c00',
            'warning_hover': '#ffa726',
            'warning_pressed': '#e65100',
            
            # Error/Danger colors
            'danger': '#f44336',
            'danger_light': '#ef5350',
            'danger_dark': '#c62828',
            'danger_hover': '#e57373',
            'danger_pressed': '#b71c1c',
            
            # Surface colors
            'surface_primary': '#2d3748',
            'surface_secondary': '#4a5568',
            'surface_tertiary': '#718096',
            'surface_hover': '#3a4a5c',
            'surface_pressed': '#1a202c',
            
            # Background colors
            'background_primary': '#1a202c',
            'background_secondary': '#2d3748',
            'background_tertiary': '#4a5568',
            'background_overlay': 'rgba(0, 0, 0, 0.7)',
            
            # Text colors
            'text_primary': '#ffffff',
            'text_secondary': '#e2e8f0',
            'text_tertiary': '#a0aec0',
            'text_muted': '#718096',
            'text_disabled': '#4a5568',
            
            # Border colors
            'border_primary': '#4a5568',
            'border_secondary': '#718096',
            'border_focus': '#66aaff',
            'border_error': '#f44336',
            'border_success': '#4caf50',
            
            # Chess board colors
            'chess_light': '#f0d9b5',
            'chess_dark': '#b58863',
            'chess_border': '#8b4513',
            'chess_frame': '#2d1810',
            
            # Special state colors
            'selected': '#4a90e2',
            'highlighted': '#ffd700',
            'disabled_overlay': 'rgba(255, 255, 255, 0.1)',
        },
        
        'light': {
            # Primary colors
            'primary': '#1976d2',
            'primary_light': '#42a5f5',
            'primary_dark': '#0d47a1',
            'primary_hover': '#1e88e5',
            'primary_pressed': '#0b3c87',
            
            # Secondary colors
            'secondary': '#7b1fa2',
            'secondary_light': '#ab47bc',
            'secondary_dark': '#4a148c',
            'secondary_hover': '#8e24aa',
            'secondary_pressed': '#3d1078',
            
            # Success/Action colors
            'success': '#388e3c',
            'success_light': '#66bb6a',
            'success_dark': '#1b5e20',
            'success_hover': '#4caf50',
            'success_pressed': '#2e7d32',
            
            # Warning colors
            'warning': '#f57c00',
            'warning_light': '#ffb74d',
            'warning_dark': '#e65100',
            'warning_hover': '#ff9800',
            'warning_pressed': '#ef6c00',
            
            # Error/Danger colors
            'danger': '#d32f2f',
            'danger_light': '#ef5350',
            'danger_dark': '#b71c1c',
            'danger_hover': '#f44336',
            'danger_pressed': '#c62828',
            
            # Surface colors
            'surface_primary': '#ffffff',
            'surface_secondary': '#f5f5f5',
            'surface_tertiary': '#eeeeee',
            'surface_hover': '#fafafa',
            'surface_pressed': '#e0e0e0',
            
            # Background colors
            'background_primary': '#fafafa',
            'background_secondary': '#ffffff',
            'background_tertiary': '#f5f5f5',
            'background_overlay': 'rgba(0, 0, 0, 0.5)',
            
            # Text colors
            'text_primary': '#212121',
            'text_secondary': '#424242',
            'text_tertiary': '#616161',
            'text_muted': '#9e9e9e',
            'text_disabled': '#bdbdbd',
            
            # Border colors
            'border_primary': '#e0e0e0',
            'border_secondary': '#bdbdbd',
            'border_focus': '#1976d2',
            'border_error': '#d32f2f',
            'border_success': '#388e3c',
            
            # Chess board colors (same as dark theme for consistency)
            'chess_light': '#f0d9b5',
            'chess_dark': '#b58863',
            'chess_border': '#8b4513',
            'chess_frame': '#2d1810',
            
            # Special state colors
            'selected': '#1976d2',
            'highlighted': '#ff8f00',
            'disabled_overlay': 'rgba(0, 0, 0, 0.1)',
        }
    }
    
    # Typography
    TYPOGRAPHY = {
        'font_family_primary': 'Segoe UI, Arial, sans-serif',
        'font_family_mono': 'Consolas, Monaco, monospace',
        
        'font_size_xs': '10px',
        'font_size_sm': '12px',
        'font_size_base': '14px',
        'font_size_lg': '16px',
        'font_size_xl': '18px',
        'font_size_2xl': '20px',
        'font_size_3xl': '24px',
        
        'font_weight_normal': 'normal',
        'font_weight_medium': '500',
        'font_weight_bold': 'bold',
        
        'line_height_tight': '1.2',
        'line_height_normal': '1.4',
        'line_height_relaxed': '1.6',
    }
    
    # Spacing and sizing
    SPACING = {
        'xs': '2px',
        'sm': '4px',
        'base': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '20px',
        '2xl': '24px',
        '3xl': '32px',
        '4xl': '40px',
    }
    
    # Border radius
    RADIUS = {
        'none': '0px',
        'sm': '2px',
        'base': '4px',
        'md': '6px',
        'lg': '8px',
        'xl': '12px',
        'full': '50%',
    }
    
    # Shadows
    SHADOWS = {
        'sm': '0 1px 2px rgba(0, 0, 0, 0.1)',
        'base': '0 1px 3px rgba(0, 0, 0, 0.2)',
        'md': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'lg': '0 10px 15px rgba(0, 0, 0, 0.1)',
        'xl': '0 20px 25px rgba(0, 0, 0, 0.1)',
    }
    
    @classmethod
    def get_current_theme(cls) -> ThemeVariant:
        """Get the current theme variant"""
        return cls._current_theme
    
    @classmethod
    def set_theme(cls, theme: Union[ThemeVariant, str]) -> None:
        """Set the current theme variant"""
        if isinstance(theme, str):
            theme = ThemeVariant(theme)
        
        if theme != cls._current_theme:
            cls._current_theme = theme
            # In a real implementation, you might emit a signal here
            # cls.theme_changed.emit(theme.value)
    
    @classmethod
    def get_color(cls, color_name: str, theme: Optional[ThemeVariant] = None) -> str:
        """Get a color value from the current theme palette"""
        if theme is None:
            theme = cls._current_theme
        
        theme_colors = cls.COLORS.get(theme.value, cls.COLORS['dark'])
        return theme_colors.get(color_name, '#ffffff')
    
    @classmethod
    def get_colors(cls, theme: Optional[ThemeVariant] = None) -> Dict[str, str]:
        """Get all colors for the specified theme"""
        if theme is None:
            theme = cls._current_theme
        
        return cls.COLORS.get(theme.value, cls.COLORS['dark'])
    
    @classmethod
    def get_style(cls, style_name: str, **kwargs) -> str:
        """Get a pre-defined style with optional customizations"""
        styles = cls._get_widget_styles()
        base_style = styles.get(style_name, '')
        
        # Apply any custom properties
        if kwargs:
            custom_props = []
            for key, value in kwargs.items():
                css_key = key.replace('_', '-')
                custom_props.append(f"{css_key}: {value};")
            
            if custom_props:
                # Insert custom properties before the closing brace
                custom_css = '\n    ' + '\n    '.join(custom_props)
                base_style = base_style.replace('}', custom_css + '\n}')
        
        return base_style
    
    @classmethod
    def _get_widget_styles(cls) -> Dict[str, str]:
        """Get all pre-defined widget styles"""
        colors = cls.get_colors()
        
        return {
            # Button styles
            'primary_button': f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['primary']}, stop:1 {colors['primary_dark']});
                    border: 1px solid {colors['primary_dark']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    padding: {cls.SPACING['base']} {cls.SPACING['lg']};
                    min-height: 32px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['primary_hover']}, stop:1 {colors['primary']});
                    border-color: {colors['primary_light']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['primary_pressed']}, stop:1 {colors['primary_dark']});
                    border-color: {colors['primary_dark']};
                }}
                QPushButton:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            'secondary_button': f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['surface_secondary']}, stop:1 {colors['surface_tertiary']});
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    padding: {cls.SPACING['base']} {cls.SPACING['lg']};
                    min-height: 32px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['surface_hover']}, stop:1 {colors['surface_secondary']});
                    border-color: {colors['border_secondary']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['surface_pressed']}, stop:1 {colors['surface_tertiary']});
                    border-color: {colors['border_primary']};
                }}
                QPushButton:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            'danger_button': f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['danger']}, stop:1 {colors['danger_dark']});
                    border: 1px solid {colors['danger_dark']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    padding: {cls.SPACING['base']} {cls.SPACING['lg']};
                    min-height: 32px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['danger_hover']}, stop:1 {colors['danger']});
                    border-color: {colors['danger_light']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['danger_pressed']}, stop:1 {colors['danger_dark']});
                    border-color: {colors['danger_dark']};
                }}
                QPushButton:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            'success_button': f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['success']}, stop:1 {colors['success_dark']});
                    border: 1px solid {colors['success_dark']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    padding: {cls.SPACING['base']} {cls.SPACING['lg']};
                    min-height: 32px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['success_hover']}, stop:1 {colors['success']});
                    border-color: {colors['success_light']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 {colors['success_pressed']}, stop:1 {colors['success_dark']});
                    border-color: {colors['success_dark']};
                }}
                QPushButton:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            # Input styles
            'line_edit': f"""
                QLineEdit {{
                    background: {colors['surface_primary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    padding: {cls.SPACING['base']} {cls.SPACING['md']};
                    min-height: 32px;
                }}
                QLineEdit:focus {{
                    border-color: {colors['border_focus']};
                    background: {colors['surface_hover']};
                }}
                QLineEdit:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            'text_edit': f"""
                QTextEdit {{
                    background: {colors['surface_primary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    padding: {cls.SPACING['base']};
                }}
                QTextEdit:focus {{
                    border-color: {colors['border_focus']};
                    background: {colors['surface_hover']};
                }}
                QTextEdit:disabled {{
                    background: {colors['surface_tertiary']};
                    color: {colors['text_disabled']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            'combo_box': f"""
                QComboBox {{
                    background: {colors['surface_primary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    padding: {cls.SPACING['base']} {cls.SPACING['md']};
                    min-height: 32px;
                }}
                QComboBox:focus {{
                    border-color: {colors['border_focus']};
                    background: {colors['surface_hover']};
                }}
                QComboBox::drop-down {{
                    border: none;
                    width: 20px;
                }}
                QComboBox::down-arrow {{
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid {colors['text_tertiary']};
                    margin-right: 5px;
                }}
                QComboBox QAbstractItemView {{
                    background: {colors['surface_primary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_primary']};
                    selection-background-color: {colors['primary']};
                }}
            """,
            
            # Container styles
            'group_box': f"""
                QGroupBox {{
                    background: {colors['surface_primary']};
                    border: 2px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['md']};
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    margin-top: 12px;
                    padding-top: {cls.SPACING['md']};
                }}
                QGroupBox::title {{
                    background: {colors['surface_secondary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                    color: {colors['text_secondary']};
                    padding: {cls.SPACING['sm']} {cls.SPACING['base']};
                    subcontrol-origin: margin;
                    subcontrol-position: top left;
                    left: 10px;
                }}
            """,
            
            'tab_widget': f"""
                QTabWidget::pane {{
                    background: {colors['surface_primary']};
                    border: 1px solid {colors['border_primary']};
                    border-radius: {cls.RADIUS['base']};
                }}
                QTabBar::tab {{
                    background: {colors['surface_secondary']};
                    border: 1px solid {colors['border_primary']};
                    color: {colors['text_secondary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    padding: {cls.SPACING['base']} {cls.SPACING['lg']};
                    margin-right: 2px;
                }}
                QTabBar::tab:selected {{
                    background: {colors['primary']};
                    color: {colors['text_primary']};
                    border-color: {colors['primary_dark']};
                }}
                QTabBar::tab:hover:!selected {{
                    background: {colors['surface_hover']};
                    border-color: {colors['border_secondary']};
                }}
            """,
            
            # Separator/divider
            'separator': f"""
                QFrame {{
                    background-color: {colors['border_primary']};
                    border: none;
                    margin: {cls.SPACING['base']} 0;
                }}
            """,
            
            # Labels
            'header_label': f"""
                QLabel {{
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_lg']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_bold']};
                    padding: {cls.SPACING['base']};
                }}
            """,
            
            'subheader_label': f"""
                QLabel {{
                    color: {colors['text_secondary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_medium']};
                    padding: {cls.SPACING['sm']};
                }}
            """,
            
            'body_label': f"""
                QLabel {{
                    color: {colors['text_primary']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_normal']};
                }}
            """,
            
            'muted_label': f"""
                QLabel {{
                    color: {colors['text_muted']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_sm']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_normal']};
                    font-style: italic;
                }}
            """,
            
            'error_label': f"""
                QLabel {{
                    color: {colors['danger']};
                    font-family: {cls.TYPOGRAPHY['font_family_primary']};
                    font-size: {cls.TYPOGRAPHY['font_size_base']};
                    font-weight: {cls.TYPOGRAPHY['font_weight_bold']};
                }}
            """,
            
            # Chess board specific styles
            'chess_board_frame': f"""
                QFrame {{
                    background: {colors['chess_frame']};
                    border: 3px solid {colors['chess_border']};
                    border-radius: {cls.RADIUS['md']};
                    padding: {cls.SPACING['base']};
                }}
            """,
            
            # Window styles
            'main_window': f"""
                QMainWindow {{
                    background: {colors['background_primary']};
                    color: {colors['text_primary']};
                }}
                QMainWindow::separator {{
                    background: {colors['border_primary']};
                    width: 1px;
                    height: 1px;
                }}
            """,
            
            'dialog': f"""
                QDialog {{
                    background: {colors['background_secondary']};
                    color: {colors['text_primary']};
                }}
            """,
        }
    
    @classmethod
    def apply_window_theme(cls, window: QWidget) -> None:
        """Apply the main window theme to a widget"""
        window.setStyleSheet(cls.get_style('main_window'))
    
    @classmethod
    def apply_dialog_theme(cls, dialog: QWidget) -> None:
        """Apply the dialog theme to a widget"""
        dialog.setStyleSheet(cls.get_style('dialog'))


# Convenience functions for easy theming
def apply_theme_to_widget(widget: QWidget, style_name: str, **kwargs) -> None:
    """Apply a named theme style to a widget"""
    style = ThemeManager.get_style(style_name, **kwargs)
    widget.setStyleSheet(style)


def get_themed_style(style_name: str, **kwargs) -> str:
    """Get a themed stylesheet string"""
    return ThemeManager.get_style(style_name, **kwargs)


def get_color(color_name: str) -> str:
    """Get a color from the current theme"""
    return ThemeManager.get_color(color_name)


def set_theme(theme: Union[ThemeVariant, str]) -> None:
    """Set the application theme"""
    ThemeManager.set_theme(theme)


def apply_standard_button_themes(parent_widget: QWidget) -> None:
    """
    Apply standard themes to common button types found in a widget
    
    This function looks for buttons with specific object names and applies
    appropriate themes automatically.
    """
    # Find buttons by common naming patterns
    primary_buttons = parent_widget.findChildren(QPushButton, name=lambda x: 'save' in x.lower() or 'apply' in x.lower() or 'ok' in x.lower())
    danger_buttons = parent_widget.findChildren(QPushButton, name=lambda x: 'delete' in x.lower() or 'remove' in x.lower() or 'cancel' in x.lower())
    success_buttons = parent_widget.findChildren(QPushButton, name=lambda x: 'new' in x.lower() or 'add' in x.lower() or 'create' in x.lower())
    
    # Apply themes
    for btn in primary_buttons:
        apply_theme_to_widget(btn, 'primary_button')
    
    for btn in danger_buttons:
        apply_theme_to_widget(btn, 'danger_button')
    
    for btn in success_buttons:
        apply_theme_to_widget(btn, 'success_button')


# Widget factory functions that return pre-themed widgets
def create_themed_button(text: str, style: str = 'secondary_button', **kwargs) -> 'QPushButton':
    """Create a button with a specific theme applied"""
    from PyQt6.QtWidgets import QPushButton
    
    button = QPushButton(text)
    apply_theme_to_widget(button, style, **kwargs)
    return button


def create_themed_label(text: str, style: str = 'body_label', **kwargs) -> 'QLabel':
    """Create a label with a specific theme applied"""
    from PyQt6.QtWidgets import QLabel
    
    label = QLabel(text)
    apply_theme_to_widget(label, style, **kwargs)
    return label


def create_themed_line_edit(placeholder: str = "", **kwargs) -> 'QLineEdit':
    """Create a line edit with theme applied"""
    from PyQt6.QtWidgets import QLineEdit
    
    line_edit = QLineEdit()
    if placeholder:
        line_edit.setPlaceholderText(placeholder)
    apply_theme_to_widget(line_edit, 'line_edit', **kwargs)
    return line_edit


def create_themed_combo_box(**kwargs) -> 'QComboBox':
    """Create a combo box with theme applied"""
    from PyQt6.QtWidgets import QComboBox
    
    combo_box = QComboBox()
    apply_theme_to_widget(combo_box, 'combo_box', **kwargs)
    return combo_box


def create_themed_group_box(title: str, **kwargs) -> 'QGroupBox':
    """Create a group box with theme applied"""
    from PyQt6.QtWidgets import QGroupBox
    
    group_box = QGroupBox(title)
    apply_theme_to_widget(group_box, 'group_box', **kwargs)
    return group_box
