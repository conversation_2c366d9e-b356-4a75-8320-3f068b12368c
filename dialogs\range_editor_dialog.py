#!/usr/bin/env python3
"""
Reusable Target Range Dialog for Adventure Chess
Used for selecting target squares/ranges in abilities and adjacency configurations
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QCheckBox, QSlider, QComboBox, QSpinBox, QFormLayout)
from PyQt6.QtCore import Qt

# Import base dialog class
from .base_dialog import BaseChessBoardDialog

class TargetRangeDialog(BaseChessBoardDialog):
    """
    Target range dialog for selecting target squares/ranges
    Used for abilities and adjacency configurations
    """

    # Class-level state storage for checkbox persistence
    _last_checkbox_state = {
        'starting_square_checked': False,
        'continue_off_board_checked': False
    }

    def __init__(self, initial_pattern=None, piece_position=None, title="Target Range Editor", parent=None, checkbox_states=None):
        # Initialize data before calling super().__init__
        self._initialize_data(initial_pattern, piece_position, checkbox_states)

        # Call parent constructor with standardized parameters
        super().__init__(parent, title, (800, 600), "range")

    def _initialize_data(self, initial_pattern, piece_position, checkbox_states):
        """Initialize dialog data before UI setup."""
        # Initialize pattern - convert to boolean for range editor
        if initial_pattern is None:
            self.pattern = [[False for _ in range(8)] for _ in range(8)]
        else:
            # Convert different pattern formats to boolean
            self.pattern = []
            for row in initial_pattern:
                new_row = []
                for cell in row:
                    # Handle different data types (bool, int, etc.)
                    if isinstance(cell, bool):
                        new_row.append(cell)
                    elif isinstance(cell, int):
                        new_row.append(cell > 0)  # Convert int to bool
                    else:
                        new_row.append(bool(cell))
                self.pattern.append(new_row)

        # Piece position (use provided position or default to center)
        if piece_position is not None and len(piece_position) == 2:
            self.piece_pos = piece_position[:]  # Copy the position
        else:
            self.piece_pos = [3, 3]  # Default to center

        # Initialize checkbox states from parameter or defaults
        if checkbox_states is not None:
            self.include_starting_square = checkbox_states.get('starting_square_checked', False)
            self.continue_off_board = checkbox_states.get('continue_off_board_checked', False)
        else:
            self.include_starting_square = False
            self.continue_off_board = False

    def setup_controls(self):
        """Setup the control widgets on the left side."""
        # Instructions
        instructions = self.create_description_label(
            "Click tiles to set range. Right-click to move piece position."
        )
        self.left_layout.addWidget(instructions)

        # Options section
        options_group = self.create_section_group("Options")
        options_layout = QVBoxLayout()

        self.starting_square_check = QCheckBox("Include Starting Square")
        self.starting_square_check.setToolTip("Allow targeting the piece's starting position")
        self.starting_square_check.stateChanged.connect(self.on_starting_square_changed)
        self.starting_square_check.stateChanged.connect(self.mark_data_changed)
        options_layout.addWidget(self.starting_square_check)

        self.continue_off_board_check = QCheckBox("Continue Off Board")
        self.continue_off_board_check.setToolTip("continues board pattern off edges of map")
        self.continue_off_board_check.stateChanged.connect(self.on_continue_off_board_changed)
        self.continue_off_board_check.stateChanged.connect(self.mark_data_changed)
        options_layout.addWidget(self.continue_off_board_check)

        # Set checkbox states from initialization
        self.starting_square_check.setChecked(self.include_starting_square)
        self.continue_off_board_check.setChecked(self.continue_off_board)

        options_group.setLayout(options_layout)
        self.left_layout.addWidget(options_group)

        # Range Controls section
        controls_group = self.create_section_group("Range Controls")
        controls_form = QFormLayout()

        # Range size slider
        self.range_size_slider = QSlider(Qt.Orientation.Horizontal)
        self.range_size_slider.setRange(1, 8)
        self.range_size_slider.setValue(1)
        self.range_size_slider.setToolTip("Size of the range effect (1-8)")
        self.range_size_slider.valueChanged.connect(self.on_range_size_changed)
        self.range_size_slider.valueChanged.connect(self.mark_data_changed)

        # Size display label
        self.size_label = QLabel("1")
        size_layout = QHBoxLayout()
        size_layout.addWidget(self.range_size_slider)
        size_layout.addWidget(self.size_label)
        controls_form.addRow("Range Size:", size_layout)

        # Range shape dropdown
        self.range_shape_combo = QComboBox()
        self.range_shape_combo.addItems(["Circle", "Square", "Cross", "Line", "Custom"])
        self.range_shape_combo.setToolTip("Shape of the range effect")
        self.range_shape_combo.currentTextChanged.connect(self.on_range_shape_changed)
        self.range_shape_combo.currentTextChanged.connect(self.mark_data_changed)
        controls_form.addRow("Range Shape:", self.range_shape_combo)

        controls_group.setLayout(controls_form)
        self.left_layout.addWidget(controls_group)

        # Quick Patterns section (simplified)
        patterns_group = QLabel("Quick Patterns:")
        patterns_group.setStyleSheet("font-weight: bold; margin-top: 15px;")
        left_panel.addWidget(patterns_group)

        # Simplified pattern buttons
        pattern_buttons = [
            ("♜", "rook", "Orthogonal lines"),
            ("♝", "bishop", "Diagonal lines"),
            ("♛", "queen", "All directions"),
            ("♞", "knight", "L-shaped moves"),
            ("♚", "king", "Adjacent squares")
        ]

        # Track pattern buttons for highlighting
        self.range_preset_buttons = []
        self.current_range_preset_type = None

        for symbol, preset_type, tooltip in pattern_buttons:
            btn = QPushButton(f"{symbol} {tooltip}")
            btn.setFixedHeight(30)
            btn.setToolTip(f"{tooltip}")

            # Store button reference with preset type
            self.range_preset_buttons.append((btn, preset_type))

            # Set initial styling (normal style)
            self.update_range_preset_button_style(btn, preset_type)

            btn.clicked.connect(lambda _, pt=preset_type: self.select_range_preset(pt))
            left_panel.addWidget(btn)

        # Clear button
        clear_preset_btn = QPushButton("Clear All")
        clear_preset_btn.clicked.connect(self.clear_pattern)
        left_panel.addWidget(clear_preset_btn)

        # Add stretch to push everything to top
        left_panel.addStretch()

        # Add left panel to content layout
        content_layout.addLayout(left_panel)

        # Right side: Chess Board
        self.range_preview = EnhancedChessBoardWidget(
            mode="range",
            title="",  # Remove title for cleaner look
            show_presets=False,  # We have our own preset buttons on the left
            show_controls=False,  # We have our own checkboxes on the left
            tile_size=50
        )

        # Convert boolean pattern to integer pattern for enhanced widget
        int_pattern = []
        for row in self.pattern:
            int_row = []
            for cell in row:
                int_row.append(1 if cell else 0)
            int_pattern.append(int_row)

        self.range_preview.set_pattern_from_array(int_pattern)
        self.range_preview.set_piece_position(self.piece_pos[0], self.piece_pos[1])

        # Connect signals
        self.range_preview.pattern_changed.connect(self.on_pattern_changed)
        self.range_preview.piece_position_changed.connect(self.on_piece_position_changed)

        content_layout.addWidget(self.range_preview)

        # Add content layout to main layout
        main_layout.addLayout(content_layout)


        # Buttons using shared utility
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        save_btn, cancel_btn = create_dialog_buttons("Save Range", "Cancel")
        cancel_btn.clicked.connect(self.reject)
        save_btn.clicked.connect(self.accept)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

        # Initialize the preview widget with current data
        self.update_preview_widget()

    def on_range_size_changed(self, value):
        """Handle range size slider changes"""
        self.size_label.setText(str(value))
        self.update_range_pattern()

    def on_range_shape_changed(self, shape):
        """Handle range shape dropdown changes"""
        # Enable/disable size slider for custom shapes
        self.range_size_slider.setEnabled(shape != "Custom")
        self.size_label.setEnabled(shape != "Custom")
        self.update_range_pattern()

    def update_range_pattern(self):
        """Update the range pattern based on current shape and size settings"""
        shape = self.range_shape_combo.currentText()
        size = self.range_size_slider.value()

        if shape == "Custom":
            # Don't override custom patterns
            return

        # Generate pattern based on shape and size
        pattern = [[False for _ in range(8)] for _ in range(8)]
        center_r, center_c = self.piece_pos

        if shape == "Square":
            # Square pattern: size x size area centered on piece
            half_size = size // 2
            for r in range(max(0, center_r - half_size), min(8, center_r + size - half_size)):
                for c in range(max(0, center_c - half_size), min(8, center_c + size - half_size)):
                    if r != center_r or c != center_c:  # Don't include piece position unless starting square is checked
                        pattern[r][c] = True

        elif shape == "Circle":
            # Circular pattern centered on piece
            for r in range(8):
                for c in range(8):
                    distance = max(abs(r - center_r), abs(c - center_c))
                    if distance <= size and (r != center_r or c != center_c):
                        pattern[r][c] = True

        elif shape == "Cross":
            # Cross pattern extending from piece
            for i in range(1, size + 1):
                # Horizontal
                if center_c - i >= 0:
                    pattern[center_r][center_c - i] = True
                if center_c + i < 8:
                    pattern[center_r][center_c + i] = True
                # Vertical
                if center_r - i >= 0:
                    pattern[center_r - i][center_c] = True
                if center_r + i < 8:
                    pattern[center_r + i][center_c] = True

        elif shape == "Line":
            # Line pattern in all directions
            for i in range(1, size + 1):
                # All 8 directions
                directions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
                for dr, dc in directions:
                    r, c = center_r + i*dr, center_c + i*dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        pattern[r][c] = True

        # Update the pattern
        self.pattern = pattern

        # Convert to integer pattern for the widget
        int_pattern = []
        for row in pattern:
            int_row = []
            for cell in row:
                int_row.append(1 if cell else 0)
            int_pattern.append(int_row)

        self.range_preview.set_pattern_from_array(int_pattern)
        self.update_preview_widget()

    def on_pattern_changed(self, pattern):
        """Handle pattern changes from the enhanced board widget"""
        # Convert integer pattern to boolean pattern
        bool_pattern = []
        for row in pattern:
            bool_row = []
            for cell in row:
                bool_row.append(cell > 0)
            bool_pattern.append(bool_row)
        self.pattern = bool_pattern

    def on_piece_position_changed(self, position):
        """Handle piece position changes from the enhanced board widget"""
        self.piece_pos = position

    def update_preview_widget(self):
        """Update the enhanced board widget with current data"""
        # Convert boolean pattern to integer pattern
        int_pattern = []
        for row in self.pattern:
            int_row = []
            for cell in row:
                int_row.append(1 if cell else 0)
            int_pattern.append(int_row)

        self.range_preview.set_pattern_from_array(int_pattern)
        self.range_preview.set_piece_position(self.piece_pos[0], self.piece_pos[1])

    def on_starting_square_changed(self, state):
        """Handle starting square checkbox change"""
        self.include_starting_square = state == Qt.CheckState.Checked.value

    def on_continue_off_board_changed(self, state):
        """Handle continue off board checkbox change"""
        self.continue_off_board = state == Qt.CheckState.Checked.value

    def save_checkbox_states(self):
        """Save current checkbox states for persistence (both class-level and JSON)"""
        # Update class-level state for immediate use
        RangeEditorDialog._last_checkbox_state = {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }

    def restore_checkbox_states(self):
        """Restore checkbox states from saved state"""
        state = RangeEditorDialog._last_checkbox_state
        self.starting_square_check.setChecked(state.get('starting_square_checked', False))
        self.continue_off_board_check.setChecked(state.get('continue_off_board_checked', False))

    def reset_to_defaults(self):
        """Reset checkbox states to default values"""
        self.starting_square_check.setChecked(False)
        self.continue_off_board_check.setChecked(False)
        # Update class-level state to reflect defaults
        RangeEditorDialog._last_checkbox_state = {
            'starting_square_checked': False,
            'continue_off_board_checked': False
        }

    def accept(self):
        """Override accept to save checkbox states before closing"""
        self.save_checkbox_states()
        super().accept()
    
    def clear_pattern(self):
        """Clear the range pattern"""
        self.pattern = [[False for _ in range(8)] for _ in range(8)]
        # Clear preset selection when manually clearing
        self.current_range_preset_type = None
        self.update_all_range_preset_button_styles()
        # Update enhanced board widget
        self.update_preview_widget()

    def update_range_preset_button_style(self, btn, preset_type):
        """Update the style of a single range preset button based on selection state"""
        if self.current_range_preset_type == preset_type:
            # Highlighted style for the current preset
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 3px solid #66aaff;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #5599dd, stop:1 #4477bb);
                    border-color: #77bbff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #2255aa, stop:1 #1144aa);
                    border-color: #4488cc;
                }
            """)
        else:
            # Normal style
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)

    def update_all_range_preset_button_styles(self):
        """Update all range preset button styles based on current selection"""
        for btn, preset_type in self.range_preset_buttons:
            self.update_range_preset_button_style(btn, preset_type)

    def select_range_preset(self, preset_type):
        """Select a range preset and update button highlighting"""
        # Update current selection
        self.current_range_preset_type = preset_type

        # Update all button styles
        self.update_all_range_preset_button_styles()

        # Apply the preset pattern
        self.apply_range_preset(preset_type)
    
    def set_radius_pattern(self, radius):
        """Set a circular radius pattern"""
        self.pattern = [[False for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        for r in range(8):
            for c in range(8):
                # Calculate distance from piece
                dr = abs(r - piece_r)
                dc = abs(c - piece_c)
                distance = max(dr, dc)  # Chebyshev distance (king's move distance)

                if distance <= radius and [r, c] != self.piece_pos:
                    self.pattern[r][c] = True

        self.update_preview_widget()

    def apply_range_preset(self, preset_type):
        """Apply a preset range pattern based on chess piece movement with auto continue off board"""
        # Clear current pattern
        self.pattern = [[False for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        # Auto-set continue off board based on preset type
        # King and Knight should NOT continue off board, all others should
        should_continue_off_board = preset_type not in ['king', 'knight']
        if hasattr(self, 'continue_off_board_check'):
            self.continue_off_board_check.setChecked(should_continue_off_board)
        if hasattr(self, 'continue_off_board'):
            self.continue_off_board = should_continue_off_board

        if preset_type == "rook":
            # Rook: All squares in orthogonal lines
            # Horizontal line
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = True
            # Vertical line
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = True

        elif preset_type == "bishop":
            # Bishop: All squares in diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        # Check if on diagonal
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = True

        elif preset_type == "queen":
            # Queen: Combination of rook and bishop
            # Orthogonal lines (rook)
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = True
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = True
            # Diagonal lines (bishop)
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = True

        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2),
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = True

        elif preset_type == "king":
            # King: 8 adjacent squares
            directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1),
                         (0, 1), (1, -1), (1, 0), (1, 1)]
            for dr, dc in directions:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = True

        elif preset_type == "global":
            # Global: Entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.piece_pos:
                        self.pattern[r][c] = True

        # Update enhanced board widget
        self.update_preview_widget()
    
    def get_range_pattern(self):
        """Get the range pattern as boolean array"""
        return [row[:] for row in self.pattern]  # Return deep copy

    def get_piece_position(self):
        """Get the piece position"""
        return self.piece_pos[:]  # Return copy

    def get_checkbox_states(self):
        """Get the current checkbox states"""
        return {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }
    
    def set_range_pattern(self, pattern):
        """Set the range pattern"""
        if pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            # Convert to boolean pattern
            self.pattern = []
            for row in pattern:
                new_row = []
                for cell in row:
                    if isinstance(cell, bool):
                        new_row.append(cell)
                    elif isinstance(cell, int):
                        new_row.append(cell > 0)
                    else:
                        new_row.append(bool(cell))
                self.pattern.append(new_row)
            self.update_preview_widget()

    # ========== ABSTRACT METHOD IMPLEMENTATIONS ==========

    def validate_data(self) -> bool:
        """Validate the current dialog data."""
        # Range editor data is always valid - pattern is managed by the chess board
        return True

    def collect_data(self) -> dict:
        """Collect data from the dialog."""
        return {
            'pattern': self.get_range_pattern(),
            'piece_position': self.get_piece_position(),
            'checkbox_states': self.get_checkbox_states()
        }

    def populate_data(self, data: dict) -> None:
        """Populate the dialog with data."""
        if 'pattern' in data:
            self.set_range_pattern(data['pattern'])
        if 'piece_position' in data:
            self.piece_pos = data['piece_position'][:]
        if 'checkbox_states' in data:
            states = data['checkbox_states']
            self.starting_square_check.setChecked(states.get('starting_square_checked', False))
            self.continue_off_board_check.setChecked(states.get('continue_off_board_checked', False))


# Convenience function
def edit_target_range(initial_pattern=None, piece_position=None, title="Edit Target Range", parent=None, checkbox_states=None):
    """
    Edit a target range pattern with checkbox state persistence
    Returns: (pattern, piece_position, checkbox_states) or (None, None, None) if cancelled
    """
    dialog = TargetRangeDialog(initial_pattern, piece_position, title, parent, checkbox_states)

    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_range_pattern(), dialog.get_piece_position(), dialog.get_checkbox_states()
    return None, None, None

# Legacy compatibility function
def edit_range_pattern(initial_pattern=None, piece_position=None, title="Edit Range Pattern", parent=None, checkbox_states=None):
    """Legacy compatibility function - redirects to edit_target_range"""
    return edit_target_range(initial_pattern, piece_position, title, parent, checkbox_states)